import {
  Customer,
  LoginAttempts,
  MfaRemainingAttempts,
  User,
} from '@fincloud/swagger-generator/authorization-server';
import { LoginErrorState } from '@fincloud/types/enums';
import { createAction, props } from '@ngrx/store';

import { Dictionary } from '@fincloud/types/models';

import { HttpErrorResponse } from '@angular/common/http';
import { LoginError } from '../../models/login-error';
/* ------------- Customers ------------- */

export const requestCustomerKeysSuccess = createAction(
  '[Login API] Get Customer keys Success',
  props<{
    customerKeys: string[];
    email: string;
    isDeviceConfirmed: boolean;
    stopNavigateSideEffect?: boolean;
    deviceName: string;
  }>(),
);

export const requestCustomerKeysFailure = createAction(
  '[Login API] Get Customer keys Failure',
  props<{
    customerKeys: string[];
    email: string;
    isDeviceConfirmed: boolean;
    stopNavigateSideEffect?: boolean;
    deviceName?: string;
  }>(),
);

export const requestCustomersSuccess = createAction(
  '[Login API] Get Customer data Success',
  props<{ customers: Dictionary<Customer>; email?: string }>(),
);

export const requestCustomersFailure = createAction(
  '[Login API] Get Customer data Failure',
  props<LoginError>(),
);

/* ------------- MFA ------------- */

export const requestIsMfaEnabledSuccess = createAction(
  '[Login API] Get mfa enabled Success',
  props<{ isMfaEnabled: boolean }>(),
);

export const requestIsMfaEnabledFailure = createAction(
  '[Login API] Get mfa enabled Failure',
  props<LoginError>(),
);

export const requestMfaCodeSuccess = createAction(
  '[Login API] Request mfa code Success',
);

export const requestMfaCodeFailure = createAction(
  '[Login API] Request mfa code Failure',
  props<LoginError>(),
);
export const validateMfaCodeSuccess = createAction(
  '[Login API] Validate mfa code Success',
);

export const validateMfaCodeFailure = createAction(
  '[Login API] Validate mfa code Failure',
  props<LoginError>(),
);

export const requestMfaRemainingAttemptsSuccess = createAction(
  '[Login API] Request mfa remaining attempts Success',
  props<{ mfaRemainingAttempts: MfaRemainingAttempts }>(),
);

export const requestMfaRemainingAttemptsFailure = createAction(
  '[Login API] Request mfa remaining attempts Failure',
  props<LoginError>(),
);

export const requestMfaConfirmCounterSuccess = createAction(
  '[Login API] Request mfa confirm counter Success',
  props<{ seconds: number; isMfaCounterResponseInvalid: boolean }>(),
);

export const requestMfaCounterFailure = createAction(
  '[Login API] Request mfa confirm counter Failure',
  props<LoginError>(),
);

/* ------------- Device authorization ------------- */

export const requestDeviceAuthorizationSuccess = createAction(
  '[Login API] Request device authorization Success',
  props<{ deviceName: string }>(),
);

export const requestDeviceAuthorizationFailure = createAction(
  '[Login API] Request device authorization Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const requestDeviceAuthorizationServerFailure = createAction(
  '[Login API] Request device authorization server Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const requestDeviceConfirmCounterSuccess = createAction(
  '[Login API] Request device confirm counter Success',
  props<{ seconds: number; isDeviceCounterReponseInvalid: boolean }>(),
);

export const requestDeviceConfirmCounterFailure = createAction(
  '[Login API] Request device confirm counter Failure',
  props<LoginError>(),
);

export const checkDeviceCookieSuccess = createAction(
  '[Login API] Check device cookie Success',
);

export const checkDeviceCookieFailure = createAction(
  '[Login API] Check device cookie Failure',
  props<{ email: string }>(),
);

export const invalidateDeviceCookieSuccess = createAction(
  '[Login API] Invalidate device cookie Success',
);

export const invalidateDeviceCookieFailure = createAction(
  '[Login API] Invalidate device cookie Failure',
);

/* ----------------- Users ----------------- */

export const requestUserByUsernameAndCustomerKeySuccess = createAction(
  '[Login API] Request user by username and customerKey Success',
  props<{ user: User }>(),
);

export const requestUserByUsernameAndCustomerKeyFailure = createAction(
  '[Login API] Request user by username and customerKey Failure',
  props<LoginError>(),
);

export const confirmDeviceAuthorizationSuccess = createAction(
  '[Login API] Confirm device Success',
);

export const confirmDeviceAuthorizationFailure = createAction(
  '[Login API] Confirm device Failure',
  props<{ errorCode: string; email: string }>(),
);

export const requestUserUnlockSuccess = createAction(
  '[Login API] Request user unlock via magic link Success',
);

export const requestUserUnlockFailure = createAction(
  '[Login API] Request user unlock via magic link Failure',
);

export const unlockUserSuccess = createAction(
  '[Login API] Unlock user via magic link Success',
);

export const unlockUserFailure = createAction(
  '[Login API] Unlock user via magic link Failure',
);

export const requestUserStatusFailure = createAction(
  '[Login API] Get Customer data Failure',
);

export const requestResetForgotPasswordSuccess = createAction(
  '[Login API] Reset forgot password request Success',
);

export const requestResetForgotPasswordFailure = createAction(
  '[Login API] Reset forgot password request Failure',
);

export const requestValidateForgotPasswordCodeSuccess = createAction(
  '[Login API] Request validate forgot password code Success',
  props<{ code: string }>(),
);

export const requestValidateForgotPasswordCodeFailure = createAction(
  '[Login API] Request validate forgot password code Failure',
  props<{ errorState: LoginErrorState }>(),
);

export const resetInitialPasswordSuccess = createAction(
  '[Login API] Reset initial password Success',
);

export const resetInitialPasswordFailure = createAction(
  '[Login API] Reset initial password Failure',
  props<LoginError>(),
);

export const resetForgotPasswordSuccess = createAction(
  '[Login API] Reset forgot password Success',
);

export const resetForgotPasswordFailure = createAction(
  '[Login API] Reset forgot password Failure',
  props<LoginError>(),
);

export const changePasswordSuccess = createAction(
  '[Login API] Change password Success',
);

export const changePasswordFailure = createAction(
  '[Login API] Change password Failure',
  props<LoginError>(),
);

export const requestLoginRemainingAttemptsSuccess = createAction(
  '[Login API] Request reamining login attempts Success',
  props<{ attempts: LoginAttempts }>(),
);

export const requestLoginRemainingAttemptsFailure = createAction(
  '[Login API] Request reamining login attempts Failure',
  props<LoginError>(),
);

export const requestUserLockSuccess = createAction(
  '[Login API] Request user lock Success',
);

export const requestUserLockFailure = createAction(
  '[Login API] Request user lock Failure',
  props<LoginError>(),
);

export const checkDeviceAuthorizationSuccess = createAction(
  '[Login API] Check device authorization Success',
  props<{
    email: string;
    stopNavigateSideEffect?: boolean;
    deviceName?: string;
  }>(),
);

export const checkDeviceAuthorizationFailure = createAction(
  '[Login API] Check device authorization Failure',
  props<{ email: string }>(),
);

export const clearOauth2SessionCookieSuccess = createAction(
  '[Login API] Clear Oauth2 Session Cookie Success',
);

export const clearOauth2SessionCookieFailure = createAction(
  '[Login API] Clear Oauth2 Session Cookie Failure',
);

export const sendInitialPasswordToMigratedUserSuccess = createAction(
  '[Login API] Send initial password to migrated user Success',
);

export const sendInitialPasswordToMigratedUserFailure = createAction(
  '[Login API] Send initial password to migrated user Failure',
);

export const resendInitialPasswordSuccess = createAction(
  '[Login API] Resend initial password Success',
);

export const resendInitialPasswordFailure = createAction(
  '[Login API] Resend initial password Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const authorizationFailure = createAction(
  '[Login API] Authorization Failure',
);
