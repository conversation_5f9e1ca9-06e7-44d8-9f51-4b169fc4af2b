<h1 class="page-header tw-mb-[3rem]" i18n="@@login-title">Anmelden</h1>
@if (pageData$ | async; as pageData) {
  <form
    method="POST"
    action="/api/authorization-server/login-rest"
    (submit)="submitForm($event, pageData.username)"
    class="wrapper"
  >
    <app-entity-card
      [options]="{
        name: pageData.user?.firstName,
        lastName: pageData.user?.lastName,
        email: pageData?.email,
        deviceName: pageData?.deviceName,
        isUser: true,
        showChangeButton: true,
        customerKey: pageData.selectedCustomerKey,
        userId: pageData.user?.id,
      }"
      (goBackClicked)="changeEmail()"
    ></app-entity-card>
    @if (pageData.hasAccessToManyCustomers) {
      <app-entity-card
        class="organization-card"
        [options]="{
          name: pageData.customer?.name || null,
          location: pageData.customer?.cityName || null,
          showChangeButton: true,
          isUser: false,
          imageUrl: pageData.imageUrl || './assets/svg/svgAvatarNoImage.svg',
        }"
        (goBackClicked)="goToSelectOrganization()"
      ></app-entity-card>
    }
    <div>
      <div class="password-label-wrapper">
        <label i18n="@@login.user.validation.password">Passwort</label>
        @if (!pageData?.user?.isMigrated) {
          <a
            class="password-label-wrapper__forgot-password text-decoration-none"
            i18n="@@login.user.validation.forgot.password"
            (click)="initiateResetPassword(pageData?.user?.isInitial)"
          >
            Sie haben Ihr Passwort vergessen?
          </a>
        }
      </div>

      <ui-input-addon>
        <ng-container slot="content">
          <ui-text-input
            [type]="isPasswordVisible ? 'text' : 'password'"
            [formControl]="passwordControl"
            size="large"
            [rightAddon]="true"
            [isInitiallyFocused]="true"
            bckgColor="background"
            [forceHasError]="passwordControl.invalid && passwordControl.touched"
            [blurOnEnter]="false"
            [autocomplete]="'current-password'"
          ></ui-text-input>
          <div class="errors-container">
            @if (isFormSubmitted) {
              <ui-validation-error
                [control]="passwordControl"
                i18n-errorMessage="@@validationError.requiredField"
                validationError="required"
                errorMessage="Pflichtfeld"
              ></ui-validation-error>
            }
            @if (
              pageData.showValidationInfo &&
              pageData.loginRemainingAttempts !== 1 &&
              pageData.loginRemainingAttempts !== null
            ) {
              <ui-validation-error
                [forceShowError]="true"
                i18n-errorMessage="@@userValidation.wrongPassword"
                errorMessage="Ungültiges Passwort. Sie haben {{
                  pageData.loginRemainingAttempts
                }} Versuche übrig."
              ></ui-validation-error>
            }
            @if (
              pageData.showValidationInfo &&
              pageData.loginRemainingAttempts === 1
            ) {
              <ui-validation-error
                [forceShowError]="true"
                i18n-errorMessage="@@userValidation.wrongPasswordSingular"
                errorMessage="Ungültiges Passwort. Sie haben noch 1 Versuch."
              ></ui-validation-error>
            }
          </div>
        </ng-container>

        <ng-container slot="addon-right">
          <ui-icon
            (click)="togglePasswordVisibility()"
            size="medium"
            class="clickable"
            [name]="isPasswordVisible ? 'visibility' : 'visibility_off'"
          ></ui-icon>
        </ng-container>
      </ui-input-addon>
    </div>
    <input
      type="hidden"
      name="password"
      autocomplete="current-password"
      [value]="passwordControl.value"
    />
    <ui-button
      class="continue-btn"
      type="fill"
      label="Anmelden"
      nativeType="submit"
      i18n-label="@@login-title"
    >
    </ui-button>
    @if (pageData?.user?.isMigrated) {
      <div
        class="warning-text-container"
        i18n="@@userValidation.migrated-users-warning"
      >
        Hinweis: Im Rahmen unseres Sicherheitsupgrades haben wir Ihnen ein neues
        Passwort per E-Mail zugesandt. Bitte verwenden Sie dieses neue Passwort
        um sich einzuloggen und mit der neoshare-Plattform fortzufahren.
      </div>
    }
  </form>
}
