import { Component, Inject } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import {
  DOMAIN_BASED_ENVIRONMENT,
  EnvironmentKey,
} from '@fincloud/core/config';
import { StateLibAuthTokensPageActions } from '@fincloud/state/auth-tokens';
import { StateLibLoginPageActions } from '@fincloud/state/login';
import { AuthTokensState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { LoginPageActions } from '../../+state/actions';
import { selectUserValidationPageData } from '../../+state/login.selectors';

@Component({
  selector: 'app-user-validation',
  templateUrl: './user-validation.component.html',
  styleUrls: ['./user-validation.component.scss'],
})
export class UserValidationComponent {
  passwordControl = new FormControl<string>('', Validators.required);

  isPasswordVisible = false;

  isFormSubmitted = false;

  pageData$ = this.store.select(selectUserValidationPageData);

  constructor(
    private router: Router,
    private store: Store,
    private authTokensStore: Store<AuthTokensState>,
    @Inject(DOMAIN_BASED_ENVIRONMENT)
    private environment: EnvironmentKey,
  ) {}

  togglePasswordVisibility(): void {
    this.isPasswordVisible = !this.isPasswordVisible;
  }

  changeEmail(): void {
    this.store.dispatch(StateLibLoginPageActions.resetLoginFeatureState());
    this.router.navigate(['/', 'login', 'email-authentication']);
  }

  initiateResetPassword(isInitialUser: boolean): void {
    if (isInitialUser) {
      this.store.dispatch(LoginPageActions.resendInitialPassword());
    } else {
      this.store.dispatch(LoginPageActions.requestResetForgotPassword());
    }
  }

  goToSelectOrganization(): void {
    this.store.dispatch(LoginPageActions.clearSelectedCustomerKey());
    this.router.navigate(['/', 'login', 'select-organization']);
  }

  submitForm($event: Event, username: string): void {
    this.isFormSubmitted = true;
    if (this.passwordControl.invalid) {
      $event.preventDefault();

      return;
    }

    this.store.dispatch(LoginPageActions.userSignInRequest());
    // This will be executed only in local environment because the flow is different and if we have a full page reload we are losing the cookies
    if (this.environment === EnvironmentKey.LOCAL) {
      $event.preventDefault();
      this.authTokensStore.dispatch(
        StateLibAuthTokensPageActions.obtainTokenLocal({
          password: this.passwordControl.value,
          username,
        }),
      );
    }
  }
}
