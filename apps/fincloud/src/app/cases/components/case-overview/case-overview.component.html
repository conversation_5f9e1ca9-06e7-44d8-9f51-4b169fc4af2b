@if (activeTab === 'invitations' || activeTab === 'applications-invitations') {
  <ng-template
    [appLayoutSection]="sidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL"
  >
    <app-invitation-application-filters
      [isRealEstateCorporate]="isRealEstateCorporate$ | async"
      [isInvitations]="activeTab === 'invitations'"
      (closeFilters)="onToggleFilters()"
      (filtersChange)="invitationApplicationFilters$.next($event)"
    ></app-invitation-application-filters>
  </ng-template>
}
@if (customerKey$ | async; as customerKey) {
  <div class="page-layout-center cases-overview-wrapper">
    <div class="page-content">
      <div class="cases-overview">
        @if (canSeeStatistics$ | async) {
          <div class="stats section-spacer">
            <app-statistics [stats]="casesStatistics$ | async"></app-statistics>
          </div>
        }
        @if (activeTab) {
          <div class="tabs-wrapper">
            <div class="d-flex justify-content-between align-items-center">
              <ul
                appMetaKeyOrCtrlKey
                tab="cases"
                [subTab]="selectedTabId"
                (executeOnClickChange)="changeOrOpenNewTab($event, customerKey)"
                ngbNav
                [activeId]="activeTab"
                (navChange)="onTabChanged($event)"
                class="nav-pills"
                #nav="ngbNav"
              >
                <ng-template [ngxPermissionsOnly]="permissionHtml.PERM_0001">
                  <li ngbNavItem="my-cases" class="nav-li">
                    <div>
                      <a
                        ngbNavLink
                        class="nav-link"
                        appAnchorPathBuilder
                        [anchorPathSegments]="['cases', 'my-cases']"
                        i18n="@@cases.tabNav.tab.mine"
                        >Meine</a
                      >
                    </div>
                    <ng-template ngbNavContent>
                      <app-tab-my-cases
                        [isRealEstateCorporate]="isRealEstateCorporate$ | async"
                      ></app-tab-my-cases>
                    </ng-template>
                  </li>
                </ng-template>
                <ng-template [ngxPermissionsOnly]="permissionHtml.PERM_0048">
                  <li ngbNavItem="customer-cases" class="nav-li">
                    <a
                      ngbNavLink
                      appAnchorPathBuilder
                      [anchorPathSegments]="['cases', 'customer-cases']"
                      class="nav-link"
                      >{{ participatingTabText$ | async }}</a
                    >
                    <ng-template ngbNavContent>
                      <app-tab-my-organization-cases
                        [isRealEstateCorporate]="isRealEstateCorporate$ | async"
                      ></app-tab-my-organization-cases>
                    </ng-template>
                  </li>
                </ng-template>
                @if (showApplicationsAndInvitationsTab$ | async) {
                  <li ngbNavItem="applications-invitations" class="nav-li">
                    <a
                      ngbNavLink
                      appAnchorPathBuilder
                      [anchorPathSegments]="[
                        'cases',
                        'applications-invitations',
                      ]"
                      class="nav-link"
                      i18n="
                        @@cases.businessCase.Collaboration.tab.invitationApplications"
                      >Einladungen und Bewerbungen</a
                    >
                    <ng-template ngbNavContent>
                      @if (isInvitationsApplicationsInitialized) {
                        @if (isInvitationsApplicationsFullEmptyState) {
                          <app-empty-state
                            [message]="
                              invitationsApplicationsEmptyStateMessage$ | async
                            "
                          ></app-empty-state>
                        }
                        @if (!isInvitationsApplicationsFullEmptyState) {
                          <app-applications-and-invitations-list
                            [customerKeyNames]="customerKeyNames$ | async"
                            [customer]="customer$ | async"
                            [isPlatformManager]="isPlatformManager$ | async"
                            [applicationsAndInvitationsBusinessCases]="
                              applicationsAndInvitations
                            "
                            [customerKey]="customerKey"
                            (rowSelected)="onTabChanged($event)"
                          ></app-applications-and-invitations-list>
                        }
                      }
                    </ng-template>
                  </li>
                }
                @if (showInvitationsTab$ | async) {
                  <li ngbNavItem="invitations" class="nav-li">
                    <a
                      ngbNavLink
                      appAnchorPathBuilder
                      [anchorPathSegments]="['cases', 'invitations']"
                      class="nav-link"
                      i18n="@@cases.tabNav.tab.invitations"
                      >Einladungen</a
                    >
                    <ng-template ngbNavContent>
                      @if (isInvitationsInitialized) {
                        @if (isInvitationsFullEmptyState) {
                          <app-empty-state
                            [message]="
                              invitationsApplicationsEmptyStateMessage$ | async
                            "
                          ></app-empty-state>
                        }
                        @if (!isInvitationsFullEmptyState) {
                          <app-invitations-list
                            [customerKeyNames]="customerKeyNames$ | async"
                            [customer]="customer$ | async"
                            [invitationBusinessCases]="invitationBusinessCases"
                            [customerKey]="customerKey"
                            [isRealEstateCorporate]="
                              isRealEstateCorporate$ | async
                            "
                            (rowSelected)="onTabChanged($event)"
                          ></app-invitations-list>
                        }
                      }
                    </ng-template>
                  </li>
                }
              </ul>
            </div>
            @if (
              activeTab === 'applications-invitations' &&
              isInvitationsApplicationsInitialized
            ) {
              @if (!isInvitationsApplicationsFullEmptyState) {
                <ng-container
                  *ngTemplateOutlet="invitationApplicationFiltersTemplate"
                >
                </ng-container>
              }
            }
            @if (activeTab === 'invitations' && isInvitationsInitialized) {
              @if (!isInvitationsFullEmptyState) {
                <ng-container
                  *ngTemplateOutlet="invitationApplicationFiltersTemplate"
                >
                </ng-container>
              }
            }
            <div [ngbNavOutlet]="nav" class="tab-template-content"></div>
          </div>
        }
      </div>
    </div>
  </div>
}

<ng-template #invitationApplicationFiltersTemplate>
  <div class="filters">
    <ui-search-filter
      #searchFilterComponent
      placeholder="Suche..."
      i18n-placeholder="@@cases.search.placeholder"
      color="gray"
      [hasBorder]="false"
      [focused$]="searchFilterFocused$"
      (search)="onSearchTermSelected($event)"
    ></ui-search-filter>
    <ui-button
      top-right
      (clicked)="onToggleFilters()"
      [icon]="hasFiltersApplied ? 'svgFilter' : 'filter_list'"
      corners="pointy"
      color="gray"
      class="filters-btn"
    >
    </ui-button>
  </div>
</ng-template>
