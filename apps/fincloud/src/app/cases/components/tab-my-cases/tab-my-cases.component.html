@if (isMyBusinessCasesInitialized$ | async) {
  <ng-template
    [appLayoutSection]="sidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL"
  >
    <app-case-filters
      [isRealEstateCorporate]="isRealEstateCorporate"
      (closeFilters)="onToggleFilters()"
      (filtersChange)="onCaseFiltersChange($event)"
    ></app-case-filters>
  </ng-template>
  <div class="filters" [hidden]="isFullEmptyState$ | async">
    <ui-search-filter
      #searchFilterComponent
      placeholder="Suche..."
      i18n-placeholder="@@cases.search.placeholder"
      color="gray"
      [hasBorder]="false"
      [focused$]="searchFilterFocused$"
      (search)="onSearchTermSelected($event)"
    ></ui-search-filter>
    <ui-button
      top-right
      (clicked)="onToggleFilters()"
      [icon]="hasFiltersApplied ? 'svgFilter' : 'filter_list'"
      corners="pointy"
      color="gray"
      class="filters-btn"
    >
    </ui-button>
  </div>
  @if ((isMyQueryCasesEmpty$ | async) === false) {
    <app-business-case-list
      [customerKeyNames]="customerKeyNames$ | async"
      [customerType]="customerType$ | async"
      [customerKey]="customerKey$ | async"
      [customer]="customer$ | async"
      [businessCases]="(myCases$ | async)?.results"
      [offset]="(myCases$ | async).offset"
      [limit]="(myCasesQuery$ | async)?.limit"
      [totalResults]="(myCases$ | async)?.total"
      (sortChange)="onCasesSortChange($event)"
      (pageChange)="onCasesPageChange($event)"
    ></app-business-case-list>
  }
  @if (isMyQueryCasesEmpty$ | async) {
    <app-empty-state [message]="emptyStateMessage$ | async"></app-empty-state>
  }
}
