@if (isMyOrganizationCasesInitialized$ | async) {
  <ng-template
    [appLayoutSection]="sidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL"
  >
    <app-case-filters
      [isRealEstateCorporate]="isRealEstateCorporate"
      (closeFilters)="onToggleFilters()"
      (filtersChange)="onOrganizationCaseFiltersChange($event)"
    ></app-case-filters>
  </ng-template>

  <div class="filters" [hidden]="isFullEmptyState$ | async">
    <ui-search-filter
      #searchFilterComponent
      placeholder="Suche..."
      i18n-placeholder="@@cases.search.placeholder"
      color="gray"
      [hasBorder]="false"
      [focused$]="searchFilterFocused$"
      (search)="onSearchTermSelected($event)"
    ></ui-search-filter>
    <ui-button
      top-right
      (clicked)="onToggleFilters()"
      [icon]="hasFiltersApplied ? 'svgFilter' : 'filter_list'"
      corners="pointy"
      color="gray"
      class="filters-btn"
    >
    </ui-button>
  </div>
  @if ((isMyOrganizationCasesEmpty$ | async) === false) {
    <app-business-case-list
      [customerKeyNames]="customerKeyNames$ | async"
      [businessCases]="(myOrganizationCases$ | async)?.results"
      [customerType]="customerType$ | async"
      [customerKey]="customerKey$ | async"
      [customer]="customer$ | async"
      [totalResults]="(myOrganizationCases$ | async)?.total"
      [offset]="(myOrganizationCases$ | async)?.offset"
      [limit]="(myOrganizationCasesQuery$ | async)?.limit"
      (sortChange)="onCustomerBusinessCasesSortChange($event)"
      (pageChange)="onCustomerBusinessCasesPageChange($event)"
    ></app-business-case-list>
  }
  @if (isMyOrganizationCasesEmpty$ | async) {
    <app-empty-state [message]="emptyStateMessage$ | async"></app-empty-state>
  }
}
