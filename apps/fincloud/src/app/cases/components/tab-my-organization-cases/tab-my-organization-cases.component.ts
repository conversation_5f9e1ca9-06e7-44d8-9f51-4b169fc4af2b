import { PercentPipe } from '@angular/common';
import { Component, DestroyRef, Input, OnInit, ViewChild } from '@angular/core';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Page } from '@fincloud/components/lists';
import { SearchFilterComponent } from '@fincloud/components/search-filter';
import { BusinessCaseModelService } from '@fincloud/core/business-case';
import {
  LayoutCommunicationService,
  SidebarLayoutSection,
} from '@fincloud/core/layout';
import { selectCustomer, selectCustomerType } from '@fincloud/state/customer';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { BusinessCaseType } from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { isEqual, pick } from 'lodash-es';
import { BehaviorSubject, filter, map, of, shareReplay, tap } from 'rxjs';
import {
  selectCustomerKeyNames,
  selectIsMyAllOrganizationCasesEmpty,
  selectMyOrganizationCasesData,
} from '../../+state';
import { CasesPageActions } from '../../+state/actions';
import { CaseFilters } from '../../models/case-filters';
import { CaseSort } from '../../models/case-sort';
import { StatusGroups } from '../../models/status-groups';
import { TabNames } from '../../models/tab-names';
import { DEFAULT_CASE_FILTERS } from '../../utils/default-case-filters';
import { DEFAULT_CASE_QUERY } from '../../utils/default-case-query';

@Component({
  selector: 'app-tab-my-organization-cases',
  templateUrl: './tab-my-organization-cases.component.html',
  styleUrls: ['./tab-my-organization-cases.component.scss'],
  providers: [BusinessCaseModelService, PercentPipe],
})
export class TabMyOrganizationCasesComponent implements OnInit {
  @Input() isRealEstateCorporate = false;

  @ViewChild('searchFilterComponent', { static: false })
  readonly sidebarLayoutSection = SidebarLayoutSection;
  searchFilter: SearchFilterComponent;
  activeTab: TabNames;
  selectedTabId: string;
  searchFilterFocused$ = of(true);
  searchTerm = '';
  selectedCaseTypes: BusinessCaseType[] = [];
  filtersApplied = false;
  selectedBusinessCaseStatus: StatusGroups = 'active';

  /* - - - - - - Customer   - - - - - - */

  customer$ = this.store
    .select(selectCustomer)
    .pipe(filter(Boolean), shareReplay(1));

  customerType$ = this.store.select(selectCustomerType);

  customerKey$ = this.store.select(selectUserCustomerKey);

  customerKeyNames$ = this.store.select(selectCustomerKeyNames);

  /* - - - - - - My Organization Cases initialization   - - - - - - */

  myOrganizationCases$ = this.store.select(selectMyOrganizationCasesData);

  myOrganizationCasesQuery$ = new BehaviorSubject<CaseFilters>(
    DEFAULT_CASE_QUERY,
  );
  isMyAllOrganizationCasesEmpty$ = this.store.select(
    selectIsMyAllOrganizationCasesEmpty,
  );

  isMyOrganizationCasesInitialized$ = this.myOrganizationCases$.pipe(
    map((cases) => !!cases),
  );

  /* - - - - - - Empty state   - - - - - - */

  // My business case empty is when the user has no cases
  isMyOrganizationCasesEmpty$ = this.myOrganizationCases$.pipe(
    map((cases) => !cases?.results || cases?.results.length === 0),
  );

  // Full empty state is when user has no cases without having any filters applied
  isFullEmptyState$ = this.isMyAllOrganizationCasesEmpty$.pipe(
    map(
      (isMyAllOrganizationCasesEmpty) =>
        isMyAllOrganizationCasesEmpty &&
        !this.hasFiltersApplied &&
        !this.hasAnySearchApplied,
    ),
  );

  emptyStateMessage$ = this.isFullEmptyState$.pipe(
    map((isFullEmptyState) =>
      isFullEmptyState
        ? $localize`:@@cases.bank.fullEmptyState:Ihre Organisation ist bisher an keinem Finanzierungsfall beteiligt. Sobald sie einem Fall beitritt, wird es hier erscheinen.`
        : $localize`:@@cases.filters.noResults:Keine darstellbaren Informationen vorhanden`,
    ),
  );

  /* - - - - - - Filters   - - - - - - */

  get hasFiltersApplied() {
    const emptyFilters = Object.keys(DEFAULT_CASE_FILTERS);
    const picked = pick(
      this.myOrganizationCasesQuery$.getValue(),
      emptyFilters,
    );

    return !isEqual(picked, DEFAULT_CASE_FILTERS);
  }

  get hasAnySearchApplied() {
    return !!this.myOrganizationCasesQuery$.getValue()?.searchTerm;
  }

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<AppState>,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  ngOnInit() {
    this.myOrganizationCasesQuery$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap((filters) => {
          this.loadMyOrganizationCases(filters);
        }),
      )
      .subscribe();

    this.loadMyAllOrganizationCases();
  }

  loadMyOrganizationCases(filters: CaseFilters) {
    this.store.dispatch(
      CasesPageActions.loadMyQueryOrganizationCases({ payload: filters }),
    );
  }

  loadMyAllOrganizationCases() {
    this.store.dispatch(CasesPageActions.loadMyAllOrganizationCases());
  }

  onSearchTermSelected(searchTerm: string) {
    // Reset page to 1 when search applied

    this.updateMyOrganizationCasesQuery({
      searchTerm,
      ...{
        offset: 0,
      },
    });
  }

  onToggleFilters() {
    this.layoutCommunicationService.toggleRightSideOverlayPanel();
  }

  onOrganizationCaseFiltersChange(organizationCaseFilters: CaseFilters) {
    // Reset page to 1 when any filters applied

    this.updateMyOrganizationCasesQuery({
      ...organizationCaseFilters,
      ...{
        offset: 0,
      },
    });
  }

  onCustomerBusinessCasesSortChange(sort: CaseSort) {
    const sortMap = {
      sortBy: sort.prop,
      sortOrder: sort.dir,
    };

    this.updateMyOrganizationCasesQuery(sortMap);
  }

  onCustomerBusinessCasesPageChange(page: Page) {
    this.updateMyOrganizationCasesQuery({ ...page });
  }

  private updateMyOrganizationCasesQuery(query: Partial<CaseFilters>) {
    this.myOrganizationCasesQuery$.next({
      ...this.myOrganizationCasesQuery$.getValue(),
      ...query,
    });
  }
}
