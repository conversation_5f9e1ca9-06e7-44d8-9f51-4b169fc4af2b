import { LicenseManager } from '@ag-grid-enterprise/core';
import {
  FullscreenOverlayContainer,
  OverlayContainer,
} from '@angular/cdk/overlay';
import { TextFieldModule } from '@angular/cdk/text-field';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  APP_INITIALIZER,
  ApplicationConfig,
  ErrorHandler,
  LOCALE_ID,
  importProvidersFrom,
  isDevMode,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { provideAnimations } from '@angular/platform-browser/animations';
import { Router, provideRouter } from '@angular/router';
import { AZURE_MAP_AUTH_CONFIG } from '@fincloud/components/azure-map';
import {
  ApiConfigurationService,
  BASE_HREF,
  DOMAIN_BASE_PROVIDER,
  ENVIRONMENT_TOKEN,
  POST_HOG_AUTH_CONFIG,
  PostHogService,
} from '@fincloud/core/config';
import { Instana<PERSON>rrorHand<PERSON> } from '@fincloud/core/error-handling';
import {
  DEFAULT_REGIONAL_SETTINGS,
  FIN_DATE_PICKER_INTL_DE,
  REGIONAL_SETTINGS_MAP,
  RegionalSettingsService,
} from '@fincloud/core/regional-settings';
import {
  CURRENCY_MASK_CONFIG,
  CUSTOM_MESSAGES,
  DATE_MASK_CONFIG,
  DECIMAL_MASK_CONFIG,
  MASK_CONFIG_BASE,
  MONTHS_MASK_CONFIG,
  PERCENTAGE_MASK_CONFIG,
} from '@fincloud/core/utils';
import {
  CheckIsTokenRefreshingInterceptor,
  TokenInterceptor,
} from '@fincloud/neoshare/auth';
import {
  StateLibApplicationsEffects,
  stateLibApplicationsReducer,
} from '@fincloud/state/applications';
import {
  StateLibAuthTokensEffects,
  stateLibAuthTokensReducer,
} from '@fincloud/state/auth-tokens';
import {
  StateLibContractsEffects,
  stateLibContractsReducer,
} from '@fincloud/state/contracts';
import {
  StateLibCustomerEffects,
  StateLibCustomerReducer,
} from '@fincloud/state/customer';
import { stateLibDataRoomReducer } from '@fincloud/state/data-room';
import {
  StateLibDocumentEffects,
  documentAIFillerReducer,
  documentAiTypeReducer,
  documentReducer,
} from '@fincloud/state/document';
import {
  StateLibDocumentInboxEffects,
  stateLibDocumentInboxReducer,
} from '@fincloud/state/document-inbox';
import {
  StateLibEnvironmentEffects,
  environmentReducer,
} from '@fincloud/state/environment';
import { StateLibFaqEffects } from '@fincloud/state/faq';
import {
  StateLibInvitationEffects,
  stateLibInvitationReducer,
} from '@fincloud/state/invitation';
import { StateLibLoginEffects } from '@fincloud/state/login';
import {
  clearState,
  localStorageSyncReducer,
  sessionStorageSyncReducer,
} from '@fincloud/state/metareducers';
import {
  StateLibNeogptFinancingCaseChatEffects,
  StateLibNeogtpChatEffects,
  StateLibNeogtpChatFeedbackEffects,
  StateLibNeogtpChatSettingsEffects,
  stateLibNeogtpChatReducer,
} from '@fincloud/state/neogpt-chat';

import { PAST_TIME_SUFFIX } from '@fincloud/core/date';
import { PaginatorIntlService } from '@fincloud/core/services';
import { StateLibBusinessCaseInvitationEffects } from '@fincloud/state/business-case';
import {
  StateLibNextFolderEffects,
  nextFolderFeature,
} from '@fincloud/state/next-folder';
import {
  NotificationSystemEffects,
  notificationSystemReducer,
} from '@fincloud/state/notifications';
import {
  StateLibTodosManagementEffects,
  todosManagementBadgeFeature,
} from '@fincloud/state/todos-management';
import { StateLibUserEffects, stateLibUserReducer } from '@fincloud/state/user';
import {
  StateLibUserOnboardingEffects,
  stateLibUserOnboardingReducer,
} from '@fincloud/state/user-onboarding';
import {
  StateLibUserSettingsEffects,
  stateLibUserSettingsReducer,
} from '@fincloud/state/user-settings';
import {
  StateLibUsersEffects,
  stateLibUsersReducer,
} from '@fincloud/state/users';
import { Locale } from '@fincloud/types/enums';
import {
  FIN_DATE_PICKER_INTL,
  FIN_DATE_PICKER_LOCALE,
} from '@fincloud/ui/date-picker';
import { FIN_CUSTOM_MESSAGES } from '@fincloud/ui/dropdown';
import {
  FIN_CURRENCY_MASK,
  FIN_DATE_MASK,
  FIN_DECIMAL_MASK,
  FIN_INTEGER_MASK,
  FIN_LOCALE_ID,
  FIN_MONTHS_MASK,
  FIN_PERCENTAGE_MASK,
  FIN_REGION_LOCALE_ID,
} from '@fincloud/ui/input';
import { FIN_MODAL_DEFAULT_OPTIONS } from '@fincloud/ui/modal';
import { FinPaginatorIntlService } from '@fincloud/ui/paginator';
import { provideEffects } from '@ngrx/effects';
import { provideRouterStore, routerReducer } from '@ngrx/router-store';
import { MetaReducer, provideState, provideStore } from '@ngrx/store';
import { provideStoreDevtools } from '@ngrx/store-devtools';
import * as Sentry from '@sentry/angular';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { NgxEchartsModule } from 'ngx-echarts';
import { NgxExtendedPdfViewerService } from 'ngx-extended-pdf-viewer';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { NgxSpinnerModule } from 'ngx-spinner';
import { TourNgxBootstrapModule } from 'ngx-ui-tour-ngx-bootstrap';
import { NgxWebstorageModule } from 'ngx-webstorage';
import { environment } from '../environments/environment';
import { appRoutes } from './app.routes';
import { LayoutModule } from './layout/layout.module';
import { NotificationSystemModule } from './notification-system/notification-system.module';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(appRoutes),
    provideHttpClient(withInterceptorsFromDi()),
    provideAnimations(),

    importProvidersFrom(
      TextFieldModule,
      FormsModule,
      ReactiveFormsModule,
      NgScrollbarModule,
      NotificationSystemModule,
      LayoutModule,
      NgxWebstorageModule.forRoot({
        prefix: 'neoshare',
      }),
      NgCircleProgressModule.forRoot(),
      NgxSpinnerModule,
      TourNgxBootstrapModule,
      NgxPermissionsModule.forRoot(),
      NgxEchartsModule.forRoot({
        echarts: () => import('echarts'),
      }),
    ),
    provideRouterStore(),

    provideStore(
      {
        userSettings: stateLibUserSettingsReducer,
        user: stateLibUserReducer,
        users: stateLibUsersReducer,
        customers: StateLibCustomerReducer,
        invitations: stateLibInvitationReducer,
        documentsInbox: stateLibDocumentInboxReducer,
        groupIntoView: stateLibDataRoomReducer,
        userOnboarding: stateLibUserOnboardingReducer,
        applications: stateLibApplicationsReducer,
        authTokens: stateLibAuthTokensReducer,
        contracts: stateLibContractsReducer,
        neoGptChat: stateLibNeogtpChatReducer,
        // kpis: stateLibKpiStateReducer,
        documentPreview: documentReducer,
        environment: environmentReducer,
        router: routerReducer,
        systemNotifications: notificationSystemReducer,
        document: documentAIFillerReducer,
        documentType: documentAiTypeReducer,
      },
      {
        metaReducers: [
          localStorageSyncReducer,
          sessionStorageSyncReducer,
          clearState,
        ] as Array<MetaReducer>,
      },
    ),
    provideState(todosManagementBadgeFeature),
    provideState(nextFolderFeature),
    provideStoreDevtools({
      maxAge: 25, // Retains last 25 states
      logOnly: !isDevMode(), // Restrict extension to log-only mode
      autoPause: true, // Pauses recording actions and state changes when the extension window is not open
      trace: false, //  If set to true, will include stack trace for every dispatched action, so you can see it in trace tab jumping directly to that part of code
      traceLimit: 75, // maximum stack trace frames to be stored (in case trace option was provided as true)
      connectInZone: true, // If set to true, the connection is established within the Angular zone
    }),
    provideEffects([
      StateLibUserSettingsEffects,
      StateLibCustomerEffects,
      StateLibUserEffects,
      StateLibUsersEffects,
      StateLibInvitationEffects,
      StateLibBusinessCaseInvitationEffects,
      StateLibDocumentInboxEffects,
      StateLibUserOnboardingEffects,
      StateLibLoginEffects,
      StateLibApplicationsEffects,
      StateLibAuthTokensEffects,
      StateLibContractsEffects,
      StateLibNeogtpChatEffects,
      StateLibNeogptFinancingCaseChatEffects,
      StateLibNeogtpChatSettingsEffects,
      StateLibNeogtpChatFeedbackEffects,
      NotificationSystemEffects,
      StateLibDocumentEffects,
      StateLibEnvironmentEffects,
      StateLibFaqEffects,
      StateLibTodosManagementEffects,
      StateLibNextFolderEffects,
    ]),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: CheckIsTokenRefreshingInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptor,
      multi: true,
    },
    { provide: OverlayContainer, useClass: FullscreenOverlayContainer },
    {
      provide: APP_INITIALIZER,
      useFactory: (
        apiConfigService: ApiConfigurationService,
        postHogService: PostHogService,
      ) => {
        return () => {
          apiConfigService.setRootUrls();
          postHogService.initializePostHog();
          LicenseManager.setLicenseKey(environment.agGridLicenseKey);
        };
      },
      multi: true,
      deps: [ApiConfigurationService, PostHogService, Sentry.TraceService],
    },

    {
      provide: ENVIRONMENT_TOKEN,
      useValue: environment,
    },
    {
      provide: POST_HOG_AUTH_CONFIG,
      useValue: {
        host: environment.postHogHost,
        key: environment.postHogKey,
      },
    },
    {
      provide: AZURE_MAP_AUTH_CONFIG,
      useValue: {
        clientId: environment.azureMapClientId,
        subscriptionKey: environment.azureMapSubscriptionKey,
        authType: '',
      },
    },
    {
      provide: DEFAULT_REGIONAL_SETTINGS,
      useValue: REGIONAL_SETTINGS_MAP.GERMANY,
    },
    {
      provide: FIN_LOCALE_ID,
      useFactory: (localeId: Locale) => {
        return localeId;
      },
      deps: [LOCALE_ID],
    },
    {
      provide: FIN_REGION_LOCALE_ID,
      useFactory: (regionalSettingsService: RegionalSettingsService) =>
        regionalSettingsService.regionalSettings.locale,
      deps: [RegionalSettingsService],
    },
    {
      provide: FIN_DATE_PICKER_LOCALE,
      useFactory: (localeId: Locale) =>
        localeId === Locale.DE ? FIN_DATE_PICKER_INTL_DE : FIN_DATE_PICKER_INTL,
      deps: [LOCALE_ID],
    },
    {
      provide: FIN_CURRENCY_MASK,
      useValue: CURRENCY_MASK_CONFIG,
    },
    {
      provide: FIN_PERCENTAGE_MASK,
      useValue: PERCENTAGE_MASK_CONFIG,
    },
    {
      provide: FIN_DECIMAL_MASK,
      useValue: DECIMAL_MASK_CONFIG,
    },
    {
      provide: FIN_MONTHS_MASK,
      useValue: MONTHS_MASK_CONFIG,
    },
    {
      provide: FIN_INTEGER_MASK,
      useValue: MASK_CONFIG_BASE,
    },
    {
      provide: FIN_DATE_MASK,
      useValue: DATE_MASK_CONFIG,
    },
    {
      provide: PAST_TIME_SUFFIX,
      useValue: $localize`:@@dateService.timePastSince:seit`,
    },
    {
      provide: FIN_CUSTOM_MESSAGES,
      useValue: CUSTOM_MESSAGES,
    },
    { provide: ErrorHandler, useClass: InstanaErrorHandler },
    { provide: FinPaginatorIntlService, useClass: PaginatorIntlService },
    DOMAIN_BASE_PROVIDER,
    BASE_HREF,
    // FIXME: this should not be here if the world was fair
    NgxExtendedPdfViewerService,
    {
      provide: ErrorHandler,
      useValue: Sentry.createErrorHandler({
        // FIXME get this from environment
        showDialog: false, // customize and setup crash modal https://docs.sentry.io/platforms/javascript/guides/angular/user-feedback/
      }),
    },
    {
      provide: Sentry.TraceService,
      deps: [Router],
    },
    {
      provide: FIN_CURRENCY_MASK,
      useValue: CURRENCY_MASK_CONFIG,
    },
    {
      provide: FIN_PERCENTAGE_MASK,
      useValue: PERCENTAGE_MASK_CONFIG,
    },
    {
      provide: FIN_DECIMAL_MASK,
      useValue: DECIMAL_MASK_CONFIG,
    },
    {
      provide: FIN_INTEGER_MASK,
      useValue: MASK_CONFIG_BASE,
    },
    {
      provide: FIN_CUSTOM_MESSAGES,
      useValue: CUSTOM_MESSAGES,
    },
    { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
  ],
};
