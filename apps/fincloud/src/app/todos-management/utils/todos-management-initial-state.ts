import {
  AssignmentsByStatusesCount,
  UserAssignmentGroupPositionResult,
  UserAssignmentResponse,
} from '@fincloud/swagger-generator/internal-tools';
import { TodosRouterParams } from '@fincloud/types/models';
import { TodosOrderBy } from '../enums/todos-order-by';
import { TodosSortingDirections } from '../enums/todos-sorting-directions';
import { BusinessCasePaging } from '../models/business-case-paging';
import { TodosFilters } from '../models/todos-filters';
import { TodosManagementState } from '../models/todos-management-state';

export const TODOS_MANAGEMENT_INITIAL_STATE: TodosManagementState = {
  businessCases: {} as UserAssignmentResponse,
  todos: [],
  counters: {} as AssignmentsByStatusesCount,
  filters: {
    searchPhrase: '',
    orderBy: TodosOrderBy.LAST_UPDATED_DATE,
    direction: TodosSortingDirections.DESC,
    assignmentTypes: [],
  },
  todosFilters: {
    orderBy: TodosOrderBy.LAST_UPDATED_DATE,
    direction: TodosSortingDirections.DESC,
  } as TodosFilters,
  businessCasePaging: { limit: 10, offset: 0 } as BusinessCasePaging,
  currentRouteParams: {} as TodosRouterParams,
  isLoadingBusinessCases: false,
  isLoadingTodos: false,
  todoPosition: {
    data: {} as UserAssignmentGroupPositionResult,
    isLoading: false,
  },
  companiesAvailableInOrganization: [],
  companyCases: [],
  selectedBusinessCaseId: '',
  usersWithAccessToCase: [],
  platformManagersForCustomer: [],
  availableAssignmentTypes: [],
  modalType: undefined,
};
