import { TodosStatus, TodosType } from '@fincloud/types/enums';
import { TodosOrderBy } from '../enums/todos-order-by';

export const TODOS_TABLE_CONFIG = {
  [TodosType.MY_TASKS]: {
    [TodosStatus.PENDING]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: TodosOrderBy.USER_ASSIGNMENT_TYPE,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'type',
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: TodosOrderBy.ASSIGNMENT_DUE_DATE,
        name: $localize`:@@todosManagement.table.columns.dueDate:Zu erledigen bis`,
        flexGrow: 1,
        isSortable: true,
        templateName: 'date',
      },
      {
        prop: TodosOrderBy.ASSIGNED_BY,
        name: $localize`:@@todosManagement.table.columns.assignedBy:<PERSON><PERSON><PERSON><PERSON><PERSON>,
        flexGrow: 2,
        isSortable: true,
        templateName: 'pendingAssignBy',
      },
      {
        prop: 'editManualToDo',
        name: '',
        flexGrow: 0.1,
        isSortable: false,
        templateName: 'completeManualToDoTemplate',
      },
    ],
    [TodosStatus.CLOSED]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: TodosOrderBy.USER_ASSIGNMENT_TYPE,
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: 'status',
        name: $localize`:@@todosManagement.table.columns.status:Status`,
        flexGrow: 1.5,
        isSortable: false,
        templateName: 'status',
      },
      {
        prop: TodosOrderBy.LAST_UPDATED_DATE,
        name: $localize`:@@todosManagement.table.columns.closedOn:Geschlossen am`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'closedOn',
      },
      {
        prop: 'closedBy',
        name: $localize`:@@todosManagement.table.columns.closedBy:Geschlossen von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'closedAssignBy',
      },
    ],
    [TodosStatus.COMPLETED]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: TodosOrderBy.USER_ASSIGNMENT_TYPE,
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: TodosOrderBy.LAST_UPDATED_DATE,
        name: $localize`:@@todosManagement.table.columns.completedOn:Abgeschlossen am`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'completedOn',
        headerTemplate: 'completedOn',
      },
      {
        prop: 'completedAssignBy',
        name: $localize`:@@todosManagement.table.columns.completedBy:Abgeschlossen von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'completedAssignBy',
      },
    ],
  },
  [TodosType.DELEGATED]: {
    [TodosStatus.PENDING]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: TodosOrderBy.USER_ASSIGNMENT_TYPE,
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: TodosOrderBy.ASSIGNMENT_DUE_DATE,
        name: $localize`:@@todosManagement.table.columns.dueDate:Zu erledigen bis`,
        flexGrow: 1,
        isSortable: true,
        templateName: 'date',
      },
      {
        prop: TodosOrderBy.ASSIGNED_TO,
        name: $localize`:@@todosManagement.table.columns.assignedTo:Zugewiesen an`,
        flexGrow: 2,
        isSortable: true,
        templateName: 'pendingAssignTo',
      },
      {
        prop: 'editManualToDo',
        name: '',
        flexGrow: 0.1,
        isSortable: false,
        templateName: 'actionMenuTemplate',
      },
    ],
    [TodosStatus.CLOSED]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: TodosOrderBy.USER_ASSIGNMENT_TYPE,
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: 'status',
        name: $localize`:@@todosManagement.table.columns.status:Status`,
        flexGrow: 1.5,
        isSortable: false,
        templateName: 'status',
      },
      {
        prop: TodosOrderBy.LAST_UPDATED_DATE,
        name: $localize`:@@todosManagement.table.columns.closedOn:Geschlossen am`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'closedOn',
      },
      {
        prop: 'closedBy',
        name: $localize`:@@todosManagement.table.columns.closedBy:Geschlossen von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'closedAssignBy',
      },
    ],
    [TodosStatus.COMPLETED]: [
      {
        name: $localize`:@@todosManagement.table.columns.type:Aufgabe`,
        prop: TodosOrderBy.USER_ASSIGNMENT_TYPE,
        templateName: 'type',
        isSortable: true,
        flexGrow: 1.5,
      },
      {
        prop: 'description',
        name: $localize`:@@todosManagement.table.columns.description:Beschreibung`,
        flexGrow: 3,
        isSortable: false,
        templateName: 'description',
      },
      {
        prop: TodosOrderBy.LAST_UPDATED_DATE,
        name: $localize`:@@todosManagement.table.columns.completedOn:Abgeschlossen am`,
        flexGrow: 1.5,
        isSortable: true,
        templateName: 'completedOn',
        headerTemplate: 'completedOn',
      },
      {
        prop: 'completedBy',
        name: $localize`:@@todosManagement.table.columns.completedBy:Abgeschlossen von`,
        flexGrow: 2,
        isSortable: false,
        templateName: 'completedAssignBy',
      },
    ],
  },
};
