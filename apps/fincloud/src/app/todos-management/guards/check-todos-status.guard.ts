import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router } from '@angular/router';
import { selectCustomerKey } from '@fincloud/state/customer';
import { TodosStatus } from '@fincloud/types/enums';
import { UserState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { first, map } from 'rxjs';

export const checkTodoStatusGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
) => {
  const store = inject<Store<UserState>>(Store);
  const router = inject(Router);

  return store.select(selectCustomerKey).pipe(
    first(),
    map((customerKey) => {
      const status = route.paramMap.get('todoStatus');

      const isValid = Object.values(TodosStatus).some((type) => {
        return status === type;
      });

      if (!isValid) {
        return router.createUrlTree([customerKey, 'todos-management']);
      }

      return true;
    }),
  );
};
