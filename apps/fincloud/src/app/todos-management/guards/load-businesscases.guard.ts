import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn } from '@angular/router';
import { selectRouteParams } from '@fincloud/state/router';
import { UserState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { first, map, pairwise, startWith, switchMap, tap } from 'rxjs';
import { TodosManagementPageActions } from '../+state/actions';
import { todosManagementFeature } from '../+state/reducers/todos-management.reducer';

export const loadBusinessCasesGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
) => {
  const store = inject<Store<UserState>>(Store);
  return store.select(todosManagementFeature.selectIsTodoPositionLoaded).pipe(
    first(
      ({ shouldWaitToFindTodoPosition, isTodoPositionLoaded }) =>
        (shouldWaitToFindTodoPosition && isTodoPositionLoaded) ||
        !shouldWaitToFindTodoPosition ||
        !route.paramMap.get('todoId'),
    ),
    switchMap(() => store.select(selectRouteParams).pipe(startWith(undefined))),
    pairwise(),
    tap(([prevRouteParams, currRouteParams]) => {
      if (
        currRouteParams.todoStatus !== prevRouteParams?.todoStatus ||
        currRouteParams.todoType !== prevRouteParams?.todoType
      ) {
        store.dispatch(
          TodosManagementPageActions.getAvailableAssignmentTypes(),
        );
      }
    }),
    map(() => true),
  );
};
