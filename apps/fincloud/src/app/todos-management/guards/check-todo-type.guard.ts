import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router } from '@angular/router';
import { selectCustomerKey } from '@fincloud/state/customer';
import { TodosType } from '@fincloud/types/enums';
import { UserState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { first, map } from 'rxjs';

export const checkTodoTypeGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
) => {
  const store = inject<Store<UserState>>(Store);
  const router = inject(Router);
  return store.select(selectCustomerKey).pipe(
    first((customerKey) => !!customerKey),
    map((customerKey) => {
      const isValid = Object.values(TodosType).some((type) => {
        return route.paramMap.get('todoType') === type;
      });
      if (!isValid) {
        return router.createUrlTree([customerKey, 'todos-management']);
      }

      return true;
    }),
  );
};
