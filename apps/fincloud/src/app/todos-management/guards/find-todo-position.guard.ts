import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router } from '@angular/router';
import { todoStatusMap } from '@fincloud/neoshare/todos-management';
import { TodosApiStatus, TodosStatus } from '@fincloud/types/enums';
import { Store } from '@ngrx/store';
import { filter, map, skip } from 'rxjs';
import { TodosManagementPageActions } from '../+state/actions';
import { todosManagementFeature } from '../+state/reducers/todos-management.reducer';

export const findTodoPositionGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
) => {
  const store = inject<Store>(Store);
  const router = inject(Router);

  store.dispatch(TodosManagementPageActions.findTodoPosition());

  return store
    .select(todosManagementFeature.selectTodoPositionAndCustomerKey)
    .pipe(
      skip(1),
      filter(({ todoPosition }) => !todoPosition.isLoading),
      map(({ todoPosition, customerKey }) => {
        const todoStatusParam = route.paramMap.get('todoStatus') as TodosStatus;
        const todoStatus = todoStatusMap(
          todoPosition.data.status as TodosApiStatus,
        );

        if (todoStatusParam !== todoStatus) {
          return router.createUrlTree([
            customerKey,
            'todos-management',
            route.paramMap.get('todoType'),
            todoStatus,
            route.paramMap.get('businessCaseId'),
            route.paramMap.get('todoId'),
          ]);
        }

        return true;
      }),
    );
};
