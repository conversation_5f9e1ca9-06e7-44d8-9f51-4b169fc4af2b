import {
  AssignmentsByStatusesCount,
  UserAssignment,
  UserAssignmentGroupPositionResult,
  UserAssignmentResponse,
} from '@fincloud/swagger-generator/internal-tools';

import { User } from '@fincloud/swagger-generator/authorization-server';
import { ParticipantUserDto } from '@fincloud/swagger-generator/business-case-manager';
import { Company } from '@fincloud/swagger-generator/company';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { TodosRouterParams } from '@fincloud/types/models';
import { TodosModalType } from '../enums/todos-modal-type';
import { BusinessCasePaging } from './business-case-paging';
import { UserAssignmentTypes } from './todo-assignment-type';
import { TodosBusinessCaseFilters } from './todos-business-case-filters';
import { TodosFilters } from './todos-filters';

export interface TodosManagementState {
  businessCases: UserAssignmentResponse;
  filters: TodosBusinessCaseFilters;
  todosFilters: TodosFilters;
  todos: UserAssignment[];
  counters: AssignmentsByStatusesCount;
  businessCasePaging: BusinessCasePaging;
  currentRouteParams: TodosRouterParams;
  isLoadingBusinessCases: boolean;
  isLoadingTodos: boolean;
  todoPosition: {
    data: UserAssignmentGroupPositionResult;
    isLoading: boolean;
  };
  companiesAvailableInOrganization: Company[];
  companyCases: ExchangeBusinessCase[];
  selectedBusinessCaseId: string;
  usersWithAccessToCase: ParticipantUserDto[];
  platformManagersForCustomer: User[];
  availableAssignmentTypes: UserAssignmentTypes[];
  modalType: TodosModalType;
}
