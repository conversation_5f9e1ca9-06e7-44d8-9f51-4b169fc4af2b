import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsCoreLayoutModule } from '@fincloud/core/layout';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinAvatarModule } from '@fincloud/ui/avatar-default';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinCardLabelModule } from '@fincloud/ui/card-label';
import { FinCheckboxModule } from '@fincloud/ui/checkbox';
import { FinContainerModule } from '@fincloud/ui/container';
import { FinDatePickerModule } from '@fincloud/ui/date-picker';
import { FinDropdownComponent, FinDropdownModule } from '@fincloud/ui/dropdown';
import { FinExpansionPanelModule } from '@fincloud/ui/expansion-panel';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinFilterTabsModule } from '@fincloud/ui/filter-tabs';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinInputModule } from '@fincloud/ui/input';
import { FinLoaderComponent } from '@fincloud/ui/loader';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import {
  FinModalCloseDirective,
  FinModalContentDirective,
  FinModalFooterDirective,
  FinModalHeaderDirective,
  FinModalModule,
} from '@fincloud/ui/modal';
import { FinPaginatorModule } from '@fincloud/ui/paginator';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinVerticalSeparatorDirective } from '@fincloud/ui/separators';
import { FinSidePanelModule } from '@fincloud/ui/side-panel';
import { FinRowTemplateDirective, FinTableModule } from '@fincloud/ui/table';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { FinTextAreaModule } from '@fincloud/ui/text-area';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinTypesModule } from '@fincloud/ui/types';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { TodosManagementEffects } from './+state/effects/todos-management.effects';
import { todosManagementFeature } from './+state/reducers/todos-management.reducer';
import { TodosManagementDescriptionComponent } from './components/todos-management-description/todos-management-description.component';
import { TodosManagementFiltersPanelComponent } from './components/todos-management-filters-panel/todos-management-filters-panel.component';
import { TodosManagementFiltersComponent } from './components/todos-management-filters/todos-management-filters.component';
import { TodosManagementLayoutComponent } from './components/todos-management-layout/todos-management-layout.component';
import { TodosManagementListComponent } from './components/todos-management-list/todos-management-list.component';
import { TodosManagementManualToDoModalComponent } from './components/todos-management-manual-to-do-modal/todos-management-manual-to-do-modal.component';
import { TodosManagementRoutingModule } from './todos-management-routing.module';

@NgModule({
  declarations: [
    TodosManagementLayoutComponent,
    TodosManagementFiltersComponent,
    TodosManagementFiltersPanelComponent,
    TodosManagementListComponent,
    TodosManagementDescriptionComponent,
    TodosManagementManualToDoModalComponent,
  ],
  imports: [
    CommonModule,
    TodosManagementRoutingModule,
    ReactiveFormsModule,
    FinFilterTabsModule,
    FinDropdownComponent,
    FinInputModule,
    NsUiIconsModule,
    FinTabsModule,
    FinTypesModule,
    FinExpansionPanelModule,
    FinPaginatorModule,
    FinTableModule,
    FinRowTemplateDirective,
    FinBadgesModule,
    FinIconModule,
    FinContainerModule,
    FinLoaderComponent,
    FinButtonModule,
    FinTruncateTextModule,
    FinDatePickerModule,
    FinDropdownModule,
    FinModalModule,
    FinModalCloseDirective,
    FinModalContentDirective,
    FinModalHeaderDirective,
    FinModalFooterDirective,
    FinHeaderAndFooterModule,
    FinTextAreaModule,
    FinVerticalSeparatorDirective,
    FinAvatarModule,
    FinFieldMessageModule,
    FinActionsMenuModule,
    FinMenuItemModule,
    FinCardLabelModule,
    FinSidePanelModule,
    FinScrollbarModule,
    FinCheckboxModule,
    NsCorePipesModule,
    NsUiTooltipModule,
    NsCoreLayoutModule,
  ],
  exports: [],
  providers: [
    provideState(todosManagementFeature),
    provideEffects(TodosManagementEffects),
  ],
})
export class TodosManagementModule {}
