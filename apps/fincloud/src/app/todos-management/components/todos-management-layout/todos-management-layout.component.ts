import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TODOS_TABS_OPTIONS } from '@fincloud/neoshare/todos-management';
import { TodosStatus } from '@fincloud/types/enums';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinTabChangeEvent, FinTabType } from '@fincloud/ui/tabs';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { shareReplay } from 'rxjs';
import { TodosManagementPageActions } from '../../+state/actions';
import { todosManagementFeature } from '../../+state/reducers/todos-management.reducer';
import { EMPTY_STATE_MESSAGES } from '../../utils/empty-state-messages';

@Component({
  selector: 'app-todos-management-layout',
  templateUrl: './todos-management-layout.component.html',
  styleUrl: './todos-management-layout.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TodosManagementLayoutComponent {
  todosTabTypes = FinTabType;
  finSize = FinSize;
  finButtonAppearance = FinButtonAppearance;
  tabsOptions = TODOS_TABS_OPTIONS;

  businessCasePaginatorData$ = this.store.select(
    todosManagementFeature.selectBusinessCasePaginatorData,
  );

  businessCases$ = this.store
    .select(todosManagementFeature.selectBusinessCases)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  tabIndex$ = this.store.select(todosManagementFeature.selectSelectedTabIndex);

  businessCaseInfo$ = this.store.select(
    todosManagementFeature.selectBusinessCaseInfo,
  );

  filters$ = this.store.select(todosManagementFeature.selectCounters);

  emptyStateData$ = this.store.select(
    todosManagementFeature.selectEmptyStateData,
  );

  isLoadingBusinessCases$ = this.store
    .select(todosManagementFeature.selectIsLoadingBusinessCases)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  emptyStateMessages = EMPTY_STATE_MESSAGES;

  constructor(private store: Store) {}

  changeTab(tabChange: FinTabChangeEvent): void {
    this.store.dispatch(
      TodosManagementPageActions.changeUrlParams({
        todoType: TODOS_TABS_OPTIONS[tabChange.index].name,
        todoStatus: TodosStatus.PENDING,
      }),
    );
  }

  pageChange({ pageNumber }: { pageNumber: number }): void {
    this.store.dispatch(
      TodosManagementPageActions.changeBusinessCasePage({ pageNumber }),
    );
  }

  openCreateToDoModal(): void {
    this.store.dispatch(TodosManagementPageActions.openCreateToDoModal());
  }
}
