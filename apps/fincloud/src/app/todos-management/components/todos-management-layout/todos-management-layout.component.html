<div class="tw-pt-6">
  <div class="tw-flex tw-flex-row tw-items-start tw-justify-between">
    <h3 class="tw-text-color-text-primary heading2 tw-pb-6" i18n="@@todoList">
      To-do-Liste
    </h3>
    <button
      fin-button
      [size]="finSize.M"
      [appearance]="finButtonAppearance.PRIMARY"
      (click)="openCreateToDoModal()"
    >
      <fin-icon name="add"></fin-icon>
      <span i18n="@@contractList.search.button.label">Erstellen</span>
    </button>
  </div>

  <fin-tabs
    #finTabs
    [type]="todosTabTypes.SECONDARY"
    [size]="finSize.XL"
    (focusChange)="changeTab($event)"
    [selectedIndex]="tabIndex$ | async"
  >
    @for (tab of tabsOptions; track tab.name) {
      <fin-tab #finTab>
        <ng-template finTabLabel>
          <span class="tw-me-[6px]">{{ tab.label }}</span>
        </ng-template>
      </fin-tab>
    }
  </fin-tabs>
  <app-todos-management-filters></app-todos-management-filters>
  <div class="tw-pt-[1.2rem] tw-mb-[4.8rem]">
    @if (businessCaseInfo$ | async; as businessCaseInfo) {
      @if (businessCaseInfo.length > 0) {
        <app-todos-management-list></app-todos-management-list>

        @if (businessCasePaginatorData$ | async; as businessCasePaginatorData) {
          <fin-paginator
            [totalItems]="businessCasePaginatorData.totalItems"
            [pageSize]="businessCasePaginatorData.pageSize"
            [pageSizeOptions]="businessCasePaginatorData.pageSizeOptions"
            [pageNumber]="businessCasePaginatorData.pageNumber"
            (pageChange)="pageChange($event)"
          ></fin-paginator>
        }
      }

      @if (
        businessCaseInfo.length === 0 &&
        (isLoadingBusinessCases$ | async) === false
      ) {
        <ng-template
          [ngTemplateOutlet]="emptyContainer"
          [ngTemplateOutletContext]="{ businessCaseInfo }"
        ></ng-template>
      }

      @if (isLoadingBusinessCases$ | async) {
        <ng-template [ngTemplateOutlet]="loading"></ng-template>
      }
    }
  </div>
</div>

<ng-template #emptyContainer let-businessCaseInfo="businessCaseInfo">
  <!-- will remove the tw-max-full tw class  after release 14/ fixed is already implemented in the library -->
  <div
    class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-max-w-full tw-h-[49rem] empty-container"
    finContainer
    [boxShadow]="false"
    [borderRadius]="false"
  >
    <fin-icon
      src="/assets/svg/svgNoTodos.svg"
      class="tw-text-[7.6rem]"
    ></fin-icon>
    @if (emptyStateData$ | async; as emptyStateData) {
      @for (emptyMessage of emptyStateMessages; track emptyMessage.status) {
        @if (
          !emptyStateData.searchPhrase &&
          emptyMessage.status === emptyStateData.todoStatus
        ) {
          <div
            class="tw-text-color-text-primary tw-mb-3 tw-text-[1.6rem] tw-font-semibold tw-mb-[0.8rem]"
          >
            {{ emptyMessage.header }}
          </div>
          <div
            class="tw-text-color-text-tertiary tw-text-[1.2rem] tw-font-medium"
          >
            {{ emptyMessage.description }}
          </div>
        }
      }

      @if (!!emptyStateData.searchPhrase) {
        <div
          class="tw-text-color-text-primary tw-mb-3 tw-text-[1.6rem] tw-font-semibold tw-mb-[0.8rem]"
          i18n="@@todosManagement.noTasks.header.message.emptySearch"
        >
          Keine Ergebnisse
        </div>
        <div
          class="tw-text-color-text-tertiary tw-text-[1.2rem] tw-font-medium"
          i18n="@@todosManagement.noTasks.regular.message.emptySearch"
        >
          Für die angewandten Kriterien gibt es keine Ergebnisse.
        </div>
      }
    }
  </div>
</ng-template>

<ng-template #loading>
  <div
    class="tw-flex tw-flex-col tw-justify-center tw-items-center tw-h-[49rem] empty-container"
    finContainer
    [boxShadow]="false"
    [borderRadius]="false"
  >
    <fin-loader></fin-loader>
  </div>
</ng-template>
