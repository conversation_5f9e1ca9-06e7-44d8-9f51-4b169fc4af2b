@if (modalType$ | async; as modalType) {
  <fin-modal-header>
    <fin-header [size]="finSize.M">
      @switch (modalType) {
        @case (todosModalType.UPDATE) {
          <ng-container i18n="@@todosManagement.updateToDoModal.headerLabel">
            To-do bearbeiten</ng-container
          >
        }
        @case (todosModalType.VIEW) {
          <ng-container i18n="@@todosManagement.viewToDoModal.headerLabel">
            To-do ansehen</ng-container
          >
        }
        @case (todosModalType.CREATE) {
          <ng-container i18n="@@todosManagement.createToDoModal.headerLabel"
            >To-do erstellen</ng-container
          >
        }
      }
      <button fin-button-action fin-modal-close [size]="finSize.L">
        <fin-icon name="close"></fin-icon>
      </button>
    </fin-header>
  </fin-modal-header>

  <fin-modal-content class="!tw-max-h-[55rem]">
    <form class="tw-flex tw-flex-col tw-gap-[1.8rem]" [formGroup]="toDoForm">
      <fin-dropdown
        [readonly]="isViewModal"
        formControlName="company"
        dynamicErrorSpace="dynamic"
        i18n-placeholder="
          @@todosManagement.createToDoModal.controlPlaceholder.company"
        placeholder="Unternehmen auswählen"
        autocomplete
        [messageThreshold]="messageThreshold"
        [options]="
          companyOptions$
            | async
            | filterByTerm: 'label' : toDoForm.controls.company?.value
        "
        [size]="finSize.L"
        (selectionChange)="companySelectionChanged($event)"
        (autoCompleteInputChange)="onCompanyAutoCompleteChange($event)"
      >
        <ng-container finFieldLabel>
          @if (isViewModal) {
            <ng-container i18n="@@companyEdit.table.company"
              >Unternehmen</ng-container
            >
          } @else {
            <ng-container i18n="@@createBusinessCase.formField"
              >Unternehmen</ng-container
            >
          }
        </ng-container>
        <fin-field-messages>
          <ng-template
            finFieldMessage
            type="error"
            errorKey="required"
            i18n="@@validationError.requiredField"
          >
            Pflichtfeld
          </ng-template>
        </fin-field-messages>
      </fin-dropdown>

      <div class="tw-grid tw-grid-cols-2 tw-gap-12">
        <fin-dropdown
          formControlName="case"
          [readonly]="isViewModal"
          dynamicErrorSpace="dynamic"
          i18n-placeholder="
            @@todosManagement.createToDoModal.controlPlaceholder.case"
          placeholder="Fall auswählen ID"
          autocomplete
          [inputDisplayFn]="isCreateModal ? formatBusinessCaseField : undefined"
          [messageThreshold]="messageThreshold"
          [options]="
            caseOptions$ | async | filterByTerm: 'label' : caseControl?.value
          "
          [size]="finSize.L"
          (selectionChange)="caseSelectionChanged($event)"
        >
          <ng-container finFieldLabel>
            @if (isViewModal) {
              <ng-container i18n="@@cases.filter.modal.case">Fall</ng-container>
            } @else {
              <ng-container
                i18n="@@todosManagement.createToDoModal.controlLabel.case"
                >Fall</ng-container
              >
            }
          </ng-container>
          <ng-template [finOptionLabel]="caseOptions$ | async" let-option>
            <div class="tw-flex">
              <div class="tw-text-body-3-strong">
                {{ option.value.autoGeneratedBusinessCaseName }}
              </div>
              <div
                finVerticalSeparator
                [type]="finSeparator.STRONG"
                class="!tw-h-[1.2rem] tw-mx-[0.7rem] tw-mt-[0.3rem]"
              ></div>
              <div class="tw-text-body-3-moderate">
                {{
                  option.value.information?.financingVolume?.value ??
                    option.value.finStructureCommonFields?.totalInvestmentAmount
                      .value ??
                    0
                    | currency
                    | removeTrailingZeros
                }}
              </div>
            </div>
          </ng-template>
          <fin-field-messages>
            <ng-template
              finFieldMessage
              type="error"
              errorKey="required"
              i18n="@@validationError.requiredField"
            >
              Pflichtfeld
            </ng-template>
          </fin-field-messages>
        </fin-dropdown>
        @if (isViewModal && !todo?.dueDate) {
          <ng-container *ngTemplateOutlet="noDueDateTemplate"></ng-container>
        } @else {
          <ng-container *ngTemplateOutlet="selectDateTemplate"></ng-container>
        }
      </div>

      <fin-text-area
        [readonly]="isViewModal"
        formControlName="description"
        dynamicErrorSpace="dynamic"
        [maxLength]="maxDescriptionLength"
      >
        <ng-container finInputLabel>
          @if (isViewModal) {
            <ng-container i18n="@@labels.description"
              >Beschreibung</ng-container
            >
          } @else {
            <ng-container
              i18n="@@todosManagement.createToDoModal.controlLabel.description"
              >Beschreibung</ng-container
            >
          }
        </ng-container>
        <fin-field-messages>
          <ng-template
            finFieldMessage
            type="error"
            errorKey="required"
            i18n="@@validationError.requiredField"
          >
            Pflichtfeld
          </ng-template>
          <ng-template
            finFieldMessage
            type="error"
            errorKey="whitespace"
            i18n="@@validationError.requiredField"
          >
            Pflichtfeld
          </ng-template>
        </fin-field-messages>
      </fin-text-area>

      @if (!isViewModal) {
        <fin-dropdown
          formControlName="assignee"
          dynamicErrorSpace="dynamic"
          i18n-label="@@todosManagement.createToDoModal.controlLabel.assignee"
          label="Zugewiesene Person*"
          i18n-placeholder="
            @@todosManagement.createToDoModal.controlPlaceholder.assignee"
          placeholder="Zugeordnete Person auswählen"
          autocomplete
          [messageThreshold]="messageThreshold"
          [showChips]="!todo"
          [options]="
            assigneeOptions$
              | async
              | filterByTerm: 'label' : assigneeControl.value
          "
          [size]="finSize.L"
        >
          <ng-template [finOptionPrefix]="assigneeOptions$ | async" let-option>
            <fin-icon name="work"></fin-icon>
          </ng-template>
          <ng-template [finOptionLabel]="assigneeOptions$ | async" let-option>
            <div class="tw-text-body-2-strong">
              {{ option.label }}
            </div>
            <div class="tw-text-body-3-moderate">
              {{ option.value.customerKey }}
            </div>
          </ng-template>
          <ng-template [finChipPrefix]="assigneeOptions$ | async" let-option>
            <fin-avatar-default
              class="tw-mr-[1.2rem]"
              [firstName]="option.value.firstName | finWordExtractor: 'first'"
              [lastName]="option.value.lastName | finWordExtractor: 'last'"
              [size]="finSize.XS"
            ></fin-avatar-default>
          </ng-template>
          <fin-field-messages>
            <ng-template
              finFieldMessage
              type="error"
              errorKey="required"
              i18n="@@validationError.requiredField"
            >
              Pflichtfeld
            </ng-template>
          </fin-field-messages>
        </fin-dropdown>
      }
    </form>
  </fin-modal-content>

  <fin-modal-footer separator>
    <fin-footer
      [size]="finSize.M"
      class="tw-flex"
      [class.tw-justify-end]="isViewModal"
    >
      @if (isCreateModal || isUpdateModal) {
        <ng-container
          *ngTemplateOutlet="createOrUpdateButtonsTemplate"
        ></ng-container>
      } @else {
        <ng-container *ngTemplateOutlet="viewButtonTemplate"></ng-container>
      }
    </fin-footer>
  </fin-modal-footer>
}
<ng-template #createOrUpdateButtonsTemplate>
  <button
    fin-button
    fin-modal-close
    [size]="finSize.L"
    [appearance]="finAppearance.SECONDARY"
  >
    <span i18n="@@button.label.cancel">Abbrechen</span>
  </button>
  <button
    fin-button
    [size]="finSize.L"
    [appearance]="finAppearance.PRIMARY"
    (click)="onSubmit()"
  >
    @if (isCreateModal) {
      <ng-container i18n="@@contractList.search.button.label"
        >Erstellen</ng-container
      >
    } @else {
      <ng-container i18n="@@todoModal.update.button.label"
        >Aktualisieren</ng-container
      >
    }
  </button>
</ng-template>

<ng-template #viewButtonTemplate>
  <button
    fin-button
    fin-modal-close
    [size]="finSize.L"
    [appearance]="finAppearance.SECONDARY"
    (click)="redirectByTodoType(todo)"
  >
    <ng-container i18n="@@todos.button.openCase">Fall öffnen</ng-container>
  </button>
</ng-template>

<ng-template #noDueDateTemplate>
  <fin-input
    [formGroup]="toDoForm"
    formControlName="noDueDate"
    [type]="finInputType.TEXT"
    [size]="finSize.L"
    i18n-label="@@todosManagement.table.columns.dueDate"
    label="Zu erledigen bis"
    readonly="true"
  ></fin-input>
</ng-template>

<ng-template #selectDateTemplate>
  <fin-date-picker
    [formGroup]="toDoForm"
    formControlName="dueDate"
    i18n-label="@@todosManagement.table.columns.dueDate"
    label="Zu erledigen bis"
    [placeholder]="datePlaceHolder | uppercase"
    [showIcon]="true"
    [minDate]="todayDate"
    [size]="finSize.L"
    [readonly]="isViewModal"
  >
    <fin-field-messages>
      <ng-template finFieldMessage type="error" errorKey="invalidDate">
        <ng-container
          i18n="@@todosManagement.createToDoModal.error.invalidDate.message"
          >Datum ist ungültig</ng-container
        >
      </ng-template>
      <ng-template finFieldMessage type="error" errorKey="minDate">
        <ng-container
          i18n="@@todosManagement.createToDoModal.error.minDate.message"
          >Datum darf nicht in der Vergangenheit liegen</ng-container
        >
      </ng-template>
    </fin-field-messages>
    <ng-template #finFooter>
      <button
        fin-button
        [appearance]="finAppearance.STEALTH"
        (click)="setDate(datePickerFooterDates.today.days)"
      >
        {{ datePickerFooterDates.today.label }}
      </button>
      <button
        fin-button
        [appearance]="finAppearance.STEALTH"
        (click)="setDate(datePickerFooterDates.tomorrow.days)"
      >
        {{ datePickerFooterDates.tomorrow.label }}
      </button>
      <button
        fin-button
        [appearance]="finAppearance.STEALTH"
        (click)="setDate(datePickerFooterDates.dateAfter7Days.days)"
      >
        {{ datePickerFooterDates.dateAfter7Days.label }}
      </button>
      <button
        fin-button
        [appearance]="finAppearance.STEALTH"
        (click)="setDate(datePickerFooterDates.dateAfter30Days.days)"
      >
        {{ datePickerFooterDates.dateAfter30Days.label }}
      </button>
    </ng-template>
  </fin-date-picker>
</ng-template>
