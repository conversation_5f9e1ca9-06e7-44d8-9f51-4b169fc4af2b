import { CurrencyPipe } from '@angular/common';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import {
  FormControl,
  NonNullableFormBuilder,
  Validators,
} from '@angular/forms';
import { DateFormats, DateService } from '@fincloud/core/date';
import { inputWhitespaceValidator } from '@fincloud/core/form';
import { MathUtils } from '@fincloud/core/math';
import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { StateLibTodosManagementPageActions } from '@fincloud/state/todos-management';
import { StateLibUsersPageActions } from '@fincloud/state/users';
import { User } from '@fincloud/swagger-generator/authorization-server';
import { Company } from '@fincloud/swagger-generator/company';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { UserAssignment } from '@fincloud/swagger-generator/internal-tools';
import { FinButtonActionType, FinButtonAppearance } from '@fincloud/ui/button';
import { isValidDate, minDate } from '@fincloud/ui/date-picker';
import { FinDropdownOption } from '@fincloud/ui/dropdown';
import { FinInputType } from '@fincloud/ui/input';
import {
  FIN_MODAL_DATA,
  FIN_MODAL_REF,
  FIN_MODAL_REF_PROVIDER,
  FinModalRef,
} from '@fincloud/ui/modal';
import { FinSeparator } from '@fincloud/ui/separators';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { isString } from 'lodash-es';
import { Observable, shareReplay, tap } from 'rxjs';
import { TodosManagementPageActions } from '../../+state/actions';
import { todosManagementFeature } from '../../+state/reducers/todos-management.reducer';
import { TodosModalType } from '../../enums/todos-modal-type';
import { TodoSubmitModalData } from '../../models/todo-submit-modal-data';
import { DATE_PICKER_FOOTER_DATES } from '../../utils/date-picker-footer-dates';

@Component({
  selector: 'app-todos-management-manual-to-do-modal',
  templateUrl: './todos-management-manual-to-do-modal.component.html',
  styleUrls: ['./todos-management-manual-to-do-modal.component.scss'],
  providers: [FIN_MODAL_REF_PROVIDER, CurrencyPipe, RemoveTrailingZerosPipe],
})
export class TodosManagementManualToDoModalComponent
  implements OnInit, OnDestroy
{
  isViewModal = false;
  isCreateModal = false;
  isUpdateModal = false;
  readonly maxDescriptionLength = 512;
  readonly messageThreshold = 1;
  readonly finSize = FinSize;
  readonly finSeparator = FinSeparator;
  readonly finInputType = FinInputType;
  readonly finAppearance = FinButtonAppearance;
  readonly datePickerFooterDates = DATE_PICKER_FOOTER_DATES;
  readonly finButtonActionType = FinButtonActionType;
  readonly todosModalType = TodosModalType;
  readonly todayDate = new Date();
  readonly datePlaceHolder = this.regionalSettingsService.dateInputPlaceholder;

  readonly companyOptions$: Observable<FinDropdownOption[]> = this.store
    .select(todosManagementFeature.selectCompaniesAvailableInOrganization)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));
  readonly caseOptions$: Observable<FinDropdownOption[]> = this.store
    .select(todosManagementFeature.selectCompanyCases)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));
  readonly assigneeOptions$: Observable<FinDropdownOption[]> = this.store
    .select(todosManagementFeature.selectUsersWithAccessToTheCase)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  readonly modalType$ = this.store
    .select(todosManagementFeature.selectModalType)
    .pipe(
      tap((type) => {
        this.isCreateModal = type === TodosModalType.CREATE;
        this.isViewModal = type === TodosModalType.VIEW;
        this.isUpdateModal = type === TodosModalType.UPDATE;
        if (this.isUpdateModal || this.isViewModal) {
          this.fillToDoForm();
        }
      }),
    );

  readonly toDoForm = this.fb.group({
    company: ['', [Validators.required]],
    case: ['', Validators.required],
    dueDate: ['', [minDate(this.todayDate), isValidDate()]],
    description: ['', [Validators.required, inputWhitespaceValidator()]],
    assignee: this.fb.control<User[] | string>(
      this.isCreateModal ? [] : '',
      Validators.required,
    ),
    noDueDate: $localize`:@@todos.noDueDate:Kein Fälligkeitsdatum`,
  });

  get caseControl(): FormControl<string> {
    return this.toDoForm.controls.case;
  }

  get assigneeControl(): FormControl<User[] | string> {
    return this.toDoForm.controls.assignee;
  }

  constructor(
    private fb: NonNullableFormBuilder,
    private store: Store,
    private dateService: DateService,
    private currencyPipe: CurrencyPipe,
    private removeTrailingZerosPipe: RemoveTrailingZerosPipe,
    private regionalSettingsService: RegionalSettingsService,
    @Inject(FIN_MODAL_REF)
    private finModalRef: FinModalRef<TodosManagementManualToDoModalComponent>,
    @Inject(FIN_MODAL_DATA)
    public todo: UserAssignment | undefined,
  ) {}

  ngOnInit(): void {
    this.store.dispatch(
      TodosManagementPageActions.getPlatformManagersForCustomer(),
    );
  }

  formatBusinessCaseField = (value: ExchangeBusinessCase) => {
    if (!value || isString(value)) {
      return '';
    }
    const formattedFinancingValue = Number(
      value.information?.financingVolume?.value ??
        value.finStructureCommonFields?.totalInvestmentAmount?.value,
    );
    return this.formatCaseField(
      value.autoGeneratedBusinessCaseName,
      formattedFinancingValue,
    );
  };

  setDate(daysToBeAdded: number): void {
    const selectedDate = new Date(this.todayDate);
    selectedDate.setDate(selectedDate.getDate() + daysToBeAdded);
    this.toDoForm.patchValue({
      dueDate: this.dateService.formatDate(
        selectedDate,
        DateFormats.YYYY_DASH_MM_DASH_DD,
      ),
    });
  }

  companySelectionChanged(companyChanged: Company): void {
    this.store.dispatch(TodosManagementPageActions.clearCompanyCases());
    this.caseControl.setValue('');
    this.assigneeControl.setValue([]);
    if (companyChanged) {
      this.store.dispatch(
        TodosManagementPageActions.getCompanyCases({
          companyId: companyChanged.id,
        }),
      );
    }
  }

  onCompanyAutoCompleteChange(query: string): void {
    if (!query) {
      this.store.dispatch(TodosManagementPageActions.clearCompanyCases());
      this.caseControl.setValue('');
      this.assigneeControl.setValue([]);
    }
  }

  caseSelectionChanged(caseChanged: ExchangeBusinessCase): void {
    this.assigneeControl.setValue([]);
    this.store.dispatch(
      TodosManagementPageActions.setSelectedBusinessCase({
        businessCaseId: caseChanged.id,
      }),
    );
  }

  onCaseAutoCompleteChange(query: string): void {
    if (!query) {
      this.assigneeControl.setValue([]);
      this.store.dispatch(
        TodosManagementPageActions.setSelectedBusinessCase({
          businessCaseId: '',
        }),
      );
    }
  }

  onSubmit(): void {
    if (this.toDoForm.invalid) {
      this.updateFormValidations();
      return;
    }

    if (this.isCreateModal) {
      this.store.dispatch(
        TodosManagementPageActions.createToDo({
          modalData: this.toDoForm.getRawValue() as TodoSubmitModalData<User[]>,
        }),
      );
    }
    if (this.isUpdateModal && this.todo?.id?.trim()) {
      this.store.dispatch(
        TodosManagementPageActions.updateToDo({
          modalData: {
            ...this.toDoForm.getRawValue(),
            case: this.todo.businessCaseId,
            userAssignmentId: this.todo.id,
          } as TodoSubmitModalData<User>,
        }),
      );
    }

    this.finModalRef.close();
  }

  redirectByTodoType(todo: UserAssignment): void {
    this.store.dispatch(
      StateLibTodosManagementPageActions.redirectByTodoType({ todo }),
    );
  }

  ngOnDestroy(): void {
    this.store.dispatch(StateLibUsersPageActions.clearMultipleUsers());
  }

  private updateFormValidations(): void {
    this.toDoForm.markAllAsTouched();
    Object.keys(this.toDoForm.controls).forEach((controlName) => {
      this.toDoForm.get(controlName).updateValueAndValidity();
    });
  }

  private fillToDoForm() {
    this.store.dispatch(
      TodosManagementPageActions.setSelectedBusinessCase({
        businessCaseId: this.todo.businessCaseId,
      }),
    );

    this.toDoForm.patchValue({
      company: this.todo.metadata.businessCaseCompanyName as string,
      case: this.formatCaseField(
        this.todo.metadata.autoGeneratedBusinessCaseName as string,
        this.todo.metadata.financingVolumeValue as number,
      ),
      description: this.todo.metadata.description as string,
      assignee: this.todo.metadata.assigneeUserFullName as string,
      dueDate: this.todo.dueDate ?? '',
    });

    this.toDoForm.controls.company.disable();
    this.caseControl.disable();
  }

  private formatCaseField(
    businessCaseName: string,
    financingVolume: number,
  ): string {
    const formattedFinancingValue = this.removeTrailingZerosPipe.transform(
      this.currencyPipe.transform(MathUtils.stripSign(financingVolume ?? 0)),
    );
    return `${businessCaseName} | ${formattedFinancingValue}`;
  }
}
