import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { selectRouteParams } from '@fincloud/state/router';
import {
  StateLibTodosManagementPageActions,
  todosManagementBadgeFeature,
} from '@fincloud/state/todos-management';
import { UserAssignment } from '@fincloud/swagger-generator/internal-tools';
import {
  TodosApiPerspective,
  TodosApiStatus,
  TodosApiUserAssignmentType,
  TodosStatus,
} from '@fincloud/types/enums';
import {
  FinButtonActionType,
  FinButtonAppearance,
  FinButtonShape,
} from '@fincloud/ui/button';
import { FinAccordionType } from '@fincloud/ui/expansion-panel';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { shareReplay } from 'rxjs';
import { TodosManagementPageActions } from '../../+state/actions';
import { todosManagementFeature } from '../../+state/reducers/todos-management.reducer';
import { TodosOrderBy } from '../../enums/todos-order-by';
import { TodosSortingDirections } from '../../enums/todos-sorting-directions';
import { TodoTypeBadgeText } from '../../enums/todos-types-badge-text';
import { TODOS_LABEL } from '../../utils/todos-badge-label';
import { setTimeoutUnpatched } from '@fincloud/core/utils';

@Component({
  selector: 'app-todos-management-list',
  templateUrl: './todos-management-list.component.html',
  styleUrl: './todos-management-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TodosManagementListComponent {
  nonCaseRelatedBusinessCaseId = 'Non case related';
  finSize = FinSize;
  finButtonAppearance = FinButtonAppearance;
  finAccordionType = FinAccordionType;
  todosBadgeText = TodoTypeBadgeText;
  todosStatus = TodosStatus;
  todosApiStatus = TodosApiStatus;
  todosLabelConfig = TODOS_LABEL;
  manualAssignmentType = TodosApiUserAssignmentType.MANUAL_ASSIGNMENT;
  finButtonShape = FinButtonShape;
  finButtonActionType = FinButtonActionType;
  completeToDoId = '';

  todoTranslatedStatuses = {
    CANCELLED: $localize`:@@todo.status.cancel:Abgebrochen`,
    PENDING: $localize`:@@todo.status.pending:Ausstehend`,
    ARCHIVED: $localize`:@@todo.status.archived:Archiviert`,
    COMPLETED: $localize`:@@todo.status.completed:Erledigt`,
  };

  highlightedTodo$ = this.store.select(
    todosManagementBadgeFeature.selectHighlightedTodo,
  );

  todosApiUserAssignmentType = TodosApiUserAssignmentType;

  columns$ = this.store
    .select(todosManagementFeature.selectTodoTableColumns)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  perspective$ = this.store.select(todosManagementFeature.selectPerspective);

  firstTodo$ = this.store.select(todosManagementFeature.selectFirstTodo);

  businessCaseInfo$ = this.store
    .select(todosManagementFeature.selectBusinessCaseInfo)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  todosData$ = this.store
    .select(todosManagementFeature.selectTodos)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  routeParams$ = this.store.select(selectRouteParams);

  isLoadingTodos$ = this.store.select(
    todosManagementFeature.selectIsLoadingTodos,
  );

  private timeout: NodeJS.Timeout;

  constructor(
    private store: Store,
    private router: Router,
    private route: ActivatedRoute,
  ) {}

  getTodos(businessCaseId: string, selectedBusinessCase: string): void {
    if (businessCaseId === selectedBusinessCase) {
      return;
    }

    this.store.dispatch(
      TodosManagementPageActions.getTodos({ businessCaseId }),
    );
  }

  loadMoreTodos(businessCaseId: string): void {
    this.store.dispatch(
      TodosManagementPageActions.getMoreTodos({ businessCaseId }),
    );
  }

  changeSortTodos(
    { prop, dir }: { prop: string; dir: string },
    businessCaseId: string,
  ): void {
    this.store.dispatch(
      TodosManagementPageActions.setTodoOrderBy({
        businessCaseId,
        filters: {
          orderBy: prop as TodosOrderBy,
          direction: dir.toUpperCase() as TodosSortingDirections,
        },
      }),
    );
  }

  viewTodo(todo: UserAssignment, perspective: TodosApiPerspective) {
    this.clearQueryParams();
    if (todo.type === this.manualAssignmentType) {
      if (
        perspective === TodosApiPerspective.DELEGATED &&
        todo.status === TodosApiStatus.PENDING
      ) {
        this.store.dispatch(
          TodosManagementPageActions.openUpdateToDoModal({
            todo,
          }),
        );
        return;
      }
      this.store.dispatch(
        TodosManagementPageActions.openViewToDoModal({
          todo,
        }),
      );
      return;
    }
    this.store.dispatch(
      StateLibTodosManagementPageActions.redirectByTodoType({
        todo,
      }),
    );
  }

  completeManualAssignment(userAssignmentId: string, event: MouseEvent): void {
    event.stopPropagation();
    this.completeToDoId = userAssignmentId;
    this.store.dispatch(
      TodosManagementPageActions.completeTodo({
        userAssignmentId,
      }),
    );
  }

  actionMenuClicked(event: MouseEvent): void {
    event.stopPropagation();
  }

  cancelManualAssignment(userAssignmentId: string): void {
    this.store.dispatch(
      TodosManagementPageActions.openCancelToDoModal({
        userAssignmentId,
      }),
    );
  }

  rowHighlight(
    selectedTodoId: string,
    businessCaseId: string,
    highlight: string,
  ): (row: UserAssignment) => string {
    return (row: UserAssignment): string => {
      if (
        row.id === selectedTodoId &&
        businessCaseId === row.businessCaseId &&
        highlight === 'true'
      ) {
        if (this.timeout) {
          clearTimeout(this.timeout);
        }
        this.setTimer();
        return '!tw-border !tw-border-solid !tw-border-color-border-default-warning tw-rounded-[0.4rem] tw-bg-color-background-neutral-minimal';
      }
      return '';
    };
  }

  private setTimer() {
    this.timeout = setTimeoutUnpatched(() => {
      this.clearQueryParams();
    }, 5000);
  }

  private clearQueryParams(): Promise<boolean> {
    return this.router.navigate([], {
      queryParams: {},
      relativeTo: this.route,
    });
  }
}
