@if (routeParams$ | async; as routeParams) {
  @if (perspective$ | async; as perspective) {
    @if (highlightedTodo$ | async; as highlightedTodo) {
      <div class="tw-pt-[1.2rem] tw-mb-[4.8rem]">
        <fin-accordion [type]="finAccordionType.LIGHT" togglePosition="before">
          @for (
            businessCase of businessCaseInfo$ | async;
            track businessCase.businessCaseId
          ) {
            @if (firstTodo$ | async; as todo) {
              <fin-expansion-panel
                class="!tw-mb-[1.2rem]"
                #finPanel
                [isSummaryAlwaysVisible]="true"
                [expanded]="
                  routeParams.businessCaseId === businessCase.businessCaseId
                "
                (opened)="
                  getTodos(businessCase.businessCaseId, todo?.businessCaseId)
                "
              >
                <ng-template #finTitle>
                  <div
                    class="tw-text-[1.2rem] tw-text-color-text-primary tw-font-medium"
                  >
                    @if (
                      businessCase?.businessCaseId !==
                      nonCaseRelatedBusinessCaseId
                    ) {
                      <span class="tw-font-semibold">{{
                        businessCase?.autoGeneratedBusinessCaseName
                      }}</span>
                      <span class="tw-px-[0.4rem]">|</span>
                      {{
                        businessCase?.financingVolumeValue ?? 0
                          | currency
                          | removeTrailingZeros
                      }}
                      <span class="tw-px-[0.4rem]">|</span>
                      {{ businessCase?.businessCaseCompanyName }}
                    } @else {
                      <span
                        class="tw-font-semibold"
                        i18n="@@todosManagement.noCaseRelated"
                        >Nicht fallbezogen</span
                      >
                    }
                    <span class="tw-ps-[0.4rem] tw-pe-[1.4rem]">|</span
                    ><span
                      class="tw-font-semibold tw-text-color-text-interactive"
                      i18n="@@todosManagement.summary.tasksCounts"
                      >{businessCase?.taskCount, plural,
                        one {1 Aufgabe}
                        other {{{businessCase?.taskCount}} Aufgaben}

                    }</span>
                  </div>
                </ng-template>
                <ng-template #finContent>
                  @if (todosData$ | async; as rows) {
                    <div class="tw-bg-color-surface-secondary">
                      <fin-table
                        [rows]="rows"
                        [columns]="columns$ | async"
                        [headerHeight]="40"
                        [rowHeight]="40"
                        [hasRowSpacing]="true"
                        [hasBorderRadius]="true"
                        emptyMessage=""
                        (sortChange)="
                          changeSortTodos($event, businessCase.businessCaseId)
                        "
                        (rowClick)="viewTodo($event, perspective)"
                        [rowClassFn]="
                          rowHighlight(
                            routeParams.todoId,
                            businessCase.businessCaseId,
                            highlightedTodo
                          )
                        "
                      >
                        <ng-container headerTemplates>
                          <ng-template
                            name="completedOn"
                            [finHeaderTemplate]="columns$ | async"
                            let-column
                          >
                            <div class="fin-flex fin-items-start">
                              <span
                                i18n="
                                  @@todosManagement.table.columns.completedOn"
                                >Abgeschlossen am</span
                              >
                              <ui-tooltip
                                placement="top"
                                i18n-text="
                                  @@todosManagement.tooltip.archivedAfter60days"
                                text="Aufgaben werden nach 60 Tagen automatisch archiviert."
                              >
                                <fin-icon
                                  class="fin-ms-[0.2rem] fin-me-[0.4rem]"
                                  name="info"
                                ></fin-icon>
                              </ui-tooltip>
                            </div>
                          </ng-template>
                        </ng-container>
                        <ng-container rowTemplates>
                          <ng-template
                            name="type"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <span class="tw-text-color-text-primary">
                              {{ todosLabelConfig[row.type] }}
                            </span>
                          </ng-template>
                          <ng-template
                            name="description"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <app-todos-management-description
                              [userAssignment]="row"
                              class="tw-font-medium tw-leading-8"
                            ></app-todos-management-description>
                          </ng-template>
                          <ng-template
                            name="status"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <span
                              [ngClass]="{
                                'tw-text-color-text-error':
                                  row.status === todosApiStatus.CANCELLED,
                                'tw-font-semibold tw-uppercase tw-text-color-text-secondary':
                                  row.status !== todosApiStatus.CANCELLED,
                              }"
                              >{{
                                todoTranslatedStatuses[row.status] | uppercase
                              }}</span
                            >
                          </ng-template>
                          <ng-template
                            name="date"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            @if (row.dueDate | date) {
                              <span class="tw-font-medium">{{
                                row.dueDate | date
                              }}</span>
                            } @else {
                              <div
                                class="tw-font-medium"
                                i18n="@@todos.noDueDate"
                              >
                                Kein Fälligkeitsdatum
                              </div>
                            }
                          </ng-template>
                          <ng-template
                            name="completedOn"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <span class="tw-font-medium">{{
                              row.lastUpdatedDate | date
                            }}</span>
                          </ng-template>
                          <ng-template
                            name="closedOn"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <span class="tw-font-medium">{{
                              row.lastUpdatedDate | date
                            }}</span>
                          </ng-template>
                          <ng-template
                            name="pendingAssignBy"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <span
                              class="tw-text-[1.4rem] tw-font-semibold tw-text-color-text-primary"
                            >
                              {{ row.metadata.creatorUserFullName }}
                            </span>
                            <span
                              class="tw-px-[0.4rem] tw-text-[1.4rem] tw-font-semibold tw-text-color-text-primary"
                              >|</span
                            >
                            <span
                              class="tw-text-[1.4rem] tw-font-medium tw-text-color-text-primary"
                            >
                              {{ row.metadata.creatorUserCustomerName }}
                            </span>
                          </ng-template>
                          <ng-template
                            name="pendingAssignTo"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <span
                              class="tw-text-[1.4rem] tw-font-semibold tw-text-color-text-primary"
                            >
                              {{ row.metadata.assigneeUserFullName }}
                            </span>
                            <span
                              class="tw-px-[0.4rem] tw-text-[1.4rem] tw-font-semibold tw-text-color-text-primary"
                              >|</span
                            >
                            <span
                              class="tw-text-[1.4rem] tw-font-medium tw-text-color-text-primary"
                            >
                              {{ row.metadata.assigneeUserCustomerName }}
                            </span>
                          </ng-template>
                          <ng-template
                            name="completedAssignBy"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <span
                              class="tw-text-[1.4rem] tw-font-semibold tw-text-color-text-primary"
                            >
                              {{ row.metadata.completedByUserFullName }}
                            </span>
                            <span
                              class="tw-px-[0.4rem] tw-text-[1.4rem] tw-font-semibold tw-text-color-text-primary"
                              >|</span
                            >
                            <span
                              class="tw-text-[1.4rem] tw-font-medium tw-text-color-text-primary"
                            >
                              {{ row.metadata.lastTodoUpdaterCustomerName }}
                            </span>
                          </ng-template>
                          <ng-template
                            name="closedAssignBy"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            <span
                              class="tw-text-[1.4rem] tw-font-semibold tw-text-color-text-primary"
                            >
                              @if (row.metadata.closedBySystem) {
                                <span i18n="@@todos.closedBy.system">
                                  System
                                </span>
                              } @else {
                                {{ row.metadata.lastTodoUpdaterFullName }}
                                <span class="tw-px-[0.4rem]">|</span>
                                <span class="tw-font-medium">
                                  {{ row.metadata.lastTodoUpdaterCustomerName }}
                                </span>
                              }
                            </span>
                          </ng-template>
                          <ng-template
                            name="completeManualToDoTemplate"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            @if (row.type === manualAssignmentType) {
                              <button
                                fin-button-action
                                [size]="finSize.M"
                                [actionType]="finButtonActionType.TERTIARY"
                                [shape]="finButtonShape.ROUND"
                                (click)="
                                  completeManualAssignment(row.id, $event)
                                "
                              >
                                <fin-icon
                                  name="check_circle"
                                  class="hover:tw-text-color-icons-success"
                                  [class.tw-text-color-icons-success]="
                                    completeToDoId === row.id
                                  "
                                  [size]="finSize.M"
                                ></fin-icon>
                              </button>
                            }
                          </ng-template>
                          <ng-template
                            name="actionMenuTemplate"
                            [finRowTemplate]="rows"
                            let-row
                          >
                            @if (row.type === manualAssignmentType) {
                              <button
                                fin-button-action
                                [finActionMenuTrigger]="finMenu.panel"
                                [size]="finSize.M"
                                [actionType]="finButtonActionType.TERTIARY"
                                [shape]="finButtonShape.RECTANGLE"
                                (click)="actionMenuClicked($event)"
                              >
                                <fin-icon
                                  name="more_vert"
                                  [size]="finSize.M"
                                ></fin-icon>
                              </button>
                              <fin-actions-menu
                                #finMenu="finActionMenu"
                                class="fin-w-[24rem]"
                              >
                                <button
                                  fin-menu-item
                                  iconName="close"
                                  [size]="finSize.M"
                                  (click)="cancelManualAssignment(row.id)"
                                >
                                  <ng-container
                                    i18n="@@button.label.cancel"
                                    finMenuItemTitle
                                    >Abbrechen</ng-container
                                  >
                                </button>
                              </fin-actions-menu>
                            }
                          </ng-template>
                        </ng-container>
                      </fin-table>
                    </div>
                  }
                  @if (isLoadingTodos$ | async) {
                    <div
                      class="tw-flex tw-justify-center tw-items-center tw-my-8"
                    >
                      <fin-loader></fin-loader>
                    </div>
                  }
                  @if (
                    (todosData$ | async)?.length < businessCase.taskCount &&
                    (isLoadingTodos$ | async) === false
                  ) {
                    <div
                      class="tw-flex tw-justify-center tw-items-center tw-my-4"
                    >
                      <button
                        type="button"
                        fin-button
                        [size]="finSize.S"
                        [appearance]="finButtonAppearance.STEALTH"
                        (click)="loadMoreTodos(businessCase.businessCaseId)"
                        i18n="@@todosManagement.loadMoreButton"
                      >
                        Mehr anzeigen
                        <fin-icon
                          name="keyboard_arrow_down"
                          [size]="finSize.S"
                        ></fin-icon>
                      </button>
                    </div>
                  }
                </ng-template>
              </fin-expansion-panel>
            }
          }
        </fin-accordion>
      </div>
    }
  }
}
