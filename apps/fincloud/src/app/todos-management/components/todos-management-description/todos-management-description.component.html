<div class="tw-text-color-text-primary tw-font-medium" finTruncateText>
  @if (userAssignment.type === userAssignmentType.PROVIDE_DATA) {
    <span i18n="@@todosManagement.summary.description.provideData">
      <PERSON><PERSON>n <PERSON> im Data Room Informationen für das Feld
      <span class="tw-font-semibold tw-leading-8">{{
        userAssignment?.metadata?.fieldInputRequestInformationLabel
      }}</span>
      bereit.
    </span>
  }

  @if (userAssignment.type === userAssignmentType.MANUAL_ASSIGNMENT) {
    {{ userAssignment?.metadata?.description }}
  }

  @if (userAssignment.type === userAssignmentType.REVIEW_APPLICATION) {
    <span i18n="@@todosManagement.summary.description.reviewApplicationFrom">
      Überprüfung des Antrags von
    </span>
    <span class="tw-font-semibold">
      {{ userAssignment?.metadata?.companyName }}
    </span>
  }

  @if (userAssignment.type === userAssignmentType.REVIEW_CONTRACT) {
    <span i18n="@@todosManagement.table.description.reviewContract.prefix">
      Prüfen Sie Vertrag
      <span class="tw-font-semibold"
        >"{{ userAssignment?.metadata?.contractDueDate }} /
        {{ userAssignment?.metadata?.contractName }}"
      </span>
    </span>
  }
</div>
