import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  OnInit,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, NonNullableFormBuilder } from '@angular/forms';
import {
  LayoutCommunicationService,
  SidebarLayoutSection,
} from '@fincloud/core/layout';
import { MultiselectOption } from '@fincloud/types/models';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import { tap, withLatestFrom } from 'rxjs';
import { TodosManagementPageActions } from '../../+state/actions';
import { todosManagementFeature } from '../../+state/reducers/todos-management.reducer';
import { UserAssignmentTypes } from '../../models/todo-assignment-type';
import { TodosBusinessCaseFilters } from '../../models/todos-business-case-filters';
import { SORTING_OPTIONS } from '../../utils/sorting-options';
import { TYPE_FILTER_LABELS } from '../../utils/type-filter-labels';

@Component({
  selector: 'app-todos-management-filters-panel',
  templateUrl: './todos-management-filters-panel.component.html',
  styleUrl: './todos-management-filters-panel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TodosManagementFiltersPanelComponent implements OnInit {
  @Output() toggleFiltersPanel = new EventEmitter();

  readonly finSize = FinSize;
  readonly sortingOptions = SORTING_OPTIONS;
  readonly sidebarLayoutSection = SidebarLayoutSection;
  readonly finButtonAppearance = FinButtonAppearance;
  assignmentTypes: MultiselectOption<UserAssignmentTypes>[] = [];
  typeFilterLabels = TYPE_FILTER_LABELS;

  filtersForm = this.fb.group({
    direction: this.fb.control(''),
    types: this.fb.array([true]),
  });

  availableAssignmentTypes$ = this.store
    .select(todosManagementFeature.selectAvailableAssignmentTypes)
    .pipe(
      tap((types) => {
        types.map((_, index) => {
          this.typesControl.setControl(index, this.fb.control(true));
        });
      }),
    );

  filters$ = this.store
    .select(todosManagementFeature.selectFilters)
    .pipe(tap((filters) => this.filtersForm.patchValue(filters)));

  get directionControl(): FormControl<string> {
    return this.filtersForm.controls.direction;
  }

  get typesControl(): FormArray<FormControl<boolean>> {
    return this.filtersForm.controls.types;
  }

  constructor(
    private store: Store,
    private fb: NonNullableFormBuilder,
    private destroyRef: DestroyRef,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  ngOnInit(): void {
    this.layoutCommunicationService.layoutClosed$
      .pipe(
        withLatestFrom(this.availableAssignmentTypes$, this.filters$),
        tap(([, types, filters]) => this.resetFilters(types, filters)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  onToggleFilters(): void {
    this.toggleFiltersPanel.emit();
  }

  onSubmit(
    types: UserAssignmentTypes[],
    currentFilters: TodosBusinessCaseFilters,
  ): void {
    const selectedFilters = this.filtersForm
      .value as Partial<TodosBusinessCaseFilters>;
    selectedFilters.assignmentTypes = types.filter(
      (_, i) => this.typesControl.at(i).value,
    );

    const filters = {
      ...currentFilters,
      ...selectedFilters,
    } as Partial<TodosBusinessCaseFilters>;
    if (!isEqual(currentFilters, filters)) {
      this.store.dispatch(
        TodosManagementPageActions.setBusinessCaseFilters({
          filters,
        }),
      );
    }
    this.onToggleFilters();
  }

  public resetFilters(
    types: UserAssignmentTypes[],
    filters: TodosBusinessCaseFilters,
  ): void {
    this.filtersForm.reset({
      direction: filters.direction,
      types: types.map((type) => filters.assignmentTypes.includes(type)),
    });
  }
}
