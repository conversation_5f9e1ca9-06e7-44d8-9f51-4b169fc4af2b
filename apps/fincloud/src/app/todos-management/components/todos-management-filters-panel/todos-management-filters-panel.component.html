<ng-template [appLayoutSection]="sidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL">
  @if (availableAssignmentTypes$ | async; as availableAssignmentTypes) {
    @if (filters$ | async; as filters) {
      <div
        class="tw-flex tw-justify-between tw-items-center tw-py-[1.2rem] tw-px-6 tw-border-b tw-border-color-border-default-primary tw-w-[33.6rem]"
      >
        <h2
          class="tw-text-body-1-strong tw-text-color-text-primary"
          i18n="@@dashboard.filters.title"
        >
          Filter
        </h2>
        <button
          fin-button-icon
          [appearance]="finButtonAppearance.INFORMATIVE"
          (click)="onToggleFilters()"
        >
          <fin-icon [size]="finSize.L" name="close"></fin-icon>
        </button>
      </div>

      <div
        [formGroup]="filtersForm"
        class="tw-px-[2.4rem] tw-my-[1.2rem] tw-flex tw-flex-col tw-gap-[1.2rem]"
      >
        <fin-card-label
          i18n-label="@@todosManagement.table.columns.type"
          label="Aufgabe"
        ></fin-card-label>

        @for (
          option of availableAssignmentTypes;
          track option;
          let index = $index
        ) {
          <fin-checkbox
            [size]="finSize.M"
            [formControl]="typesControl.at(index)"
            [label]="typeFilterLabels[option]"
          >
          </fin-checkbox>
        }

        <fin-card-label
          i18n-label="@@todosManagement.filters.sortBy"
          label="Sortieren nach"
        ></fin-card-label>
        <fin-dropdown
          i18n-label="@@todosManagement.filters.sortBy"
          label="Sortieren nach"
          formControlName="direction"
          [options]="sortingOptions"
          class="tw-font-bold"
        >
        </fin-dropdown>
      </div>

      <fin-footer class="tw-absolute tw-bottom-0" [size]="finSize.S">
        <button
          fin-button
          [size]="finSize.L"
          [appearance]="finButtonAppearance.SECONDARY"
          (click)="resetFilters(availableAssignmentTypes, filters)"
        >
          <span i18n="@@companyBranch.filter.clearAll">Zurücksetzen</span>
        </button>
        <button
          fin-button
          [size]="finSize.L"
          [appearance]="finButtonAppearance.PRIMARY"
          (click)="onSubmit(availableAssignmentTypes, filters)"
        >
          <span i18n="@@graph.graphFilters.button.label.apply">Anwenden</span>
        </button>
      </fin-footer>
    }
  }
</ng-template>
