@if (filters$ | async) {
  @if (routeParams$ | async) {
    <div
      class="tw-grid tw-grid-cols-3 tw-gap-6 tw-pt-[2rem] tw-px-[2.4rem] tw-bg-color-surface-primary"
    >
      @if (tabsCounters$ | async; as tabsCounters) {
        <fin-filter-tabs
          i18n-label="@@todosManagement.pending"
          label="Ausstehend"
          [count]="tabsCounters.pendingTasksCount ?? 0"
          [value]="todoStatuses.PENDING"
          [formControl]="todoStatus"
          (selectedTabChange)="changeStatusFilter($event)"
        ></fin-filter-tabs>
        <fin-filter-tabs
          i18n-label="@@todosManagement.completed"
          label="Erledigt"
          [count]="tabsCounters.completedTasksCount ?? 0"
          [value]="todoStatuses.COMPLETED"
          [formControl]="todoStatus"
          (selectedTabChange)="changeStatusFilter($event)"
        ></fin-filter-tabs>
        <fin-filter-tabs
          i18n-label="@@todosManagement.closed"
          label="Geschlossen"
          [count]="tabsCounters.canceledTasksCount ?? 0"
          [value]="todoStatuses.CLOSED"
          [formControl]="todoStatus"
          (selectedTabChange)="changeStatusFilter($event)"
        ></fin-filter-tabs>
      }
    </div>
    <div
      class="tw-grid tw-grid-cols-12 tw-gap-6 tw-px-[2.4rem] tw-py-8 tw-border-b tw-border-color-border-default-primary tw-bg-color-surface-primary"
      [formGroup]="search"
    >
      <fin-input formControlName="searchPhrase" class="tw-col-span-11">
        <ng-container finInputPrefix>
          <fin-icon name="search" [size]="finSize.L"></fin-icon>
        </ng-container>
      </fin-input>

      <button
        fin-button
        [disabled]="!(assignmentTypes$ | async).length"
        [shape]="finButtonShape.RECTANGLE"
        [appearance]="finButtonAppearance.INFORMATIVE"
        [size]="finSize.L"
        (click)="onToggleFilters()"
      >
        @if (filtersAppliedCounter$ | async) {
          <fin-badge-indicator
            class="tw-relative tw-left-[3.3rem] tw-bottom-[0.6rem]"
            [size]="finSize.XS"
            [type]="finBadgeType.ACTIVE"
          ></fin-badge-indicator>
        }
        <fin-icon name="filter_list"></fin-icon>
        <span
          class="tw-text-body-2-strong"
          i18n-label="@@companyGraph.graphFilters.title"
          >Filter</span
        >
        @if (filtersAppliedCounter$ | async; as filtersCounter) {
          <span>{{ filtersCounter }}</span>
        }
      </button>
    </div>
  }
}

<app-todos-management-filters-panel
  (toggleFiltersPanel)="onToggleFilters()"
></app-todos-management-filters-panel>
