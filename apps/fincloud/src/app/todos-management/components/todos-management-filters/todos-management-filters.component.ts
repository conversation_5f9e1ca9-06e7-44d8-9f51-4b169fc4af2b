import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder } from '@angular/forms';
import { LayoutCommunicationService } from '@fincloud/core/layout';
import { TodosStatus } from '@fincloud/types/enums';
import { FinBadgeType } from '@fincloud/ui/badges';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import isEqual from 'lodash-es/isEqual';
import {
  debounceTime,
  distinctUntilChanged,
  shareReplay,
  skip,
  tap,
} from 'rxjs';
import { TodosManagementPageActions } from '../../+state/actions';
import { todosManagementFeature } from '../../+state/reducers/todos-management.reducer';
import { TodosBusinessCaseFilters } from '../../models/todos-business-case-filters';

@Component({
  selector: 'app-todos-management-filters',
  templateUrl: './todos-management-filters.component.html',
  styleUrl: './todos-management-filters.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TodosManagementFiltersComponent implements OnInit {
  readonly todoStatuses = TodosStatus;
  readonly finSize = FinSize;
  readonly finButtonShape = FinButtonShape;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly finBadgeType = FinBadgeType;

  search = this.fb.group({
    searchPhrase: this.fb.control(''),
  });

  todoStatus = this.fb.control('');

  tabsCounters$ = this.store.select(
    todosManagementFeature.selectTodosTabsCounters,
  );

  assignmentTypes$ = this.store.select(
    todosManagementFeature.selectAvailableAssignmentTypes,
  );

  filters$ = this.store.select(todosManagementFeature.selectFilters).pipe(
    distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
    tap((filters) => {
      this.search.patchValue(filters);
    }),
  );

  filtersAppliedCounter$ = this.store
    .select(todosManagementFeature.selectFiltersAppliedCounter)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  routeParams$ = this.store
    .select(todosManagementFeature.selectTodoStatus)
    .pipe(
      tap((todoStatus) => {
        this.todoStatus.setValue(todoStatus);
      }),
    );

  constructor(
    private fb: FormBuilder,
    private store: Store,
    private destroyRef: DestroyRef,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  ngOnInit() {
    this.formValueChangeSubscriber();
  }

  changeStatusFilter(todoStatus: string) {
    this.store.dispatch(
      TodosManagementPageActions.changeUrlParams({
        todoStatus: todoStatus as TodosStatus,
      }),
    );
  }

  onToggleFilters(): void {
    this.layoutCommunicationService.toggleRightSideOverlayPanel();
  }

  private formValueChangeSubscriber() {
    this.search?.valueChanges
      .pipe(
        debounceTime(300),
        skip(1),
        distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
        tap((formValues) => {
          this.store.dispatch(
            TodosManagementPageActions.setBusinessCaseFilters({
              filters: formValues as Partial<TodosBusinessCaseFilters>,
            }),
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
