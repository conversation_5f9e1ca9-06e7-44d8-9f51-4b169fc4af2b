import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TodosStatus, TodosType } from '@fincloud/types/enums';
import { TodosManagementLayoutComponent } from './components/todos-management-layout/todos-management-layout.component';
import { checkTodoTypeGuard } from './guards/check-todo-type.guard';
import { checkTodoStatusGuard } from './guards/check-todos-status.guard';
import { findTodoPositionGuard } from './guards/find-todo-position.guard';
import { loadBusinessCasesGuard } from './guards/load-businesscases.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: TodosType.MY_TASKS,
    pathMatch: 'full',
  },
  {
    path: ':todoType',
    canActivate: [checkTodoTypeGuard, loadBusinessCasesGuard],
    children: [
      {
        path: '',
        redirectTo: TodosStatus.PENDING,
        pathMatch: 'full',
      },
      {
        path: ':todoStatus',
        canActivate: [checkTodoStatusGuard, loadBusinessCasesGuard],
        children: [
          {
            path: '',
            component: TodosManagementLayoutComponent,
          },
          {
            path: ':businessCaseId',
            children: [
              {
                path: ':todoId',
                canActivate: [findTodoPositionGuard],
                component: TodosManagementLayoutComponent,
              },
              {
                path: '',
                component: TodosManagementLayoutComponent,
              },
            ],
          },
        ],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TodosManagementRoutingModule {}
