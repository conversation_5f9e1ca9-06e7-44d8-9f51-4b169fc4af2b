import { TODOS_TABS_OPTIONS } from '@fincloud/neoshare/todos-management';
import { selectCustomerKey } from '@fincloud/state/customer';
import { selectRouteParams } from '@fincloud/state/router';
import { AssignmentsByStatusesCount } from '@fincloud/swagger-generator/internal-tools';
import { TodosStatus, TodosType, UserState } from '@fincloud/types/enums';
import { createSelector } from '@ngrx/store';
import { BaseSelectors } from '@ngrx/store/src/feature_creator';
import { isEmpty, isEqual, uniqBy } from 'lodash-es';
import { TodosManagementState } from '../../models/todos-management-state';
import { todoStatusApiMap } from '../../utils/todo-status-api-map';
import { TODOS_MANAGEMENT_INITIAL_STATE } from '../../utils/todos-management-initial-state';
import { TODOS_TABLE_CONFIG } from '../../utils/todos-tables-configs';

export const selectTodosManagementSelectors = (
  todosManagementFeature: BaseSelectors<
    'todosManagement',
    TodosManagementState
  >,
) => {
  const selectSidePanelFilterApplied = createSelector(
    todosManagementFeature.selectAvailableAssignmentTypes,
    todosManagementFeature.selectFilters,
    (availableAssignmentTypes, { assignmentTypes, direction }) => {
      const typesFilterApplied = !isEqual(
        availableAssignmentTypes,
        assignmentTypes,
      );
      const directionFilterApplied =
        direction !== TODOS_MANAGEMENT_INITIAL_STATE.filters.direction;
      return [typesFilterApplied, directionFilterApplied].filter(Boolean)
        .length;
    },
  );

  const selectFilterApplied = createSelector(
    selectSidePanelFilterApplied,
    todosManagementFeature.selectFilters,
    (filterApplied, { searchPhrase }) => {
      return filterApplied || searchPhrase;
    },
  );

  const selectTodosTabsCounters = createSelector(
    todosManagementFeature.selectCounters,
    todosManagementFeature.selectBusinessCases,
    selectRouteParams,
    selectFilterApplied,
    (counters, { totalTaskCount }, { todoStatus }, filterApplied) => {
      return {
        canceledTasksCount:
          todoStatus === TodosStatus.CLOSED && filterApplied
            ? totalTaskCount
            : (counters.canceledTasksCount || 0) +
              (counters.archivedTasksCount || 0),
        completedTasksCount:
          todoStatus === TodosStatus.COMPLETED && filterApplied
            ? totalTaskCount
            : counters.completedTasksCount,
        pendingTasksCount:
          todoStatus === TodosStatus.PENDING && filterApplied
            ? totalTaskCount
            : counters.pendingTasksCount,
      } as AssignmentsByStatusesCount;
    },
  );

  const selectSelectedTabIndex = createSelector(
    selectRouteParams,
    todosManagementFeature.selectCurrentRouteParams,
    ({ todoType }, { todoType: storedType }) => {
      const type = todoType ?? storedType;

      return TODOS_TABS_OPTIONS.findIndex(({ urlName }) => type === urlName);
    },
  );

  const selectTodoStatus = createSelector(
    selectRouteParams,
    todosManagementFeature.selectCurrentRouteParams,
    ({ todoStatus }, { todoStatus: storedStatus }) =>
      todoStatus ?? storedStatus,
  );

  const selectBusinessCaseIdParam = createSelector(
    selectRouteParams,
    ({ businessCaseId }) => businessCaseId,
  );

  const selectEmptyStateData = createSelector(
    selectRouteParams,
    todosManagementFeature.selectFilters,
    ({ todoStatus }, { searchPhrase }) => ({
      todoStatus,
      searchPhrase,
    }),
  );

  const selectTodoTableColumns = createSelector(
    selectRouteParams,
    ({ todoType, todoStatus }) => {
      return TODOS_TABLE_CONFIG[todoType as TodosType][
        todoStatus as TodosStatus
      ];
    },
  );

  const selectPerspective = createSelector(
    selectRouteParams,
    ({ todoType }) => {
      return TODOS_TABS_OPTIONS.find(({ urlName }) => urlName === todoType)
        ?.name;
    },
  );

  const selectBusinessCaseInfo = createSelector(
    todosManagementFeature.selectBusinessCases,
    (businessCases) => businessCases?.businessCasesInfo || [],
  );

  const selectAssignmentStatuses = createSelector(
    selectRouteParams,
    ({ todoStatus }) => todoStatusApiMap(todoStatus),
  );

  const selectParamsForTodosRequest = createSelector(
    todosManagementFeature.selectFilters,
    todosManagementFeature.selectTodosFilters,
    todosManagementFeature.selectTodos,
    selectAssignmentStatuses,
    todosManagementFeature.selectTodoPosition,
    selectPerspective,
    (
      filters,
      todosFilters,
      todos,
      assignmentStatuses,
      { data },
      perspective,
    ) => {
      let pagination = {
        limit: 5,
        offset: todos?.length || 0,
      };

      if (
        data?.assignmentOrderInGroup !== undefined &&
        todos?.length < data.assignmentOrderInGroup
      ) {
        const limit =
          data.assignmentOrderInGroup + 1 - todos.length > 5
            ? data.assignmentOrderInGroup + 1 - todos.length
            : 5;
        pagination = {
          limit,
          offset: todos.length,
        };
      }

      return {
        assignmentTypes: filters.assignmentTypes,
        perspective,
        assignmentStatuses,
        searchPhrase: filters.searchPhrase,
        ...todosFilters,
        ...pagination,
      };
    },
  );

  const selectBusinessCasePaginatorData = createSelector(
    todosManagementFeature.selectBusinessCasePaging,
    todosManagementFeature.selectBusinessCases,
    ({ limit, offset }, { totalGroupsCount }) => {
      return {
        totalItems: totalGroupsCount || 0,
        pageSize: limit,
        pageSizeOptions: [limit],
        pageNumber: offset / limit + 1 || 1,
      };
    },
  );

  const selectFirstTodo = createSelector(
    todosManagementFeature.selectTodos,
    (todos) => {
      return !isEmpty(todos) && todos.length > 0 ? todos[0] : {};
    },
  );

  const selectCompaniesAvailableInOrganization = createSelector(
    todosManagementFeature.selectCompaniesAvailableInOrganization,
    (companiesAvailableInOrganization) =>
      companiesAvailableInOrganization.map((company) => {
        return {
          label: company.companyInfo?.legalName,
          value: company,
        };
      }),
  );

  const selectCompanyCases = createSelector(
    todosManagementFeature.selectCompanyCases,
    (companyCases) =>
      companyCases.map((businessCase) => {
        return {
          label: businessCase.autoGeneratedBusinessCaseName,
          value: businessCase,
        };
      }),
  );

  const selectUsersWithAccessToTheCase = createSelector(
    todosManagementFeature.selectSelectedBusinessCaseId,
    todosManagementFeature.selectUsersWithAccessToCase,
    todosManagementFeature.selectPlatformManagersForCustomer,
    (businessCaseId, users, platformManagers) => {
      const activePlatformManagers = platformManagers.filter(
        (manager) => manager.userState === UserState.ACTIVE,
      );
      const allUsers = users.concat(activePlatformManagers).map((user) => {
        return {
          label: `${user.firstName} ${user.lastName}`,
          value: user,
        };
      });
      return businessCaseId ? uniqBy(allUsers, (user) => user.value.id) : [];
    },
  );

  const selectTodoPositionAndCustomerKey = createSelector(
    todosManagementFeature.selectTodoPosition,
    selectCustomerKey,
    (todoPosition, customerKey) => ({
      todoPosition,
      customerKey,
    }),
  );

  const selectIsTodoPositionLoaded = createSelector(
    selectRouteParams,
    todosManagementFeature.selectTodoPosition,
    (routeParams, { isLoading, data }) => {
      return {
        shouldWaitToFindTodoPosition: routeParams?.todoId !== undefined,
        isTodoPositionLoaded: !isLoading && !isEmpty(data),
      };
    },
  );

  const selectFiltersAppliedCounter = createSelector(
    selectSidePanelFilterApplied,
    (filtersCounter) => (filtersCounter ? `(${filtersCounter})` : ''),
  );

  return {
    selectTodosTabsCounters,
    selectSelectedTabIndex,
    selectTodoStatus,
    selectEmptyStateData,
    selectTodoTableColumns,
    selectPerspective,
    selectBusinessCaseInfo,
    selectParamsForTodosRequest,
    selectBusinessCasePaginatorData,
    selectFirstTodo,
    selectCompaniesAvailableInOrganization,
    selectCompanyCases,
    selectUsersWithAccessToTheCase,
    selectTodoPositionAndCustomerKey,
    selectIsTodoPositionLoaded,
    selectFiltersAppliedCounter,
    selectAssignmentStatuses,
    selectBusinessCaseIdParam,
  };
};
