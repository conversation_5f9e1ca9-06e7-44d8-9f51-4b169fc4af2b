import { HttpErrorResponse } from '@angular/common/http';
import { User } from '@fincloud/swagger-generator/authorization-server';
import { ParticipantUserDto } from '@fincloud/swagger-generator/business-case-manager';
import { Company } from '@fincloud/swagger-generator/company';
import {
  AssignmentsByStatusesCount,
  UserAssignment,
  UserAssignmentGroupPositionResult,
  UserAssignmentResponse,
} from '@fincloud/swagger-generator/internal-tools';
import { BusinessCaseCompanyAnalysis } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';
import { UserAssignmentTypes } from '../../models/todo-assignment-type';

export const getTodosSuccess = createAction(
  '[Todos API] Request todos for business case Success',
  props<{ todos: UserAssignment[] }>(),
);

export const getTodosFailure = createAction(
  '[Todos API] Request todos for business case Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const getMoreTodosSuccess = createAction(
  '[Todos API] Request next todos page for current business case Success',
  props<{ todos: UserAssignment[] }>(),
);

export const getMoreTodosFailure = createAction(
  '[Todos API] Request next todos page for current business case Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const getBusinessCasesSuccess = createAction(
  '[Todos API] Get Business Cases Success',
  props<{ businessCases: UserAssignmentResponse }>(),
);

export const getBusinessCasesFailure = createAction(
  '[Todos API] Get Business cases Failure',
);

export const getAssignmentsCountSuccess = createAction(
  '[Todos API] Get counts for all cases Success',
  props<{
    counters: Partial<AssignmentsByStatusesCount>;
  }>(),
);

export const getAssignmentsCountFailure = createAction(
  '[Todos API] Get counts for all cases Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const getCompaniesAvailableInOrganizationSuccess = createAction(
  '[Todos API] Request companies available in the organization Success',
  props<{ companies: Company[] }>(),
);

export const getCompaniesAvailableInOrganizationFailure = createAction(
  '[Todos API] Request companies available in the organization Failure',
);

export const getCompanyCasesSuccess = createAction(
  '[Todos API] Request cases for a company Success',
  props<{ cases: BusinessCaseCompanyAnalysis[] }>(),
);

export const getCompanyCasesFailure = createAction(
  '[Todos API] Request cases for a company Failure',
);

export const getUsersWithAccessToCaseSuccess = createAction(
  '[Todos API] Request users with access to case Success',
  props<{ users: ParticipantUserDto[] }>(),
);

export const getUsersWithAccessToCaseFailure = createAction(
  '[Todos API] Request users with access to case Failure',
);

export const getPlatformManagersForCustomerSuccess = createAction(
  '[Todos API] Request platform managers for a customer Success',
  props<{ platformManagers: User[] }>(),
);

export const getPlatformManagersForCustomerFailure = createAction(
  '[Todos API] Request platform managers for a customer Failure',
);

export const createToDoSuccess = createAction(
  '[Todos API] Create To Do Success',
  props<{ businessCaseId: string }>(),
);

export const completeToDoSuccess = createAction(
  '[Todos API] Complete To Do Success',
);

export const getAvailableAssignmentTypesSuccess = createAction(
  '[Todos API] Get available assignment types Success',
  props<{
    types: UserAssignmentTypes[];
  }>(),
);

export const getAvailableAssignmentTypesFailure = createAction(
  '[Todos API] Get available assignment types Failure',
  props<{
    types: [];
  }>(),
);

export const cancelToDoSuccess = createAction(
  '[Todos API] Cancel to do Success',
);

export const toDoActionFailure = createAction(
  '[Todos API] to do Action Failure',
);

export const updateTodoSuccess = createAction(
  '[Todos API] Update to do Success',
  props<{ businessCaseId: string }>(),
);

export const updateTodoFailure = createAction(
  '[Todos API] Update to do Action Failure',
);

export const findTodoPositionSuccess = createAction(
  '[Todos API] Find todo page position Success',
  props<{
    todoPosition: UserAssignmentGroupPositionResult;
  }>(),
);

export const findTodoPositionFailure = createAction(
  '[Todos API] Find todo page position Failure',
  props<{ error: HttpErrorResponse }>(),
);
