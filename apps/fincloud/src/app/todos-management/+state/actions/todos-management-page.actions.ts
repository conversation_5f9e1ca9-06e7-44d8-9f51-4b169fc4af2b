import { UserAssignment } from '@fincloud/swagger-generator/internal-tools';
import { TodosApiPerspective, TodosStatus } from '@fincloud/types/enums';
import { createAction, props } from '@ngrx/store';
import { User } from '@sentry/angular';
import { TodoSubmitModalData } from '../../models/todo-submit-modal-data';
import { TodosBusinessCaseFilters } from '../../models/todos-business-case-filters';
import { TodosFilters } from '../../models/todos-filters';

export const getTodos = createAction(
  '[Todos Page] Request todos for business case',
  props<{
    businessCaseId: string;
  }>(),
);

export const getMoreTodos = createAction(
  '[Todos Page] Request next todos page for current business case',
  props<{
    businessCaseId: string;
  }>(),
);

export const setTodoOrderBy = createAction(
  '[Todos Page] Change todos order by',
  props<{
    businessCaseId: string;
    filters: Partial<TodosFilters>;
  }>(),
);

export const setBusinessCaseFilters = createAction(
  '[Todos Page] Change business case filters',
  props<{
    filters: Partial<TodosBusinessCaseFilters>;
  }>(),
);

export const changeUrlParams = createAction(
  '[Todos Page] Change URL params',
  props<{ todoType?: TodosApiPerspective; todoStatus: TodosStatus }>(),
);

export const getAssignmentsCount = createAction(
  '[Todos Page] Get counts for all cases',
);

export const changeBusinessCasePage = createAction(
  '[Todos Page] Change business cases page',
  props<{
    pageNumber: number;
  }>(),
);

export const openCreateToDoModal = createAction(
  '[Todos Page] Open create to do modal',
);

export const openViewToDoModal = createAction(
  '[Todos Page] Open view to do modal',
  props<{
    todo: UserAssignment;
  }>(),
);

export const openUpdateToDoModal = createAction(
  '[Todos Page] Open update to do modal',
  props<{
    todo: UserAssignment;
  }>(),
);

export const openCancelToDoModal = createAction(
  '[Todos Page] Open cancel to do modal',
  props<{
    userAssignmentId: string;
  }>(),
);

export const createToDo = createAction(
  '[Todos Page] Close create to do modal',
  props<{
    modalData: TodoSubmitModalData<User[]>;
  }>(),
);

export const updateToDo = createAction(
  '[Todos Page] Close update to do modal',
  props<{
    modalData: TodoSubmitModalData<User>;
  }>(),
);
export const cancelToDoModalClosed = createAction(
  '[Todos Page] Close cancel to do modal',
  props<{
    userAssignmentId: string;
  }>(),
);

export const getCompanyCases = createAction(
  '[Todos Page] Request cases for a company',
  props<{
    companyId: string;
  }>(),
);

export const getPlatformManagersForCustomer = createAction(
  '[Todos Page] Request platform managers for a customer',
);

export const clearCompanyCases = createAction(
  '[Todos Page] Clear the company cases',
);

export const setSelectedBusinessCase = createAction(
  '[Todos Page] Set selected business case',
  props<{
    businessCaseId: string;
  }>(),
);

export const completeTodo = createAction(
  '[Todos Page] Complete to do',
  props<{
    userAssignmentId: string;
  }>(),
);

export const findTodoPosition = createAction(
  '[Todos Page] Find todo page position',
);

export const getAvailableAssignmentTypes = createAction(
  '[Todos Page] Get available assignment types',
);
