import { StateLibTodosManagementPageActions } from '@fincloud/state/todos-management';
import { User } from '@fincloud/swagger-generator/authorization-server';
import { ParticipantUserDto } from '@fincloud/swagger-generator/business-case-manager';
import { Company } from '@fincloud/swagger-generator/company';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { createFeature, createReducer, on } from '@ngrx/store';
import { TodosModalType } from '../../enums/todos-modal-type';
import { TodosManagementState } from '../../models/todos-management-state';
import { TODOS_MANAGEMENT_INITIAL_STATE } from '../../utils/todos-management-initial-state';
import {
  TodosManagementApiActions,
  TodosManagementPageActions,
} from '../actions';
import { selectTodosManagementSelectors } from '../selectors/todos-management.selectors';

export const todosManagementFeature = createFeature({
  name: 'todosManagement',
  reducer: createReducer(
    TODOS_MANAGEMENT_INITIAL_STATE,
    on(
      TodosManagementApiActions.getBusinessCasesSuccess,
      (state, { businessCases }): TodosManagementState => {
        return {
          ...state,
          businessCases,
          isLoadingBusinessCases: false,
        };
      },
    ),
    on(
      TodosManagementApiActions.getBusinessCasesFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          businessCases: TODOS_MANAGEMENT_INITIAL_STATE.businessCases,
          isLoadingBusinessCases: false,
        };
      },
    ),
    on(
      TodosManagementPageActions.setBusinessCaseFilters,
      (state, { filters }): TodosManagementState => {
        return {
          ...state,
          filters: {
            ...state.filters,
            ...filters,
          },
          businessCasePaging: TODOS_MANAGEMENT_INITIAL_STATE.businessCasePaging,
          businessCases: TODOS_MANAGEMENT_INITIAL_STATE.businessCases,
          todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
          isLoadingBusinessCases: true,
        };
      },
    ),
    on(
      TodosManagementPageActions.setTodoOrderBy,
      (state, { filters }): TodosManagementState => {
        return {
          ...state,
          todosFilters: {
            ...state.todosFilters,
            ...filters,
          },
          todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
        };
      },
    ),
    on(
      TodosManagementApiActions.getAssignmentsCountSuccess,
      (state, counters): TodosManagementState => {
        return {
          ...state,
          counters: counters.counters,
        };
      },
    ),
    on(
      TodosManagementPageActions.changeBusinessCasePage,
      (state, { pageNumber }): TodosManagementState => {
        return {
          ...state,
          businessCasePaging: {
            ...state.businessCasePaging,
            offset: state.businessCasePaging.limit * (pageNumber - 1),
          },
        };
      },
    ),
    on(TodosManagementPageActions.getTodos, (state): TodosManagementState => {
      return {
        ...state,
        isLoadingTodos: true,
        todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
      };
    }),
    on(
      TodosManagementApiActions.updateTodoSuccess,
      (state): TodosManagementState => {
        return {
          ...state,
          isLoadingTodos: true,
          todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
        };
      },
    ),
    on(
      TodosManagementApiActions.getTodosSuccess,
      (state, { todos }): TodosManagementState => {
        return {
          ...state,
          todos,
          todoPosition: TODOS_MANAGEMENT_INITIAL_STATE.todoPosition,
          isLoadingTodos: false,
        };
      },
    ),
    on(
      TodosManagementApiActions.getTodosFailure,
      TodosManagementApiActions.getMoreTodosFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          isLoadingTodos: false,
        };
      },
    ),
    on(
      TodosManagementPageActions.getMoreTodos,
      (state): TodosManagementState => {
        return {
          ...state,
          isLoadingTodos: true,
        };
      },
    ),
    on(
      TodosManagementApiActions.getMoreTodosSuccess,
      (state, { todos }): TodosManagementState => {
        return {
          ...state,
          todos: [...(state.todos || []), ...(todos || [])],
          isLoadingTodos: false,
        };
      },
    ),
    on(
      StateLibTodosManagementPageActions.setCurrentRouteParams,
      (state, { currentRouteParams }): TodosManagementState => {
        return {
          ...state,
          currentRouteParams,
        };
      },
    ),
    on(
      TodosManagementApiActions.getCompaniesAvailableInOrganizationSuccess,
      (state, { companies }): TodosManagementState => {
        return {
          ...state,
          companiesAvailableInOrganization: companies,
        };
      },
    ),
    on(
      TodosManagementApiActions.getCompaniesAvailableInOrganizationFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          companiesAvailableInOrganization: [] as Company[],
        };
      },
    ),
    on(
      TodosManagementApiActions.getCompanyCasesSuccess,
      (state, { cases }): TodosManagementState => {
        return {
          ...state,
          companyCases: cases,
        };
      },
    ),
    on(
      TodosManagementApiActions.getCompanyCasesFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          companyCases: [] as ExchangeBusinessCase[],
        };
      },
    ),
    on(
      TodosManagementPageActions.clearCompanyCases,
      TodosManagementPageActions.openCreateToDoModal,
      (state): TodosManagementState => {
        return {
          ...state,
          companyCases: [] as ExchangeBusinessCase[],
          selectedBusinessCaseId: '',
        };
      },
    ),
    on(
      TodosManagementPageActions.getMoreTodos,
      TodosManagementPageActions.setSelectedBusinessCase,
      (state, { businessCaseId }): TodosManagementState => {
        return {
          ...state,
          selectedBusinessCaseId: businessCaseId,
        };
      },
    ),
    on(TodosManagementPageActions.createToDo, (state): TodosManagementState => {
      return {
        ...state,
        companiesAvailableInOrganization: [] as Company[],
        selectedBusinessCaseId: '',
        companyCases: [] as ExchangeBusinessCase[],
      };
    }),
    on(
      TodosManagementApiActions.getUsersWithAccessToCaseSuccess,
      (state, { users }): TodosManagementState => {
        return {
          ...state,
          usersWithAccessToCase: users,
        };
      },
    ),
    on(
      TodosManagementPageActions.changeUrlParams,
      (state): TodosManagementState => {
        return {
          ...state,
          isLoadingTodos: true,
          todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
        };
      },
    ),
    on(
      TodosManagementApiActions.getUsersWithAccessToCaseFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          usersWithAccessToCase: [] as ParticipantUserDto[],
        };
      },
    ),
    on(
      TodosManagementApiActions.getAvailableAssignmentTypesSuccess,
      TodosManagementApiActions.getAvailableAssignmentTypesFailure,
      (state, { types }): TodosManagementState => {
        return {
          ...state,
          filters: {
            ...TODOS_MANAGEMENT_INITIAL_STATE.filters,
            assignmentTypes: types,
          },
          // businessCasePaging: TODOS_MANAGEMENT_INITIAL_STATE.businessCasePaging,
          businessCases: TODOS_MANAGEMENT_INITIAL_STATE.businessCases,
          todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
          isLoadingTodos: true,
          isLoadingBusinessCases: true,
          availableAssignmentTypes: types,
        };
      },
    ),
    on(
      TodosManagementApiActions.getPlatformManagersForCustomerSuccess,
      (state, { platformManagers }): TodosManagementState => {
        return {
          ...state,
          platformManagersForCustomer: platformManagers,
        };
      },
    ),
    on(
      TodosManagementApiActions.getPlatformManagersForCustomerFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          platformManagersForCustomer: [] as User[],
        };
      },
    ),
    on(
      TodosManagementApiActions.createToDoSuccess,
      (state): TodosManagementState => {
        return {
          ...state,
          counters: {
            ...state.counters,
            pendingTasksCount: state.counters.pendingTasksCount + 1,
          },
          todosFilters: TODOS_MANAGEMENT_INITIAL_STATE.todosFilters,
          todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
          isLoadingBusinessCases: true,
          businessCases: TODOS_MANAGEMENT_INITIAL_STATE.businessCases,
        };
      },
    ),
    on(
      TodosManagementApiActions.completeToDoSuccess,
      (state): TodosManagementState => {
        return {
          ...state,
          counters: {
            ...state.counters,
            pendingTasksCount: state.counters.pendingTasksCount - 1,
            completedTasksCount: state.counters.completedTasksCount + 1,
          },
          todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
          todosFilters: TODOS_MANAGEMENT_INITIAL_STATE.todosFilters,
          isLoadingBusinessCases: true,
          businessCases: TODOS_MANAGEMENT_INITIAL_STATE.businessCases,
        };
      },
    ),
    on(
      TodosManagementApiActions.cancelToDoSuccess,
      (state): TodosManagementState => {
        return {
          ...state,
          counters: {
            ...state.counters,
            pendingTasksCount: state.counters.pendingTasksCount - 1,
            canceledTasksCount: state.counters.canceledTasksCount + 1,
          },
          todos: TODOS_MANAGEMENT_INITIAL_STATE.todos,
          todosFilters: TODOS_MANAGEMENT_INITIAL_STATE.todosFilters,
          isLoadingBusinessCases: true,
          businessCases: TODOS_MANAGEMENT_INITIAL_STATE.businessCases,
        };
      },
    ),
    on(
      TodosManagementPageActions.findTodoPosition,
      (state): TodosManagementState => {
        return {
          ...state,
          todoPosition: {
            data: TODOS_MANAGEMENT_INITIAL_STATE.todoPosition.data,
            isLoading: true,
          },
        };
      },
    ),
    on(
      TodosManagementApiActions.findTodoPositionSuccess,
      (state, { todoPosition }): TodosManagementState => {
        return {
          ...state,
          todoPosition: {
            ...state.todoPosition,
            data: todoPosition,
            isLoading: false,
          },
          businessCasePaging: {
            ...state.businessCasePaging,
            offset:
              todoPosition.groupPageNumber * state.businessCasePaging.limit,
          },
        };
      },
    ),
    on(
      TodosManagementApiActions.findTodoPositionFailure,
      (state): TodosManagementState => {
        return {
          ...state,
          todoPosition: {
            ...state.todoPosition,
            data: TODOS_MANAGEMENT_INITIAL_STATE.todoPosition.data,
            isLoading: false,
          },
        };
      },
    ),
    on(
      TodosManagementPageActions.openViewToDoModal,
      (state): TodosManagementState => {
        return {
          ...state,
          modalType: TodosModalType.VIEW,
        };
      },
    ),
    on(
      TodosManagementPageActions.openUpdateToDoModal,
      (state): TodosManagementState => {
        return {
          ...state,
          modalType: TodosModalType.UPDATE,
        };
      },
    ),
    on(
      TodosManagementPageActions.openCreateToDoModal,
      (state): TodosManagementState => {
        return {
          ...state,
          modalType: TodosModalType.CREATE,
        };
      },
    ),
  ),
  extraSelectors: selectTodosManagementSelectors,
});
