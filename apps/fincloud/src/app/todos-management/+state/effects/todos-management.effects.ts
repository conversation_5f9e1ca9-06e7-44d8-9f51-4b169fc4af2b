import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';

import { Router } from '@angular/router';

import { ConfirmationModalComponent } from '@fincloud/components/modals';
import { Toast } from '@fincloud/core/toast';
import { CompanyAnalysisService } from '@fincloud/neoshare/company-analysis';
import { TODOS_TABS_OPTIONS } from '@fincloud/neoshare/todos-management';
import { selectCustomerKey } from '@fincloud/state/customer';
import {
  selectRouteCustomerKey,
  selectRouteParams,
} from '@fincloud/state/router';
import {
  User,
  UserManagementControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import {
  BusinessCase,
  BusinessCaseControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import { CompanyControllerService } from '@fincloud/swagger-generator/company';
import { UserAssignmentControllerService } from '@fincloud/swagger-generator/internal-tools';
import {
  TodosApiPerspective,
  TodosType,
  UserRole,
} from '@fincloud/types/enums';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, filter, map, of, switchMap, tap } from 'rxjs';
import { TodosManagementManualToDoModalComponent } from '../../components/todos-management-manual-to-do-modal/todos-management-manual-to-do-modal.component';
import { UserAssignmentTypes } from '../../models/todo-assignment-type';
import { todoStatusApiMap } from '../../utils/todo-status-api-map';
import {
  TodosManagementApiActions,
  TodosManagementPageActions,
} from '../actions';
import { todosManagementFeature } from '../reducers/todos-management.reducer';

@Injectable()
export class TodosManagementEffects {
  private readonly toDoCreatedSuccessMessage = $localize`:@@todosManagement.createToDoModal.toast.success:To-do erfolgreich erstellt`;
  private readonly toDoCompletedSuccessMessage = $localize`:@@todosManagement.completeToDo.toast.success:To-do erfolgreich erledigt`;
  private readonly toDoCanceledSuccessMessage = $localize`:@@todosManagement.cancelToDo.toast.success:To-do erfolgreich storniert`;
  private readonly toDoErrorMessage = $localize`:@@todosManagement.toDo.toast.error:Ein Fehler ist aufgetreten. Versuchen Sie es bitte erneut`;
  private readonly todoUpdatedSuccessMessage = $localize`:@@todosManagement.toDo.toast.success: To-do erfolgreich aktualisiert`;
  private readonly todoUpdatedErrorMessage = $localize`:@@todosManagement.toDo.update.toast.error:Ein Fehler ist aufgetreten`;
  constructor(
    private actions$: Actions,
    private store: Store,
    private userAssignmentService: UserAssignmentControllerService,
    private finModalService: FinModalService,
    private companyControllerService: CompanyControllerService,
    private companyAnalysisService: CompanyAnalysisService,
    private businessCaseControllerService: BusinessCaseControllerService,
    private userManagementControllerService: UserManagementControllerService,
    private finToastService: FinToastService,
    private router: Router,
  ) {}

  changeUrlParams$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementPageActions.changeUrlParams),
        concatLatestFrom(() => [
          this.store.select(selectRouteParams),
          this.store.select(selectRouteCustomerKey),
        ]),
        tap(
          ([
            { todoStatus, todoType },
            { todoType: routerTodoType },
            customerKey,
          ]) => {
            const type =
              TODOS_TABS_OPTIONS.find(({ name }) => name === todoType)
                ?.urlName ?? routerTodoType;

            this.router.navigate([
              customerKey,
              'todos-management',
              type,
              todoStatus,
            ]);
          },
        ),
      ),
    { dispatch: false },
  );

  getTodosCounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        TodosManagementPageActions.getAvailableAssignmentTypes,
        TodosManagementPageActions.setBusinessCaseFilters,
      ),
      concatLatestFrom(() => [
        this.store.select(todosManagementFeature.selectPerspective),
      ]),
      switchMap(([, perspective]) => {
        return this.userAssignmentService
          .getAssignmentCountByUserIdAndStatus({
            perspective,
          })
          .pipe(
            map((counters) => {
              return TodosManagementApiActions.getAssignmentsCountSuccess({
                counters,
              });
            }),
          );
      }),
      catchError((err) =>
        of(TodosManagementApiActions.getAssignmentsCountFailure(err)),
      ),
    ),
  );

  getBusinessCases$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        TodosManagementApiActions.getAvailableAssignmentTypesSuccess,
        TodosManagementPageActions.setBusinessCaseFilters,
        TodosManagementPageActions.changeBusinessCasePage,
      ),
      concatLatestFrom(() => [
        this.store.select(todosManagementFeature.selectFilters),
        this.store.select(todosManagementFeature.selectBusinessCasePaging),
        this.store.select(todosManagementFeature.selectAssignmentStatuses),
        this.store.select(todosManagementFeature.selectPerspective),
      ]),
      switchMap(
        ([, filters, businessCasePaging, assignmentStatuses, perspective]) => {
          const params = {
            perspective,
            assignmentStatuses,
            ...businessCasePaging,
            ...filters,
          };

          return this.userAssignmentService
            .getBusinessCasesInfoByUserAssignmentLastUpdatedDate(params)
            .pipe(
              map((businessCases) =>
                TodosManagementApiActions.getBusinessCasesSuccess({
                  businessCases,
                }),
              ),
              catchError(() => {
                return of(TodosManagementApiActions.getBusinessCasesFailure());
              }),
            );
        },
      ),
    ),
  );

  addBusinessCaseIdToRoute$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementPageActions.getTodos),
        concatLatestFrom(() => [
          this.store.select(selectRouteCustomerKey),
          this.store.select(selectRouteParams),
        ]),
        filter(
          ([actionData, , { businessCaseId }]) =>
            actionData.businessCaseId !== businessCaseId,
        ),
        tap(([{ businessCaseId }, customerKey, { todoType, todoStatus }]) => {
          this.router.navigate([
            customerKey,
            'todos-management',
            todoType,
            todoStatus,
            businessCaseId,
          ]);
        }),
      ),
    { dispatch: false },
  );

  getTodosForBusinessCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        TodosManagementPageActions.getTodos,
        TodosManagementPageActions.setTodoOrderBy,
        TodosManagementApiActions.updateTodoSuccess,
        TodosManagementApiActions.createToDoSuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(todosManagementFeature.selectParamsForTodosRequest),
        this.store.select(todosManagementFeature.selectBusinessCaseIdParam),
      ]),
      filter(
        ([{ businessCaseId }, , businessCaseIdParam]) =>
          businessCaseId === businessCaseIdParam,
      ),
      switchMap(([{ businessCaseId }, paramsForTodosRequest]) => {
        const params = {
          businessCaseId,
          ...paramsForTodosRequest,
        };

        return this.userAssignmentService
          .getUserAssignmentsForUserIdAndBusinessCaseId(params)
          .pipe(
            map((todos) =>
              TodosManagementApiActions.getTodosSuccess({
                todos,
              }),
            ),
            catchError((error) =>
              of(TodosManagementApiActions.getTodosFailure({ error })),
            ),
          );
      }),
    ),
  );

  getMoreTodosForBusinessCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.getMoreTodos),
      concatLatestFrom(() => [
        this.store.select(todosManagementFeature.selectParamsForTodosRequest),
        this.store.select(todosManagementFeature.selectSelectedBusinessCaseId),
      ]),
      switchMap(([, paramsForTodosRequest, businessCaseId]) => {
        const params = {
          businessCaseId,
          ...paramsForTodosRequest,
        };

        return this.userAssignmentService
          .getUserAssignmentsForUserIdAndBusinessCaseId(params)
          .pipe(
            map((todos) =>
              TodosManagementApiActions.getMoreTodosSuccess({
                todos,
              }),
            ),
            catchError((error) =>
              of(TodosManagementApiActions.getMoreTodosFailure({ error })),
            ),
          );
      }),
    ),
  );

  openCreateModal$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementPageActions.openCreateToDoModal),
        switchMap(() => {
          return this.finModalService
            .open(TodosManagementManualToDoModalComponent, {
              size: FinSize.L,
              disableClose: true,
            })
            .afterClosed();
        }),
      ),
    { dispatch: false },
  );

  openViewOrUpdateToDoModal$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          TodosManagementPageActions.openViewToDoModal,
          TodosManagementPageActions.openUpdateToDoModal,
        ),
        switchMap(({ todo }) => {
          return this.finModalService
            .open(TodosManagementManualToDoModalComponent, {
              size: FinSize.L,
              disableClose: true,
              data: todo,
            })
            .afterClosed();
        }),
      ),
    { dispatch: false },
  );

  openCancelToDoModal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.openCancelToDoModal),
      switchMap(({ userAssignmentId }) => {
        return this.finModalService
          .open<string, ConfirmationModalComponent>(
            ConfirmationModalComponent,
            {
              data: {
                userAssignmentId: userAssignmentId,
                title: $localize`:@@todosManagement.cancelToModal.label:Möchten Sie dieses To-do stornieren?`,
                confirmButtonAppearance: FinButtonAppearance.PRIMARY,
                confirmLabel: $localize`:@@button.label.continue:Fortfahren`,
                cancelLabel: $localize`:"@@button.label.cancel:Abbrechen`,
                svgIcon: 'svgCloseCaseComposite',
                size: FinSize.L,
                attention: true,
              },
              disableClose: true,
            },
          )
          .afterClosed()
          .pipe(
            filter(Boolean),
            map(() =>
              TodosManagementPageActions.cancelToDoModalClosed({
                userAssignmentId,
              }),
            ),
          );
      }),
    ),
  );

  getCompaniesAvailableInOrganization$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.openCreateToDoModal),
      switchMap(() => {
        return this.companyControllerService
          .getCompaniesForCustomer({
            showInactiveCompanies: false,
          })
          .pipe(
            map((companies) =>
              TodosManagementApiActions.getCompaniesAvailableInOrganizationSuccess(
                { companies },
              ),
            ),
            catchError(() =>
              of(
                TodosManagementApiActions.getCompaniesAvailableInOrganizationFailure(),
              ),
            ),
          );
      }),
    ),
  );

  getCompanyCases$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.getCompanyCases),
      switchMap(({ companyId }) => {
        return this.companyAnalysisService
          .loadCompanyBusinessCases(companyId)
          .pipe(
            map((cases) => {
              return TodosManagementApiActions.getCompanyCasesSuccess({
                cases,
              });
            }),
            catchError(() =>
              of(TodosManagementApiActions.getCompanyCasesFailure()),
            ),
          );
      }),
    ),
  );

  getUsersWithAccessToCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.setSelectedBusinessCase),
      filter(({ businessCaseId }) => !!businessCaseId),
      switchMap(({ businessCaseId }) => {
        return this.businessCaseControllerService
          .getUsersParticipantsInBusinessCase({ businessCaseId })
          .pipe(
            map((users) => {
              return TodosManagementApiActions.getUsersWithAccessToCaseSuccess({
                users,
              });
            }),
            catchError(() =>
              of(TodosManagementApiActions.getUsersWithAccessToCaseFailure()),
            ),
          );
      }),
    ),
  );

  getPlatformManagersForCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.getPlatformManagersForCustomer),
      concatLatestFrom(() => this.store.select(selectCustomerKey)),
      switchMap(([, customerKey]) => {
        return this.userManagementControllerService
          .getAllUsersForCustomer({
            customerKey,
            filterByRole: [UserRole.PLATFORM_MANAGER],
          })
          .pipe(
            map((platformManagers) => {
              return TodosManagementApiActions.getPlatformManagersForCustomerSuccess(
                { platformManagers },
              );
            }),
            catchError(() =>
              of(
                TodosManagementApiActions.getPlatformManagersForCustomerFailure(),
              ),
            ),
          );
      }),
    ),
  );

  createToDo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.createToDo),
      switchMap(({ modalData }) => {
        const userIds = (modalData.assignee as User[]).map((user) => user.id);
        const businessCaseId = (modalData.case as BusinessCase).id;
        return this.userAssignmentService
          .createManualUserAssignments({
            businessCaseId,
            body: {
              description: modalData.description,
              dueDate: modalData.dueDate,
              userIds,
            },
          })
          .pipe(
            map(() => {
              return TodosManagementApiActions.createToDoSuccess({
                businessCaseId,
              });
            }),
            catchError(() => of(TodosManagementApiActions.toDoActionFailure())),
          );
      }),
    ),
  );

  cancelToDo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.cancelToDoModalClosed),
      filter(({ userAssignmentId }) => !!userAssignmentId),
      switchMap(({ userAssignmentId }) => {
        return this.userAssignmentService
          .cancelManualUserAssignment({
            userAssignmentId,
          })
          .pipe(
            map(() => {
              return TodosManagementApiActions.cancelToDoSuccess();
            }),
            catchError(() => of(TodosManagementApiActions.toDoActionFailure())),
          );
      }),
    ),
  );
  updateTodo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.updateToDo),
      switchMap(({ modalData }) => {
        return this.userAssignmentService
          .updateManualUserAssignments({
            businessCaseId: modalData.case,
            userAssignmentId: modalData.userAssignmentId,
            body: {
              description: modalData.description,
              dueDate: modalData.dueDate,
              assigneeId: (modalData.assignee as User).id,
            },
          })
          .pipe(
            map(() => {
              return TodosManagementApiActions.updateTodoSuccess({
                businessCaseId: modalData.case as string,
              });
            }),
            catchError(() => of(TodosManagementApiActions.updateTodoFailure())),
          );
      }),
    ),
  );

  showUpdateTodoSuccessMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementApiActions.updateTodoSuccess),
        tap(() => {
          this.finToastService.show(
            Toast.success(this.todoUpdatedSuccessMessage),
          );
        }),
      ),
    { dispatch: false },
  );

  showCreateToDoSuccessMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementApiActions.createToDoSuccess),
        tap(() => {
          this.finToastService.show(
            Toast.success(this.toDoCreatedSuccessMessage),
          );
        }),
      ),
    { dispatch: false },
  );

  completeToDo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.completeTodo),
      switchMap(({ userAssignmentId }) => {
        return this.userAssignmentService
          .completeManualUserAssignment({ userAssignmentId })
          .pipe(
            map(() => {
              return TodosManagementApiActions.completeToDoSuccess();
            }),
            catchError(() => of(TodosManagementApiActions.toDoActionFailure())),
          );
      }),
    ),
  );

  getAvailableAssignmentTypes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        TodosManagementPageActions.getAvailableAssignmentTypes,
        TodosManagementApiActions.createToDoSuccess,
        TodosManagementApiActions.cancelToDoSuccess,
        TodosManagementApiActions.completeToDoSuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(todosManagementFeature.selectPerspective),
        this.store.select(todosManagementFeature.selectTodoStatus),
      ]),
      switchMap(([, perspective, todoStatus]) => {
        const statusList = todoStatusApiMap(todoStatus);

        return this.userAssignmentService
          .getUserAssignmentsAvailableTypesForStatus({
            statusList,
            perspective,
          })
          .pipe(
            map((types: UserAssignmentTypes[]) => {
              return TodosManagementApiActions.getAvailableAssignmentTypesSuccess(
                {
                  types,
                },
              );
            }),
            catchError(() =>
              of(
                TodosManagementApiActions.getAvailableAssignmentTypesFailure({
                  types: [],
                }),
              ),
            ),
          );
      }),
    ),
  );

  showCompleteToDoSuccessMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementApiActions.completeToDoSuccess),
        tap(() => {
          this.finToastService.show(
            Toast.success(this.toDoCompletedSuccessMessage),
          );
        }),
      ),
    { dispatch: false },
  );

  showCancelToDoSuccessMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementApiActions.cancelToDoSuccess),
        tap(() => {
          this.finToastService.show(
            Toast.success(this.toDoCanceledSuccessMessage),
          );
        }),
      ),
    { dispatch: false },
  );

  showToDoErrorMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementApiActions.toDoActionFailure),
        tap(() => {
          this.finToastService.show(Toast.error(this.toDoErrorMessage));
        }),
      ),
    { dispatch: false },
  );

  findTodoPosition$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TodosManagementPageActions.findTodoPosition),
      concatLatestFrom(() => [
        this.store.select(selectRouteParams),
        this.store.select(todosManagementFeature.selectFilters),
        this.store.select(todosManagementFeature.selectTodosFilters),
        this.store.select(todosManagementFeature.selectBusinessCasePaging),
      ]),
      switchMap(
        ([
          ,
          { todoType, todoId },
          businessCaseFilters,
          todosFilters,
          { limit },
        ]) => {
          const [key] = Object.entries(TodosType).find(
            ([, value]) => value === todoType,
          );

          const perspective =
            TodosApiPerspective[key as keyof typeof TodosApiPerspective];

          return this.userAssignmentService
            .getAssignmentPosition({
              perspective,
              userAssignmentId: todoId,
              limit,
              businessCaseOrderBy: businessCaseFilters.orderBy,
              businessCaseDirection: businessCaseFilters.direction,
              userAssignmentOrderBy: todosFilters.orderBy,
              userAssignmentDirection: todosFilters.direction,
            })
            .pipe(
              map((todoPosition) =>
                TodosManagementApiActions.findTodoPositionSuccess({
                  todoPosition,
                }),
              ),
              catchError((error) =>
                of(
                  TodosManagementApiActions.findTodoPositionFailure({ error }),
                ),
              ),
            );
        },
      ),
    ),
  );

  showUpdateTodoErrorMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(TodosManagementApiActions.updateTodoFailure),
        tap(() => {
          this.finToastService.show(Toast.error(this.todoUpdatedErrorMessage));
        }),
      ),
    { dispatch: false },
  );
}
