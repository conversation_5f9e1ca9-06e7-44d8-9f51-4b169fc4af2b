import { ALLOWED_FACILITIES, FacilityName } from '@fincloud/core/types';
import { StateLibFacilityPageActions } from '@fincloud/state/business-case';
import {
  StateLibFacilitiesApiActions,
  StateLibFacilitiesPageActions,
} from '@fincloud/state/facilities';
import { FieldType } from '@fincloud/types/enums';
import { BusinessCaseDashboardState } from '@fincloud/types/models';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import { keyBy, sortBy } from 'lodash-es';
import { BUSINESS_CASE_DASHBOARD_INITIAL_STATE } from '../../utils/business-case-dashboard-state';
import { FacilityPageActions } from '../actions';

export const facilityReducer: ActionReducer<
  BusinessCaseDashboardState,
  Action
> = createReducer(
  BUSINESS_CASE_DASHBOARD_INITIAL_STATE,
  on(StateLibFacilitiesPageActions.setFacilities, (state, action) => {
    return {
      ...state,
      facilities: sortBy(
        action.facilities
          .filter((f) => ALLOWED_FACILITIES.includes(f.name as FacilityName))
          .map((f) => {
            return {
              ...f,
              facilityFields: sortBy(f.facilityFields, (ff) => ff.ordinal).map(
                (ff) => {
                  const field = {
                    ...ff,
                    id: `${f.name}|${ff.group}|${ff.key}`,
                    facilityName: f.name,
                    fieldType: ff.fieldType,
                  };

                  if (ff.key === 'targetCompany') {
                    field.fieldType = FieldType.COMPOSITE;
                    field.isEditable = true;
                  }
                  return field;
                },
              ),
            };
          }),
        (f) => f.ordinal,
        'asc',
      ),
    };
  }),
  on(StateLibFacilityPageActions.updateFacilityFields, (state, action) => {
    const updatedFacilityFieldsById = keyBy(action.payload, (ff) => ff.id);
    return {
      ...state,
      facilities: state.facilities.map((facility) => {
        return {
          ...facility,
          facilityFields: facility.facilityFields.map((ff) => {
            return updatedFacilityFieldsById[ff.id] || { ...ff };
          }),
        };
      }),
    };
  }),
  on(FacilityPageActions.enableFacility, (state, action) => {
    return {
      ...state,
      facilities: state.facilities.map((f) => {
        if (f.name === action.payload) {
          return {
            ...f,
            enabledInCase: true,
          };
        }
        return f;
      }),
    };
  }),
  on(FacilityPageActions.disableFacility, (state, action) => {
    return {
      ...state,
      facilities: state.facilities.map((f) => {
        if (f.name === action.payload) {
          return {
            ...f,
            enabledInCase: false,
          };
        }
        return f;
      }),
    };
  }),
  on(
    StateLibFacilityPageActions.setIsRevisionApplied,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        isRevisionApplied: action.payload,
      };
    },
  ),
  on(
    StateLibFacilitiesPageActions.clearNorthDataCompanies,
    StateLibFacilitiesApiActions.addConfigurationFacilityFieldValueSuccess,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        singleCompanyNorthDataCompanies:
          BUSINESS_CASE_DASHBOARD_INITIAL_STATE.singleCompanyNorthDataCompanies,
        multiCompanyNorthDataCompanies:
          BUSINESS_CASE_DASHBOARD_INITIAL_STATE.multiCompanyNorthDataCompanies,
      };
    },
  ),
);
