import { Injectable } from '@angular/core';
import { Toast } from '@fincloud/core/toast';
import {
  StateLibFacilityPageActions,
  selectBusinessCaseId,
} from '@fincloud/state/business-case';
import { selectCustomerKey } from '@fincloud/state/customer';
import {
  StateLibFacilitiesApiActions,
  StateLibFacilitiesPageActions,
} from '@fincloud/state/facilities';
import { FacilitiesControllerService } from '@fincloud/swagger-generator/business-case-manager';
import { NorthDataControllerService } from '@fincloud/swagger-generator/company';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { FinToastService } from '@fincloud/ui/toast';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import {
  catchError,
  debounceTime,
  map,
  mergeMap,
  switchMap,
} from 'rxjs/operators';

@Injectable()
export class FacilityEffects {
  loadFacilitiesOnBusinessCaseLoaded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibFacilitiesPageActions.getFacilities,
        StateLibFacilitiesApiActions.addConfigurationFacilityFieldValueSuccess,
        StateLibFacilityPageActions.setIsRevisionApplied,
      ),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([action, businessCaseId]) => {
        return this.facilitiesControllerService
          .getBusinessCaseFacilities({ businessCaseId })
          .pipe(
            map((facilities) => {
              return StateLibFacilitiesPageActions.setFacilities({
                facilities,
              });
            }),
          );
      }),
    ),
  );

  addConfiguraitonFacilityFieldValue$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFacilitiesPageActions.addConfigurationFacilityFieldValue),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      mergeMap(([payload, businessCaseId]) =>
        this.facilitiesControllerService
          .addConfiguraitonFacilityFieldValue({
            businessCaseId,
            ...payload,
          })
          .pipe(
            map((businessCase) =>
              StateLibFacilitiesApiActions.addConfigurationFacilityFieldValueSuccess(
                {
                  businessCase:
                    businessCase as unknown as Partial<ExchangeBusinessCase>,
                },
              ),
            ),
            catchError((err) =>
              of(
                StateLibFacilitiesApiActions.addConfigurationFacilityFieldValueFailure(
                  { err },
                ),
              ),
            ),
          ),
      ),
    ),
  );

  addConfiguraitonFacilityFieldValueSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibFacilitiesApiActions.addConfigurationFacilityFieldValueSuccess,
        ),
        map(() => this.finToastService.show(Toast.success())),
      ),
    { dispatch: false },
  );

  addConfiguraitonFacilityFieldValueFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibFacilitiesApiActions.addConfigurationFacilityFieldValueFailure,
        ),
        map(() => this.finToastService.show(Toast.error())),
      ),
    { dispatch: false },
  );

  loadSingleCompaniesFromNorthData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFacilitiesPageActions.getSingleNorthDataCompanies),
      debounceTime(400),
      concatLatestFrom(() => this.store.select(selectCustomerKey)),
      switchMap(([action, customerKey]) =>
        this.northDataControllerService
          .getCompaniesFromNorthData({ query: action.query, customerKey })
          .pipe(
            map((res) =>
              StateLibFacilitiesApiActions.getSingleNorthDataCompaniesSuccess({
                companies: res,
              }),
            ),
            catchError((err) =>
              of(
                StateLibFacilitiesApiActions.getSingleNorthDataCompaniesFailure(
                  {
                    err,
                  },
                ),
              ),
            ),
          ),
      ),
    ),
  );

  loadMultiCompaniesFromNorthData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFacilitiesPageActions.getMultiNorthDataCompanies),
      debounceTime(400),
      concatLatestFrom(() => this.store.select(selectCustomerKey)),
      switchMap(([action, customerKey]) =>
        this.northDataControllerService
          .getCompaniesFromNorthData({ query: action.query, customerKey })
          .pipe(
            map((res) =>
              StateLibFacilitiesApiActions.getMultiNorthDataCompaniesSuccess({
                companies: res,
              }),
            ),
            catchError((err) =>
              of(
                StateLibFacilitiesApiActions.getMultiNorthDataCompaniesFailure({
                  err,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  constructor(
    private actions$: Actions,
    private store: Store,
    private finToastService: FinToastService,
    private facilitiesControllerService: FacilitiesControllerService,
    private northDataControllerService: NorthDataControllerService,
  ) {}
}
