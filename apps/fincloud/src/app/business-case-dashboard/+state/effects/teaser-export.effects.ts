import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { DateFormats, DateService } from '@fincloud/core/date';
import { FileService } from '@fincloud/core/files';
import { Toast } from '@fincloud/core/toast';
import {
  StateLibTeaserExportApiActions,
  StateLibTeaserExportPageActions,
  selectAutoGeneratedBusinessCaseName,
  selectBusinessCaseId,
} from '@fincloud/state/business-case';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { BusinessCaseTeaserGeneratorService } from '@fincloud/swagger-generator/document-generator';
import { Locale } from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, map, of, switchMap } from 'rxjs';

@Injectable()
export class TeaserExportEffects {
  downloadFinancingTeaserExport$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibTeaserExportPageActions.downloadFinancingTeaserExport),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseId),
        this.store.select(selectUserCustomerKey),
        this.store.select(selectAutoGeneratedBusinessCaseName),
      ]),
      switchMap(
        ([
          action,
          businessCaseId,
          userCustomerKey,
          autoGeneratedBusinessCaseName,
        ]) => {
          this.finToastService.show(
            Toast.info(
              $localize`:@@pdfExport.toast.info:PDF-Export gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.`,
            ),
          );

          return this.businessCaseTeaserGeneratorService
            .financingStructureTeaserControllerGenerateFinancingStructureTeaser(
              {
                businessCaseId,
                body: {
                  configuration: { locale: this.locale },
                  staticGroups: action.staticGroups,
                },
              },
            )
            .pipe(
              map((response) => {
                const prefix =
                  action.customerKey === userCustomerKey
                    ? 'Export FS'
                    : `FS ${action.customerKey}`;

                //That character : is not a ordinary colon , as ordinary colon is not valid for windows file's name
                //so do not replace it, or do it , if you are sure tht won't be raplaced by underscore
                const todaysDate = this.dateService.formatDate(
                  this.dateService.todaysDate(),
                  DateFormats.DD_DOT_MM_DOT_YYYY_WITH_HOURS,
                );
                this.fileService.downloadBinary(
                  response as Blob,
                  `${prefix} ${autoGeneratedBusinessCaseName} - ${todaysDate}.pdf`,
                );
                return StateLibTeaserExportApiActions.downloadFinancingTeaserExportSuccess();
              }),
              catchError((err) => {
                this.finToastService.show(
                  Toast.error(
                    $localize`:@@excelSpreadsheetDownload.toast.error:Es ist ein Fehler aufgetreten, bitte versuchen Sie es erneut.`,
                  ),
                );
                return of(
                  StateLibTeaserExportApiActions.downloadFinancingTeaserExportFailure(
                    {
                      err,
                    },
                  ),
                );
              }),
            );
        },
      ),
    ),
  );

  // TODO bring DATA ROOM teaser export, as in the current flow there is 3 nested subscription
  downloadDataRoomTeaserExport$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibTeaserExportPageActions.downloadDataRoomTeaserExport),
    ),
  );

  constructor(
    private actions$: Actions,
    private store: Store,
    private businessCaseTeaserGeneratorService: BusinessCaseTeaserGeneratorService,
    private fileService: FileService,
    private dateService: DateService,
    private finToastService: FinToastService,
    @Inject(LOCALE_ID) public locale: Locale,
  ) {}
}
