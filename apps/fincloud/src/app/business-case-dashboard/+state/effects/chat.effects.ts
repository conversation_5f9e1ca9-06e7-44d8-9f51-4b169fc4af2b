import { Injectable } from '@angular/core';
import {
  Chat,
  ChatManagementControllerService,
  ChatMessageManagementControllerService,
  ChatNotificationStatusControllerService,
} from '@fincloud/swagger-generator/communication';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { EMPTY, filter, forkJoin, map, of } from 'rxjs';
import { catchError, mergeMap, switchMap, tap } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';

import {
  FileManagerService,
  TusDocumentUploadController,
} from '@fincloud/core/files';
import { IdentityService } from '@fincloud/core/services';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import { DocumentFieldManageHelperService } from '@fincloud/neoshare/document';
import {
  StateLibBusinessCaseApiActions,
  StateLibBusinessCasePageActions,
  StateLibContextPageActions,
  selectBusinessCase,
  selectBusinessCaseId,
} from '@fincloud/state/business-case';
import {
  StateLibChatApiActions,
  StateLibChatHistoryRangeApiActions,
  StateLibChatHistoryRangePageActions,
  StateLibChatPageActions,
  selectExistingChats,
  selectInitialChatById,
} from '@fincloud/state/chat';
import { selectCustomerKey } from '@fincloud/state/customer';
import { selectUserId, selectUserToken } from '@fincloud/state/user';
import { ChatType, TusUploadType } from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION } from '@fincloud/utils';
import { concatLatestFrom } from '@ngrx/operators';
import {
  BusinessCaseDataRoomApiActions,
  ChatApiActions,
  ChatPageActions,
} from '../actions';

@Injectable()
export class ChatEffects {
  loadExistingChats$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      switchMap((action) => {
        return this.chatManagementControllerService
          .getAllChats({
            businessCaseId: action.payload.id,
            includeArchived: true,
          })
          .pipe(
            switchMap((existingChats) => {
              return of(
                ChatPageActions.setExistingChats({
                  payload: existingChats,
                }),
                ChatPageActions.createInternalChat({
                  payload: existingChats,
                }),
              );
            }),
          );
      }),
    ),
  );

  setMutedChats$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      concatLatestFrom(() => this.store.select(selectUserId)),
      switchMap(([action, userId]) => {
        return this.chatNotificationController
          .getMutedChatsForBusinessCaseUser({
            userId,
            businessCaseId: action.payload.id,
          })
          .pipe(
            map((chatIds: string[]) => {
              return ChatPageActions.setMutedChatIds({
                payload: chatIds,
              });
            }),
          );
      }),
    ),
  );

  refreshChat$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibChatPageActions.refreshChat),
      concatLatestFrom(() => this.store.select(selectBusinessCase)),
      switchMap(([action, businessCase]) => {
        return this.chatManagementControllerService
          .getChat({ chatId: action.payload, businessCaseId: businessCase?.id })
          .pipe(
            map((chat) => {
              return StateLibChatPageActions.updateChat({ payload: chat });
            }),
          );
      }),
    ),
  );

  setInitialSelectedChat$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ChatPageActions.setExistingChats),
      concatLatestFrom(() => this.store.select(selectInitialChatById)),
      switchMap(([, selectedChat]) => {
        if (selectedChat) {
          return of(
            ChatApiActions.setInitialSelectedChatSuccess({
              payload: selectedChat,
            }),
          );
        } else {
          return of(ChatApiActions.setInitialSelectedChatFailure());
        }
      }),
      catchError(() => of(ChatApiActions.setInitialSelectedChatFailure())),
    ),
  );

  refreshAllChats$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibChatPageActions.refreshAllChats,
        StateLibBusinessCasePageActions.receivePlatformNotificationSocketMessage,
      ),
      switchMap((action) => {
        return this.chatManagementControllerService
          .getAllChats({
            businessCaseId: action.payload,
            includeArchived: true,
          })
          .pipe(
            map((existingChats) =>
              ChatPageActions.setExistingChats({
                payload: existingChats,
              }),
            ),
          );
      }),
    ),
  );

  createInternalChat$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ChatPageActions.createInternalChat),
      concatLatestFrom(() => [
        this.store.select(selectUserToken),
        this.store.select(selectBusinessCase),
      ]),
      switchMap(([action, tokenParsed, businessCase]) => {
        const chats = action.payload;
        const hasInternalChat = chats.find(
          (ch) =>
            ch.status === 'ACTIVE' &&
            ch.chatType === ChatType.INTERNAL &&
            ch.customerKey === tokenParsed.customer_key,
        );

        if (!hasInternalChat) {
          return this.chatManagementControllerService
            .createChat({
              body: {
                customerKey: tokenParsed.customer_key,
                chatType: ChatType.INTERNAL,
                component: uuidv4(),
                participantCustomerKeys: [tokenParsed.customer_key],
                userId: tokenParsed.sub,
                businessCaseId: businessCase?.id,
              },
              businessCaseId: businessCase?.id,
            })
            .pipe(
              map((chat) => {
                return StateLibChatPageActions.addToExistingChats({
                  payload: chat,
                });
              }),
            );
        } else {
          return EMPTY;
        }
      }),
    ),
  );

  createOrUpdateInternalCompanyChat$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibChatPageActions.createOrUpdateCompanyInternalChat),
      concatLatestFrom(() => [
        this.store.select(selectExistingChats),
        this.store.select(selectUserToken),
        this.store.select(selectBusinessCase),
      ]),
      switchMap(([action, existingChats, tokenParsed, businessCase]) => {
        const userIds = action.payload.userIds;
        const chats = existingChats;

        const internalCompanyChat: Chat = chats.find(
          (ch) =>
            ch.status === 'ACTIVE' &&
            ch.chatType === 'INTERNAL_GROUP' &&
            ch.customerKey === tokenParsed.customer_key &&
            ch.topic === 'INTERNAL_GROUP_COMPANY_CHAT',
        );

        if (!internalCompanyChat && action.payload.updateOnly) {
          return of();
        }

        if (!internalCompanyChat) {
          const customerParticipantUsers = businessCase.participants
            .find((p) => p.customerKey === tokenParsed.customer_key)
            .users.map((u) => u.userId);

          return this.chatManagementControllerService
            .createChat({
              body: {
                customerKey: tokenParsed.customer_key,
                chatType: ChatType.INTERNAL_GROUP,
                topic: 'INTERNAL_GROUP_COMPANY_CHAT',
                component: uuidv4(),
                participantCustomerKeys: [tokenParsed.customer_key],
                userId: tokenParsed.sub,
                participantUserIds: [...customerParticipantUsers, ...userIds],
                businessCaseId: businessCase?.id,
              },
              businessCaseId: businessCase?.id,
            })
            .pipe(
              map((chat) => {
                return StateLibChatPageActions.addToExistingChats({
                  payload: chat,
                });
              }),
            );
        } else {
          const existingChatUsersIds = internalCompanyChat.chatUsers.map(
            (cu) => cu.userId,
          );
          const newUsersIds = userIds.filter(
            (uid) => !existingChatUsersIds.includes(uid),
          );

          const reqs = newUsersIds.map((uid) => {
            return this.chatManagementControllerService
              .addUserToChat({
                userId: uid,
                chatId: internalCompanyChat.id,
                businessCaseId: businessCase?.id,
              })
              .pipe(catchError(() => of(null)));
          });

          return forkJoin(reqs).pipe(
            map((chats: Chat[]) =>
              StateLibChatPageActions.updateChat({
                payload: chats[chats?.length - 1],
              }),
            ),
          );
        }
      }),
    ),
  );

  getOldestAndNewestMessageOfChat$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibChatHistoryRangePageActions.getOldestAndNewestMessageOfChat,
      ),
      switchMap((action) =>
        this.chatMessageManagementController
          .getOldestAndNewestMessageOfChat(action.payload)
          .pipe(
            map((res) =>
              StateLibChatHistoryRangeApiActions.getOldestAndNewestMessageOfChatSuccess(
                { payload: res },
              ),
            ),
            catchError((err) =>
              of(
                StateLibChatHistoryRangeApiActions.getOldestAndNewestMessageOfChatFailure(
                  { payload: err },
                ),
              ),
            ),
          ),
      ),
    ),
  );

  initiateFileUpload$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibChatPageActions.initiateFileUpload),
      concatLatestFrom(() => this.store.select(selectCustomerKey)),
      mergeMap(([{ file, businessCaseId }, customerKey]) => {
        return this.fileManagerService
          .upload({
            file: file,
            customerKey,
            uploadId: this.identityService.generateKey(),
            docType: 'CHAT',
            typeOfTusUpload: TusUploadType.CHAT,
            endpoint: this.tusService.getUploadChatDocumentPath(businessCaseId),
          })
          .pipe(
            map(({ document }) => {
              return StateLibChatApiActions.chatDocumentUploadedSuccess({
                attachment: document,
              });
            }),
            catchError((error) => {
              const message =
                this.documentUploadHelperService.getHandleFileErrorMsg(
                  file,
                  error.message,
                );
              return of(
                StateLibChatApiActions.chatDocumentUploadedFailure({
                  message,
                  file,
                }),
              );
            }),
          );
      }),
    ),
  );

  chatDocumentUploadedSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibChatApiActions.chatDocumentUploadedSuccess),
        tap(() => this.finToastService.show(Toast.success())),
      ),
    { dispatch: false },
  );

  chatDocumentUploadFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibChatApiActions.chatDocumentUploadedFailure),
        tap(({ message }) => {
          this.finToastService.show(Toast.error(message));
        }),
      ),
    { dispatch: false },
  );

  аrchiveExistingChat$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseDataRoomApiActions.deleteFieldBusinessCaseDataRoomSuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseId),
        this.store.select(selectExistingChats),
      ]),
      filter(
        ([deletedFieldPayload, , existingChats]) =>
          !!existingChats.find(
            (chat) => chat.component === deletedFieldPayload.field?.id,
          ),
      ),
      switchMap(([deletedFieldPayload, businessCaseId, existingChats]) => {
        const chatId: string = existingChats.find(
          (chat) => chat.component === deletedFieldPayload.field?.id,
        ).id;
        return this.chatManagementControllerService
          .archiveChat({
            chatId,
            businessCaseId,
          })
          .pipe(
            map((chat) =>
              ChatApiActions.archiveExistingChatSuccess({
                payload: chat,
              }),
            ),
            catchError((error) =>
              of(
                ChatApiActions.archiveExistingChatFailure({
                  errorMessage: error?.message || '',
                }),
              ),
            ),
          );
      }),
    ),
  );

  initializeChatSocket$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibContextPageActions.loadBusinessCaseContext),
        filter(() => !this.socketService.checkIsConnected(SocketType.CHAT)),
        tap(() => {
          this.socketService.initializeSocket(SocketType.CHAT);
        }),
      ),
    { dispatch: false },
  );

  joinRoomsForChatSocket$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(ChatPageActions.setExistingChats),
        concatLatestFrom(() => [
          this.store.select(selectBusinessCaseId),
          this.store.select(selectExistingChats),
        ]),
        tap(([, , existingChats]) => {
          existingChats.forEach((chat: Chat) => {
            this.socketService.joinRoomAndReceiveMessagesByDestination(
              chat.id,
              `${CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION}-${chat.id}`,
              SocketType.CHAT,
            );
          });
        }),
      ),
    { dispatch: false },
  );

  constructor(
    private actions$: Actions,
    private chatManagementControllerService: ChatManagementControllerService,
    private chatNotificationController: ChatNotificationStatusControllerService,
    private store: Store,
    private chatMessageManagementController: ChatMessageManagementControllerService,
    private fileManagerService: FileManagerService,
    private identityService: IdentityService,
    private tusService: TusDocumentUploadController,
    private finToastService: FinToastService,
    private documentUploadHelperService: DocumentFieldManageHelperService,
    private socketService: SocketService,
  ) {}
}
