import { Injectable } from '@angular/core';
import {
  BusinessCaseControllerService,
  InformationControllerService,
  ParticipantControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  CompanyControllerService,
  InformationControllerService as CompanyInformationService,
} from '@fincloud/swagger-generator/company';
import {
  BusinessCaseParticipantCustomer,
  Company,
  CompanyInformation,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { EMPTY, Observable, forkJoin, iif, of, tap } from 'rxjs';
import {
  catchError,
  debounceTime,
  filter,
  map,
  mergeMap,
  switchMap,
  take,
  takeUntil,
} from 'rxjs/operators';

import { Location } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { TokenManagementService } from '@fincloud/core/auth';
import { StateTransfer } from '@fincloud/core/business-case';
import { DateService } from '@fincloud/core/date';
import { ModalService } from '@fincloud/core/modal';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import {
  NDA_IS_NOT_SIGNED,
  NO_ACCESS_TO_PORTAL_CASE,
  RESTRICTED_CASE_ACCESS,
} from '@fincloud/neoshare/account-management';
import {
  CollaborationInvitationModalComponent,
  INVITATION_STATUS_NAME,
} from '@fincloud/neoshare/business-case-collaboration';
import {
  StateLibBusinessCaseApiActions,
  StateLibBusinessCasePageActions,
  StateLibInvitationsPageActions,
  StateLibMyPartnersApiActions,
  StateLibMyPartnersPageActions,
  selectBusinessCase,
  selectBusinessCaseId,
  selectBusinessCaseParticipationType,
  selectCaseFieldsAccessLoaded,
  selectCustomerBusinessCaseContext,
  selectFieldKeysToBeIncluded,
  selectInvitations,
  selectIsBusinessCaseActive,
  selectIsBusinessCaseRealEstate,
  selectMirroredCalculatableFieldKeys,
  selectMirroredFieldKeys,
  selectReasonsForClosingCase,
  selectReasonsForReactivatingCase,
} from '@fincloud/state/business-case';
import {
  StateLibBusinessCaseRealEstateApiActions,
  StateLibBusinessCaseRealEstatePageActions,
} from '@fincloud/state/business-case-real-estate';
import { selectCustomerKey } from '@fincloud/state/customer';
import {
  StateLibDocumentInboxApiActions,
  StateLibDocumentInboxPageActions,
  selectInboxEmail,
} from '@fincloud/state/document-inbox';
import { selectUrl } from '@fincloud/state/router';
import { selectUserId } from '@fincloud/state/user';
import { UserManagementControllerService } from '@fincloud/swagger-generator/authorization-server';
import {
  EmailForwardingRuleDto,
  EmailForwardingService,
} from '@fincloud/swagger-generator/document-forwarding';
import { CoreBankingSystemIntegrationControllerService } from '@fincloud/swagger-generator/external-integrations';
import {
  FinStructureControllerService,
  FinStructureMirroringControllerService,
} from '@fincloud/swagger-generator/financing-details';
import {
  ApplicationOrInvitationType,
  CustomerStatus,
  FinancingStructureType,
  ParticipationType,
} from '@fincloud/types/enums';
import { ApplicationOrInvitationInfo } from '@fincloud/types/models';
import { FinDropdownOption } from '@fincloud/ui/dropdown';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import {
  CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION,
  NEXT_FOLDER_DESTINATION,
  PLATFORM_NOTIFICATION_CUSTOMER_ADDED_TO_CHAT_DESTINATION,
  PLATFORM_NOTIFICATION_CUSTOMER_REMOVED_FROM_CHAT_DESTINATION,
  PLATFORM_NOTIFICATION_USER_ADDED_TO_CHAT_DESTINATION,
} from '@fincloud/utils';
import { concatLatestFrom } from '@ngrx/operators';
import { flatten } from 'lodash-es';
import { ChangeBusinessCaseStateModalComponent } from '../../components/change-business-case-state-modal/change-business-case-state-modal.component';
import { ChangeBusinessCasedData } from '../../models/change-business-case-data';
import { Perception } from '../../models/perceptions';
import { prepareCaseStatus } from '../../utils/map-case-status';
import {
  BusinessCaseApiActions,
  BusinessCaseDataRoomApiActions,
  BusinessCasePageActions,
  ChatPageActions,
  CompanyPortalPageActions,
  InvitationsApiActions,
} from '../actions';

@Injectable()
export class BusinessCaseEffects {
  showCloseBusinessCaseModal$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCasePageActions.showCompleteBusinessCaseModal),
      switchMap(({ payload }) => {
        const message = $localize`:@@businessCase.dashboard.modals.completeBusinessCaseModal.message:Sind Sie sicher, dass Sie Ihren Finanzierungsfall abschließen möchten?
      Bitte wählen Sie einen Grund aus:`;
        const confirmLabel = $localize`:@@businessCase.dashboard.modals.completeBusinessCaseModal.buttons.confirm:Abschließen`;

        const modalRef = this.finModalService.open(
          ChangeBusinessCaseStateModalComponent,
          {
            data: {
              businessCaseId: payload,
              selectOptions$: this.store.select(
                selectReasonsForClosingCase,
              ) as Observable<FinDropdownOption[]>,
              actionType: StateTransfer.COMPLETE,
              svgIconName: 'svgCloseCaseComposite',
              message: message,
              confirmLabel: confirmLabel,
            } as ChangeBusinessCasedData,
            size: this.finSize.S,
            disableClose: true,
          },
        );

        return modalRef.afterClosed().pipe(
          filter((data) => data !== ''),
          map(() =>
            BusinessCaseApiActions.showCompleteBusinessCaseModalSuccess(),
          ),
        );
      }),
    );
  });

  showToastSuccessMessagesOnCaseStatusChange$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(
          BusinessCaseApiActions.showReactivateBusinessCaseModalSuccess,
          BusinessCaseApiActions.showCompleteBusinessCaseModalSuccess,
        ),
        tap(() => this.finToastService.show(Toast.success())),
      );
    },
    {
      dispatch: false,
    },
  );

  showReactivateBusinessCaseModal$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCasePageActions.showReactivateBusinessCaseModal),
      switchMap(({ payload }) => {
        const message = $localize`:@@businessCase.dashboard.modals.reactivateBusinessCaseModal.message:Sind Sie sicher, dass Sie Ihren Finanzierungsfall reaktivieren wollen?
      Bitte wählen Sie einen Grund aus:`;
        const confirmLabel = $localize`:@@businessCase.dashboard.modals.reactivateBusinessCaseModal.buttons.reactivate:Reaktivieren`;
        const modalRef = this.finModalService.open(
          ChangeBusinessCaseStateModalComponent,
          {
            data: {
              businessCaseId: payload,
              message: message,
              selectOptions$: this.store.select(
                selectReasonsForReactivatingCase,
              ) as Observable<FinDropdownOption[]>,
              actionType: StateTransfer.REACTIVATE,
              svgIconName: 'svgReactivateCaseComposite',
              confirmLabel: confirmLabel,
            } as ChangeBusinessCasedData,
            size: this.finSize.S,
            disableClose: true,
          },
        );

        return modalRef.afterClosed().pipe(
          filter((data) => data !== ''),
          map(() =>
            BusinessCaseApiActions.showReactivateBusinessCaseModalSuccess(),
          ),
        );
      }),
    );
  });

  reloadBusinessCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.reloadBusinessCase),
      debounceTime(600),
      map((action) =>
        StateLibBusinessCasePageActions.loadBusinessCase({
          payload: action.payload,
        }),
      ),
    ),
  );

  loadMainRefsCommonFields$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      filter(
        ({ payload }) =>
          payload?.structuredFinancingConfiguration?.financingStructureType ===
          FinancingStructureType.REAL_ESTATE,
      ),
      map((action) => {
        return StateLibBusinessCaseRealEstatePageActions.loadMainRefsCommonFields(
          {
            payload: { businessCaseId: action.payload.id },
          },
        );
      }),
    );
  });

  joinRoomForPlatformNotification$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
        concatLatestFrom(() => [
          this.store.select(selectUserId),
          this.store.select(selectCustomerKey),
        ]),
        filter(() => this.socketService.checkIsConnected(SocketType.CHAT)),
        tap(([action, userId, customerKey]) => {
          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `added-to-chat-user-${userId}`,
            PLATFORM_NOTIFICATION_USER_ADDED_TO_CHAT_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          );

          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `added-to-chat-customer-${customerKey}`,
            PLATFORM_NOTIFICATION_CUSTOMER_ADDED_TO_CHAT_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          );

          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `removed-from-chat-customer-${customerKey}`,
            PLATFORM_NOTIFICATION_CUSTOMER_REMOVED_FROM_CHAT_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          );

          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `nex-folder-document-update-business-case-${action.payload.id}`,

            NEXT_FOLDER_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          );
        }),
      ),
    { dispatch: false },
  );

  leavePlatformNotificationRoomsOnClearBusinessCase$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibBusinessCasePageActions.clearBusinessCase),
        concatLatestFrom(() => [
          this.store.select(selectCustomerKey),
          this.store.select(selectUserId),
          this.store.select(selectUrl),
        ]),
        tap(([action, customerKey, userId, selectUrl]) => {
          // only trigger if user is navigating away from businessCase and not to the next folder
          if (!selectUrl.includes('apps-integration/nextfolder-integration')) {
            this.socketService.leaveRoom(
              `added-to-chat-user-${userId}`,
              SocketType.PLATFORM_NOTIFICATION,
            );
            this.socketService.leaveRoom(
              `added-to-chat-customer-${customerKey}`,
              SocketType.PLATFORM_NOTIFICATION,
            );
            this.socketService.leaveRoom(
              `removed-from-chat-customer-${customerKey}`,
              SocketType.PLATFORM_NOTIFICATION,
            );
            this.socketService.leaveRoom(
              `nex-folder-document-update-business-case-${action.businessCaseId}`,
              SocketType.PLATFORM_NOTIFICATION,
            );
          }
        }),
      ),
    { dispatch: false },
  );

  handleAddedCustomerToChatMessage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      take(1),
      switchMap(() =>
        this.socketService
          .getMessagesByDestination$<{
            chatId: string;
          }>(
            PLATFORM_NOTIFICATION_CUSTOMER_ADDED_TO_CHAT_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          )
          .pipe(
            concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
            map(([message, businessCaseId]) => {
              this.socketService.joinRoomAndReceiveMessagesByDestination(
                message.chatId,
                `${CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION}-${message.chatId}`,
                SocketType.CHAT,
              );

              return StateLibBusinessCasePageActions.receivePlatformNotificationSocketMessage(
                {
                  payload: businessCaseId,
                },
              );
            }),
            takeUntil(
              this.actions$.pipe(
                ofType(StateLibBusinessCasePageActions.clearBusinessCase),
              ),
            ),
          ),
      ),
    ),
  );

  handleCustomerRemovedFromChatMessage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ChatPageActions.setExistingChats),
      take(1),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([, businessCaseId]) =>
        this.socketService
          .getMessagesByDestination$<{
            chatId: string;
          }>(
            PLATFORM_NOTIFICATION_CUSTOMER_REMOVED_FROM_CHAT_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          )
          .pipe(
            map((message) => {
              this.socketService.leaveRoom(message.chatId, SocketType.CHAT);

              return StateLibBusinessCasePageActions.receivePlatformNotificationSocketMessage(
                {
                  payload: businessCaseId,
                },
              );
            }),
            takeUntil(
              this.actions$.pipe(
                ofType(StateLibBusinessCasePageActions.clearBusinessCase),
              ),
            ),
          ),
      ),
    ),
  );

  loadMainFinStructureCommonFields$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibBusinessCaseRealEstatePageActions.loadMainRefsCommonFields,
      ),
      switchMap((action) =>
        // TODO: overhaul - swagger
        this.finStructureControllerService
          .getLeadFinancingStructureCommonFields1({
            businessCaseId: action.payload.businessCaseId,
          })
          .pipe(
            map((fields) => {
              return BusinessCaseApiActions.loadMainRefsCommonFieldsSuccess({
                payload: fields,
              });
            }),
          ),
      ),
    );
  });

  loadCaseFieldsAccessOnFirstLoad$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.loadBusinessCase),
      switchMap((action) =>
        this.store.select(selectCaseFieldsAccessLoaded).pipe(
          filter((loaded) => !loaded),
          map(() => action),
        ),
      ),
      map((action) =>
        CompanyPortalPageActions.loadCaseFieldsAccess({
          payload: action.payload,
        }),
      ),
    ),
  );

  addUserToGuestCustomerFromMyPartnersPage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibMyPartnersPageActions.addUserToGuestCustomerFromMyPartnersPage,
      ),
      concatLatestFrom(() => [
        this.store.select(selectInvitations),
        this.store.select(selectBusinessCase),
      ]),
      filter(
        ([, invitations, businessCase]) =>
          !!invitations?.length && !!businessCase,
      ),
      switchMap(([action, invitations, businessCase]) => {
        const targetInvitation = invitations.find(
          (invitation) =>
            invitation.invitedCustomerKey === action.payload.customerKey,
        );

        const applicationOrInvitationInfo = {
          applicationOrInvitationStatusValue:
            INVITATION_STATUS_NAME[targetInvitation.invitationStatus],
          autoGeneratedBusinessCaseName:
            businessCase.autoGeneratedBusinessCaseName,
          businessCaseId: businessCase.id,
          callerCustomerKey: targetInvitation.callerCustomerKey,
          customerKey: targetInvitation.invitedCustomerKey,
          customerName: targetInvitation.invitedCustomerKey,
          customerStatus: CustomerStatus.GUEST,
          customerType: targetInvitation.customerType,
          expirationDate: targetInvitation.expirationDate,
          id: targetInvitation.id,
          imageUrl: '',
          invitationStatus: targetInvitation.invitationStatus,
          invitedCustomerKey: targetInvitation.invitedCustomerKey,
          invitedUsers: targetInvitation.invitedUsers,
          isRegistered: true,
          key: targetInvitation.invitedCustomerKey,
          name: targetInvitation.invitedCustomerKey,
          ndaContractId: null,
          applicationOrInvitationType: ApplicationOrInvitationType.INVITATION,
          participationType: targetInvitation.participationType,
          bic: '',
          creationDate: targetInvitation.creationDate,
        } as ApplicationOrInvitationInfo;

        const modalRef = this.finModalService.open(
          CollaborationInvitationModalComponent,
          {
            data: {
              addUsersForAcceptedGuest: true,
              applicationOrInvitationInfo,
              businessCase: businessCase,
              isOpenViaKebabMenu: true,
            },
            size: FinSize.M,
          },
        );

        return modalRef.afterClosed().pipe(
          map((result) => {
            if (result) {
              return StateLibMyPartnersApiActions.addUsersToGuestCustomerSuccess(
                {
                  payload: applicationOrInvitationInfo,
                },
              );
            }

            return StateLibMyPartnersPageActions.addUsersToGuestCustomerCancelled();
          }),
        );
      }),

      catchError(() => {
        this.modalService.closeActiveModals();
        this.finToastService.show(Toast.error());

        return of(
          StateLibMyPartnersApiActions.addUsersToGuestCustomerFailure(),
        );
      }),
    ),
  );

  setLoadingStateForBusinessCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.loadBusinessCase),
      map(() =>
        BusinessCasePageActions.setBusinessCaseLoading({
          payload: true,
        }),
      ),
    ),
  );

  inviteUsersToGuestCustomer$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibInvitationsPageActions.inviteUsersToGuestCustomer),
      switchMap((action) =>
        this.participantControllerService
          .addParticipantGuestUsersToBusinessCase({
            businessCaseId: action.payload.businessCaseId,
            customerKey: action.payload.customerKey,
            body: action.payload.body,
          })
          .pipe(
            map(() => {
              this.modalService.closeActiveModals(true);

              return InvitationsApiActions.inviteUsersToGuestCustomerSuccess();
            }),
            catchError(() => {
              this.finToastService.show(Toast.error());

              return of(
                InvitationsApiActions.inviteUsersToGuestCustomerFailure(),
              );
            }),
          ),
      ),
    ),
  );

  loadMirroringEligibleFieldKeys$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibBusinessCasePageActions.loadMirroredFieldKeys,
        BusinessCaseDataRoomApiActions.deleteFieldBusinessCaseDataRoomSuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCase),
        this.store.select(selectMirroredFieldKeys),
        this.store.select(selectIsBusinessCaseRealEstate),
      ]),
      filter(
        ([payload, , mirroredFieldKeys, isCaseRealEstate]) =>
          (!mirroredFieldKeys?.length || !!payload.groupsOrdered?.length) &&
          isCaseRealEstate,
      ),
      switchMap(([payload, businessCase]) => {
        return this.financingDetailsMirroringService
          .getMirroringEligibleStaticFieldKeys({
            businessCaseId: businessCase.id,
          })
          .pipe(
            map((fieldKeys) =>
              StateLibBusinessCaseApiActions.loadMirroredFieldKeysSuccess({
                eligibleFieldKeys: fieldKeys,
                dataRoomTemplateFieldKeys: flatten(
                  (payload.groupsOrdered?.length &&
                    payload.groupsOrdered.map((group) => group.fields)) ??
                    businessCase.businessCaseTemplate.template.groupsOrdered.map(
                      (group) => group.fields,
                    ),
                ),
              }),
            ),
            catchError((error) =>
              of(
                StateLibBusinessCaseApiActions.loadMirroredFieldKeysFailure({
                  error,
                }),
              ),
            ),
          );
      }),
    ),
  );

  loadMirroringCalculatableFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.loadMirroredFieldKeys),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCase),
        this.store.select(selectMirroredCalculatableFieldKeys),
        this.store.select(selectIsBusinessCaseRealEstate),
      ]),
      filter(
        ([, , mirroredCalculatableFields, isCaseRealEstate]) =>
          !mirroredCalculatableFields?.length && isCaseRealEstate,
      ),
      switchMap(([, businessCase]) => {
        return this.financingDetailsMirroringService
          .eligibleFinancingFieldsCalculatableMirroring({
            businessCaseId: businessCase.id,
          })
          .pipe(
            map((fieldKeys) =>
              StateLibBusinessCaseApiActions.loadMirroredCalculatableFieldsSuccess(
                {
                  calculatableFieldKeys: fieldKeys,
                },
              ),
            ),
            catchError((error) =>
              of(
                StateLibBusinessCaseApiActions.loadMirroredCalculatableFieldsFailure(
                  {
                    error,
                  },
                ),
              ),
            ),
          );
      }),
    ),
  );

  loadBusinessCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.loadBusinessCase),
      switchMap((action) => {
        return this.store.select(selectCaseFieldsAccessLoaded).pipe(
          filter((loaded) => loaded),
          map(() => action),
        );
      }),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCase),
        this.store.select(selectFieldKeysToBeIncluded),
      ]),
      switchMap(([action, businessCaseFromStore, fieldKeysToBeIncluded]) => {
        const getBusinessCaseRequests = forkJoin([
          this.businessCaseControllerService
            .getBusinessCaseById({
              businessCaseId: action.payload,
              body: {
                fieldKeysToBeIncluded,
              },
            })
            .pipe(
              catchError(({ error }) => {
                this.store.dispatch(
                  BusinessCaseApiActions.loadBusinessCaseFailure({
                    payload: error.code,
                  }),
                );
                return EMPTY;
              }),
            ),
          this.informationControllerService.getAllInformation({
            businessCaseId: action.payload,
            body: {
              fieldKeysToBeIncluded,
            },
          }),
        ]);

        return getBusinessCaseRequests.pipe(
          switchMap(([businessCaseLoaded, information]) => {
            return forkJoin([
              of(businessCaseLoaded),
              of(information),
              iif(
                () => !businessCaseFromStore?.company,
                // this builds company
                forkJoin([
                  this.companyService.getCompanyById({
                    id: businessCaseLoaded.companyId,
                  }),
                  this.companyInformationControllerService
                    .getAllInformation({
                      companyId: businessCaseLoaded.companyId,
                      includeDeleted: false,
                    })
                    .pipe(catchError(() => of({}))),
                ]).pipe(
                  map(([company, information]) => {
                    return {
                      ...company,
                      information: Object.values(
                        information || {},
                      ) as CompanyInformation[],
                    } as Company;
                  }),
                ),
                of(businessCaseFromStore?.company),
              ),
            ]);
          }),
          map(([businessCase, caseInformation, company]) => {
            const exchangeBusinessCase: ExchangeBusinessCase = {
              ...businessCase,
              autoGeneratedBusinessCaseName:
                businessCase.autoGeneratedBusinessCaseName,
              structuredFinancingConfiguration:
                businessCase.structuredFinancingConfiguration,
              company: company,
              businessCaseType: businessCase.businessCaseType,
              businessCaseTemplate: businessCase.businessCaseTemplate,
              information: caseInformation,
              leadCustomerKey: businessCase.leadCustomerKey,
              masterBusinessCaseId: businessCase.masterBusinessCaseId,
              participants: businessCase.participants,
            } as unknown as ExchangeBusinessCase;

            return StateLibBusinessCaseApiActions.loadBusinessCaseSuccess({
              payload: {
                ...exchangeBusinessCase,
                lastModifiedDate: this.dateService.addTimeZoneInfo(
                  businessCase.lastModifiedDate || new Date().toDateString(),
                ),
              },
            });
          }),
          catchError(({ error }) => {
            this.store.dispatch(
              BusinessCaseApiActions.loadBusinessCaseFailure({
                payload: error.code,
              }),
            );
            return EMPTY;
          }),
        );
      }),
    ),
  );

  loadGlobalRequiredFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      switchMap(() => {
        return this.informationControllerService.getGlobalRequiredFields().pipe(
          map((globalRequiredFieldsKeys) => {
            return ChatPageActions.setGlobalRequiredFieldsKeys({
              payload: globalRequiredFieldsKeys,
            });
          }),
        );
      }),
    ),
  );

  checkIfCanShowDataExportHintsAndSection$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      concatLatestFrom(() => [
        this.store.select(selectIsBusinessCaseRealEstate),
      ]),
      filter(([, isRealEstateCase]) => isRealEstateCase),
      switchMap(() => {
        // TODO: overhaul - swagger
        return this.coreBankingService.getRegistration1().pipe(
          map((tokenData) => {
            const hasActiveToken = tokenData?.registrationExpirationDate
              ? this.dateService.getDifferenceBetweenDates(
                  tokenData.registrationExpirationDate,
                  new Date(),
                  'hours',
                ) /
                  24 >
                0
              : false;
            return BusinessCaseApiActions.loadCustomerDataExportTokenSuccess({
              payload: {
                hasActiveToken,
                businessCases: tokenData?.businessCases,
              },
            });
          }),
          catchError(() =>
            of(BusinessCaseApiActions.loadCustomerDataExportTokenFailure()),
          ),
        );
      }),
    ),
  );

  applyEmailFromStoreOrGetIfMissing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibDocumentInboxPageActions.applyEmailFromStoreOrGetIfMissing,
        StateLibDocumentInboxPageActions.openDocumentsInboxPanelModal,
      ),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCase),
        this.store.select(selectInboxEmail),
      ]),
      map(([, businessCase, email]) => {
        if (email) {
          return StateLibDocumentInboxPageActions.setDocumentEmailForwarding({
            payload: email,
          });
        }

        return StateLibDocumentInboxPageActions.loadInboxEmail({
          payload: businessCase.id,
        });
      }),
    ),
  );

  loadEmailForwarding$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibDocumentInboxPageActions.loadInboxEmail),
      switchMap((action) =>
        this.mailForwardingControllerService.emailForwardingRuleControllerGetByBusinessCaseId(
          {
            id: action.payload,
          },
        ),
      ),
      map((res: EmailForwardingRuleDto) =>
        StateLibDocumentInboxPageActions.setDocumentEmailForwarding({
          payload: res.emailAddress,
        }),
      ),
      catchError(() =>
        of(StateLibDocumentInboxApiActions.setDocumentEmailForwardingFailure()),
      ),
    ),
  );

  updateCustomerParticipationAmount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.updateCustomerParticipationAmount),
      mergeMap(({ payload }) =>
        this.participantControllerService
          .updateCustomerParticipationAmount(payload)
          .pipe(
            map((response) => {
              this.finToastService.show(Toast.success());
              return StateLibBusinessCaseRealEstateApiActions.updateMyParticipationAmountSuccess(
                {
                  payload: response as BusinessCaseParticipantCustomer,
                },
              );
            }),
            catchError((err) => {
              this.finToastService.show(Toast.error());
              return of(
                StateLibBusinessCaseRealEstateApiActions.updateMyParticipationAmountFailure(
                  {
                    payload: err,
                  },
                ),
              );
            }),
          ),
      ),
    ),
  );

  updateMyParticipationAmount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibBusinessCaseRealEstatePageActions.updateMyParticipationAmount,
      ),
      mergeMap(({ payload }) =>
        this.participantControllerService
          .updateParticipationAmount(payload)
          .pipe(
            map((response) => {
              this.finToastService.show(Toast.success());

              return StateLibBusinessCaseRealEstateApiActions.updateMyParticipationAmountSuccess(
                {
                  payload: response as BusinessCaseParticipantCustomer,
                },
              );
            }),
            catchError((err) => {
              this.finToastService.show(Toast.error());
              return of(
                StateLibBusinessCaseRealEstateApiActions.updateMyParticipationAmountFailure(
                  { payload: err },
                ),
              );
            }),
          ),
      ),
    ),
  );

  handleBusinessCaseError$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(BusinessCaseApiActions.loadBusinessCaseFailure),
        concatLatestFrom(() => [
          this.store.select(selectCustomerKey),
          this.store.select(selectCustomerBusinessCaseContext),
        ]),
        tap(([error, customerKey, caseContext]) => {
          if (error.payload === RESTRICTED_CASE_ACCESS) {
            this.router.navigate([
              customerKey,
              'business-case',
              caseContext.businessCaseId,
              'restricted-access',
            ]);
            this.store.dispatch(
              BusinessCasePageActions.loadCurrentUsersFromMyOrganizationInBusinessCase(),
            );
            return;
          }
          if (error.payload === NDA_IS_NOT_SIGNED) {
            this.finToastService.show(
              Toast.error(
                $localize`:@@privateBusinessCase.toast.ndaNotSignedByAllParties:Alle Unterzeichner aus Ihrer Organisation müssen die NDA unterschreiben, damit Sie auf den Fall zugreifen können.`,
              ),
            );

            this.location.back();
            return;
          }

          if (error.payload === NO_ACCESS_TO_PORTAL_CASE) {
            this.finToastService.show(Toast.error());
            return;
          }
        }),
      ),
    { dispatch: false },
  );
  loadCaseParticipantsFromMyOrganization$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCasePageActions.loadCurrentUsersFromMyOrganizationInBusinessCase,
      ),
      concatLatestFrom(() =>
        this.store.select(selectCustomerBusinessCaseContext),
      ),
      switchMap(([_, caseContext]) =>
        this.participantControllerService
          .getCurrentUsersForCustomerInBusinessCase({
            businessCaseId: caseContext.businessCaseId,
          })
          .pipe(
            catchError(() => {
              this.store.dispatch(
                BusinessCaseApiActions.loadCurrentUsersFromMyOrganizationInBusinessCaseFailure(),
              );
              return EMPTY;
            }),
            switchMap((res) =>
              forkJoin(
                res.users.map((user) =>
                  this.userManagementControllerService
                    .getUserById({ userId: user.userId })
                    .pipe(catchError(() => of(null))),
                ),
              ).pipe(
                map((users) =>
                  BusinessCaseApiActions.loadCurrentUsersFromMyOrganizationInBusinessCaseSuccess(
                    {
                      payload: {
                        autoGeneratedBusinessCaseName:
                          res.autoGeneratedBusinessCaseName,
                        users,
                      },
                    },
                  ),
                ),
              ),
            ),
          ),
      ),
    ),
  );

  getBusinessCaseStatuses$ = createEffect(() => {
    const participantPerceptionsRequestMap = new Map<
      ParticipationType,
      Observable<Perception[]>
    >([
      [
        ParticipationType.LEADER,
        this.businessCaseControllerService.getLeaderLeaderCaseStatePerceptions(),
      ],
      [
        ParticipationType.STRUCTURER,
        this.businessCaseControllerService.participantStructurerCaseStatePerception(),
      ],
      [
        ParticipationType.PARTICIPANT,
        this.businessCaseControllerService.participantParticipantCaseStatePerception(),
      ],
    ]);

    return this.actions$.pipe(
      ofType(
        BusinessCasePageActions.loadBusinessCaseStatuses,
        StateLibBusinessCaseApiActions.loadBusinessCaseSuccess,
      ),
      concatLatestFrom(() =>
        this.store.select(selectBusinessCaseParticipationType),
      ),
      filter(([, participationType]) => !!participationType),
      switchMap(([_, participationType]) => {
        return participantPerceptionsRequestMap.get(participationType).pipe(
          map((results) =>
            BusinessCaseApiActions.loadBusinessCaseStatusesSuccess({
              payload: results,
            }),
          ),
          catchError((err: HttpErrorResponse) => {
            return of(
              BusinessCaseApiActions.loadBusinessCaseStatusesFailure({
                payload: err,
              }),
            );
          }),
        );
      }),
    );
  });

  getReasonsForClosingBusinessCase$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCasePageActions.loadReasonsForClosingCase),
      switchMap(() => {
        return this.businessCaseControllerService
          .getClosedLeaderLeaderCaseStatePerceptions()
          .pipe(
            map((results) =>
              BusinessCaseApiActions.loadReasonsForClosingCaseSuccess({
                payload: results,
              }),
            ),
            catchError((err) =>
              of(
                BusinessCaseApiActions.loadReasonsForClosingCaseFailure({
                  payload: err,
                }),
              ),
            ),
          );
      }),
    );
  });

  changeBusinessCaseStatus$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCasePageActions.changeBusinessCaseStatus),
      concatLatestFrom(() => [
        this.store.select(selectCustomerKey),
        this.store.select(selectIsBusinessCaseActive),
      ]),
      switchMap(
        ([
          {
            payload: { businessCaseId, status },
          },
          customerKey,
          isActive,
        ]) => {
          return this.participantControllerService
            .updateParticipantStatePerception({
              businessCaseId: businessCaseId,
              newState: prepareCaseStatus(status),
            })
            .pipe(
              map(() => {
                this.finToastService.show(
                  Toast.success(
                    $localize`:@@businessCase.dashboard.changeStatus.success:Fallstatus erfolgreich geändert`,
                  ),
                );
                return BusinessCaseApiActions.changeBusinessCaseStatusSuccess({
                  payload: {
                    customerKey: customerKey,
                    status: status,
                    targetLead: !isActive,
                  },
                });
              }),
              catchError((err) => {
                this.finToastService.show(
                  Toast.error(
                    $localize`:@@businessCase.dashboard.changeStatus.error:Statusupdate fehlgeschlagen`,
                  ),
                );
                return of(
                  BusinessCaseApiActions.changeBusinessCaseStatusFailure(err),
                );
              }),
            );
        },
      ),
    );
  });

  closeBusinessCaseSetState$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCasePageActions.completeBusinessCase),
      switchMap((action) => {
        return this.businessCaseControllerService
          .setState({
            businessCaseId: action.payload.businessCaseId,
            stateTransfer: StateTransfer.COMPLETE,
          })
          .pipe(
            map((businessCase) => {
              return StateLibBusinessCasePageActions.updateBusinessCaseState({
                payload: {
                  businessCaseState: businessCase.state,
                  id: action.payload.businessCaseId,
                },
              });
            }),
            catchError((err) =>
              of(
                BusinessCaseApiActions.completeBusinessCaseFailure({
                  payload: err,
                }),
              ),
            ),
          );
      }),
    );
  });

  closeBusinessCaseSetStatus$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        BusinessCasePageActions.completeBusinessCase,
        BusinessCasePageActions.reactivateBusinessCase,
      ),
      concatLatestFrom(() => this.store.select(selectCustomerKey)),
      switchMap(([action, customerKey]) => {
        return this.participantControllerService
          .updateParticipantStatePerception({
            businessCaseId: action.payload.businessCaseId,
            newState: prepareCaseStatus(action.payload.status),
          })
          .pipe(
            map(() => {
              return BusinessCaseApiActions.changeBusinessCaseStatusSuccess({
                payload: {
                  customerKey: customerKey,
                  status: action.payload.status,
                },
              });
            }),
            catchError((err) => {
              return of(
                BusinessCaseApiActions.changeBusinessCaseStatusFailure(err),
              );
            }),
          );
      }),
    );
  });

  reactivateBusinessCaseSetState$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCasePageActions.reactivateBusinessCase),
      switchMap((action) => {
        return this.businessCaseControllerService
          .setState({
            businessCaseId: action.payload.businessCaseId,
            stateTransfer: StateTransfer.REACTIVATE,
          })
          .pipe(
            map((businessCase) =>
              StateLibBusinessCasePageActions.updateBusinessCaseState({
                payload: {
                  businessCaseState: businessCase.state,
                  id: action.payload.businessCaseId,
                },
              }),
            ),
            catchError((err) =>
              of(
                BusinessCaseApiActions.reactivateBusinessCaseFailure({
                  payload: err,
                }),
              ),
            ),
          );
      }),
    );
  });

  addPlatformManagerToCase$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCasePageActions.addPlatformManagerToCase),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseId),
        this.store.select(selectUserId),
      ]),
      switchMap(([, businessCaseId, userId]) => {
        return this.participantControllerService
          .addParticipantUserToBusinessCase({
            businessCaseId,
            userId,
          })
          .pipe(
            map((participantCustomer) =>
              BusinessCaseApiActions.addPlatformManagerToCaseSuccess({
                payload: participantCustomer,
              }),
            ),
            catchError(() =>
              of(BusinessCaseApiActions.addPlatformManagerToCaseFailure()),
            ),
          );
      }),
    ),
  );

  addPlatformManagerToCaseFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(BusinessCaseApiActions.addPlatformManagerToCaseFailure),
        tap(() => this.finToastService.show(Toast.error())),
      ),
    { dispatch: false },
  );

  deactivateSocketForBusinessCase$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibBusinessCasePageActions.clearBusinessCase),
        tap(() => {
          this.socketService.deactivateSocket(SocketType.CHAT);
          this.socketService.deactivateSocket(SocketType.PORTAL);
          this.socketService.deactivateSocket(SocketType.CADR);
        }),
      ),
    { dispatch: false },
  );

  constructor(
    private actions$: Actions,
    private businessCaseControllerService: BusinessCaseControllerService,
    private informationControllerService: InformationControllerService,
    private dateService: DateService,
    private companyService: CompanyControllerService,
    private store: Store,
    private coreBankingService: CoreBankingSystemIntegrationControllerService,
    private companyInformationControllerService: CompanyInformationService,
    private mailForwardingControllerService: EmailForwardingService,
    private router: Router,
    private participantControllerService: ParticipantControllerService,
    private finToastService: FinToastService,
    private modalService: ModalService,
    private finModalService: FinModalService,
    private location: Location,
    private userManagementControllerService: UserManagementControllerService,
    private finStructureControllerService: FinStructureControllerService,
    private financingDetailsMirroringService: FinStructureMirroringControllerService,
    private tokenManegementService: TokenManagementService,
    private socketService: SocketService,
  ) {}
  readonly finSize = FinSize;
}
