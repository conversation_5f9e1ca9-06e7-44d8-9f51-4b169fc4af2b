import { HttpErrorResponse } from '@angular/common/http';
import {
  CurrentUsersInBusinessCaseResponse,
  ParticipantCustomer,
} from '@fincloud/swagger-generator/business-case-manager';
import { BusinessCaseParticipantCustomer } from '@fincloud/swagger-generator/exchange';
import { FinStructureField } from '@fincloud/swagger-generator/financing-details';
import { BusinessCase } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const loadCustomerDataExportTokenSuccess = createAction(
  '[BusinessCase API] Load customer data export token Success',
  props<{ payload: { hasActiveToken: boolean; businessCases: string[] } }>(),
);
export const loadCustomerDataExportTokenFailure = createAction(
  '[BusinessCase API] Load customer data export token Failure',
);
export const partialRefreshBusinessCaseSuccess = createAction(
  '[BusinessCase API] Refresh Success',
  props<{ payload: BusinessCase }>(),
);
export const updateParticipationAmountSuccess = createAction(
  '[BusinessCase API] Update Participation Amount Success',
  props<{ payload: BusinessCaseParticipantCustomer }>(),
);
export const updateParticipationAmountFailure = createAction(
  '[BusinessCase API] Update Participation Amount Failure',
  props<{ payload: HttpErrorResponse }>(),
);
export const loadReasonsForClosingCaseSuccess = createAction(
  '[BusinessCase API] Get Reasons For Closing a Business Case Success',
  props<{ payload: string[] }>(),
);

export const loadReasonsForClosingCaseFailure = createAction(
  '[BusinessCase API] Get Reasons For Closing a Business Case Failure',
  props<{ payload: HttpErrorResponse }>(),
);

export const loadBusinessCaseStatusesSuccess = createAction(
  '[BusinessCase API] Get Business Case Statuses Success',
  props<{
    payload: string[];
  }>(),
);

export const loadBusinessCaseStatusesFailure = createAction(
  '[BusinessCase API] Get Business Case Statuses Failure',
  props<{ payload: HttpErrorResponse }>(),
);

export const changeBusinessCaseStatusSuccess = createAction(
  '[BusinessCase API] Change Business Case Status Success',
  props<{
    payload: {
      customerKey: string;
      status: string;
      targetLead?: boolean;
    };
  }>(),
);

export const changeBusinessCaseStatusFailure = createAction(
  '[BusinessCase API] Change Business Case Status Failure',
  props<{ payload: HttpErrorResponse }>(),
);

export const loadMainRefsCommonFieldsSuccess = createAction(
  '[BusinessCase API] Set main refs common fields Success',
  props<{ payload: FinStructureField[] }>(),
);

export const showCompleteBusinessCaseModalSuccess = createAction(
  '[BusinessCase API] Show Close Business Case Modal Success',
);

export const showReactivateBusinessCaseModalSuccess = createAction(
  '[BusinessCase API] Show Reactivate Business Case Modal Success',
);

export const completeBusinessCaseFailure = createAction(
  '[BusinessCase API] Close Business Case Failure',
  props<{
    payload: HttpErrorResponse;
  }>(),
);

export const reactivateBusinessCaseFailure = createAction(
  '[BusinessCase API] Reactivate Business Case Failure',
  props<{
    payload: HttpErrorResponse;
  }>(),
);

export const loadCurrentUsersFromMyOrganizationInBusinessCaseSuccess =
  createAction(
    '[BusinessCase API] Load current users from my organization Success',
    props<{
      payload: CurrentUsersInBusinessCaseResponse;
    }>(),
  );

export const loadCurrentUsersFromMyOrganizationInBusinessCaseFailure =
  createAction(
    '[BusinessCase API] Load current users from my organization Failure',
  );

export const loadBusinessCaseFailure = createAction(
  '[BusinessCase API] Load Failure',
  props<{ payload: string }>(),
);

export const addPlatformManagerToCaseSuccess = createAction(
  '[BusinessCase API] Add Platform Manager to Case Success',
  props<{
    payload: ParticipantCustomer;
  }>(),
);

export const addPlatformManagerToCaseFailure = createAction(
  '[BusinessCase API] Add Platform Manager to Case Failure',
);
