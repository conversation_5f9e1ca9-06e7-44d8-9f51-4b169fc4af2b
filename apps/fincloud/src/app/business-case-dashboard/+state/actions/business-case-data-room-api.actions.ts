import {
  Group,
  Information,
} from '@fincloud/swagger-generator/business-case-manager';
import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';

import { DocumentCategoryDto } from '@fincloud/swagger-generator/document';
import { DataRoomChats } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const deleteFieldBusinessCaseDataRoomSuccess = createAction(
  '[Business case - Data Room API]  Field deleted Success',
  props<{
    field: Information | CompanyInformation;
    chat?: DataRoomChats;
    successMessage?: string;
    groupsOrdered?: Group[];
  }>(),
);

export const deleteFieldBusinessCaseDataRoomFailure = createAction(
  '[Business case - Data Room API]  Field deleted Failure',
  props<{
    errorMessage?: string;
  }>(),
);

export const loadDocumentFieldCategoriesSuccess = createAction(
  '[Business case - Data Room API] Load document field categories Success',
  props<{ payload: DocumentCategoryDto[] }>(),
);

export const loadDocumentFieldCategoriesFailure = createAction(
  '[Business case - Data Room API] Load document field categories Failure',
);

export const dataRoomUploadFileSuccess = createAction(
  '[Business case - Data Room API] Upload file Success',
  props<{ fieldKey: string }>(),
);

export const dataRoomUploadFileFailure = createAction(
  '[Business case - Data Room API] Upload file Failure',
  props<{ error: unknown; fieldKey: string }>(),
);
