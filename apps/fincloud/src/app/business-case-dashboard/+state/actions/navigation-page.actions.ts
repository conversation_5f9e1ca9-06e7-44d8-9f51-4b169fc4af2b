import { NAVIGATION_ITEM } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const setActiveNavigationItem = createAction(
  '[Navigation Page] Set active navigation item',
  props<{ payload: NAVIGATION_ITEM }>(),
);

export const selectOverviewGeneral = createAction(
  '[Navigation Overview General Page] Select',
);

export const selectOverviewKpi = createAction(
  '[Navigation Overview KPI Page] Select',
);

export const selectFinancingDetailsMyParticipation = createAction(
  '[Navigation Financing Details My Participation Page] Select',
);

export const selectFinancingDetailsFinancingStructure = createAction(
  '[Navigation Financing Details Financing Structure Page] Select',
);

export const selectFinancingDetailsSharedFinancingStructure = createAction(
  '[Navigation Financing Details Shared Financing Structure Page] Select',
);

export const selectCollaborationMyPartners = createAction(
  '[Navigation Collaboration My Partners Page] Select',
);

export const clearCollaboration = createAction(
  '[Navigation Collaboration Page] Clear',
);

export const selectDataRoomCase = createAction(
  '[Navigation Data room Case Page] Select',
);

export const selectDataRoomCompany = createAction(
  '[Navigation Data room Company Page] Select',
);
