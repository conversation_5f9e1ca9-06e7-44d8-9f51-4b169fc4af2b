import { CompanyAnalysisNavigationItems } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const setActiveNavigationItem = createAction(
  '[Navigation Page] Set active navigation item',
  props<{ payload: CompanyAnalysisNavigationItems }>(),
);
export const selectDataRoomOwn = createAction(
  '[Navigation Data room Own Page] Select',
);
export const selectDataRoomShared = createAction(
  '[Navigation Data room Shared Page] Select',
);
