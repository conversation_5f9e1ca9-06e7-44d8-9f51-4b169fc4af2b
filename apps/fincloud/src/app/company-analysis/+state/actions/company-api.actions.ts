import {
  CompaniesOnSameAddressResponseDto,
  Company,
  CompanyBranch,
  CompanyProfile,
} from '@fincloud/swagger-generator/company';
import { CompanyFolderResponseDto } from '@fincloud/swagger-generator/handelsregister';
import { BusinessCaseCompanyAnalysis } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const loadCompanyBusinessCasesSuccess = createAction(
  '[Company API] Load Company business cases Success',
  props<{ payload: BusinessCaseCompanyAnalysis[] }>(),
);
export const loadCompanyBusinessCasesFailure = createAction(
  '[Company API] Load Company business cases Failure',
);
export const loadCompanySuccess = createAction(
  '[Company API] Load Company Success',
  props<{ payload: Company }>(),
);
export const reloadCompanySuccess = createAction(
  '[Company API] Reload Company Success',
  props<{ payload: Company }>(),
);
export const loadCompanyFailure = createAction(
  '[Company API] Load Company Failure',
);
export const loadCompanyProfileSuccess = createAction(
  '[Company API] Load Company profile Success',
  props<{ payload: CompanyProfile }>(),
);
export const loadCompanyProfileFailure = createAction(
  '[Company API] Load Company profile Failure',
);
export const loadCompanyDocumentsSuccess = createAction(
  '[Company API] Load Company documents Success',
  props<{ payload: CompanyFolderResponseDto[] }>(),
);
export const loadCompanyDocumentsFailure = createAction(
  '[Company API] Load Company documents Failure',
);
export const loadDocumentTypesAvailabilityFailure = createAction(
  '[Company API] Load Document Types Availability Failure',
);
export const loadCompanyBranchesSuccess = createAction(
  '[Company API] Load Company Branches Success',
  props<{ payload: CompanyBranch[] }>(),
);
export const loadCompanyBranchesFailure = createAction(
  '[Company API] Load Company Branches Failure',
);
export const loadCompaniesOnSameAddressSuccess = createAction(
  '[Company API] Load Companies On Same Address Success',
  props<{
    payload: {
      branchId: string;
      companiesOnSameAddress: CompaniesOnSameAddressResponseDto;
    }[];
  }>(),
);
export const loadCompaniesOnSameAddressFailure = createAction(
  '[Company API] Load Companies On Same Address Failure',
);

export const loadCompanyCrawlErrorSuccess = createAction(
  '[Company API] load has error is Success',
  props<{
    hasCrawlError: boolean;
  }>(),
);

export const loadCompanyCrawlErrorFailure = createAction(
  '[Company API] hasCrawlError Failure',
);

export const companyDataRoomUploadFileSuccess = createAction(
  '[Company - Data Room API] Upload file Success',
  props<{ fieldKey: string }>(),
);

export const companyDataRoomUploadFileFailure = createAction(
  '[Company - Data Room API] Upload file Failure',
  props<{ error: unknown; fieldKey: string }>(),
);
