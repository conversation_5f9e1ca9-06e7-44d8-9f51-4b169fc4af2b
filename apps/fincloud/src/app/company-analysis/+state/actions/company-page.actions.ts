import {
  CadrTemplate,
  Company,
  CompanyBranch,
  Information,
  SharedCadrdto,
} from '@fincloud/swagger-generator/company';
import { FieldDto } from '@fincloud/swagger-generator/demo';
import { DocumentAvailabilityResponseDto } from '@fincloud/swagger-generator/handelsregister';
import {
  BusinessCaseCompanyAnalysis,
  CadrShareObjectWithName,
  CompanyDocument,
  TreeNode,
  ValueChangeModel,
} from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const setCompanyId = createAction(
  '[Company Page] Set Company ID',
  props<{ payload: string }>(),
);
export const clearCompanyId = createAction('[Company Page] Clear Company ID');
export const clearCompanyState = createAction(
  '[Company Page] Clear company state',
);
export const setSectionIntoView = createAction(
  '[Company Page] Set Section Into View',
  props<{ payload: number }>(),
);
export const loadCadrTemplate = createAction(
  '[Company Page] Load CADR template',
);
export const loadCompanyBusinessCases = createAction(
  '[Company Page] Load Company business cases success',
  props<{ payload: BusinessCaseCompanyAnalysis[] }>(),
);
export const loadCompanyDocuments = createAction(
  '[Company Page] Load Company documents',
);
export const setSelectedCompanyDocuments = createAction(
  '[Company Page] Set Selected Company documents',
  props<{ payload: TreeNode<CompanyDocument>[] }>(),
);
export const clearSelectedCompanyDocuments = createAction(
  '[Company Page] Clear Selected Company documents',
);
export const loadDocumentTypesAvailability = createAction(
  '[Company Page] Load Document Types Availability',
  props<{ payload: DocumentAvailabilityResponseDto }>(),
);
export const loadCompanyBranches = createAction(
  '[Company Page] Load Company Branches',
  props<{ payload: string }>(),
);
export const addCompanyBranch = createAction(
  '[Company Page] Add Company Branch',
  props<{ payload: CompanyBranch }>(),
);
export const updateCompanyBranch = createAction(
  '[Company Page] Update Company Branch',
  props<{ payload: CompanyBranch }>(),
);
export const removeCompanyBranch = createAction(
  '[Company Page] Remove Company Branch',
  props<{ payload: CompanyBranch }>(),
);
export const setInformation = createAction(
  '[Company Page] Set company information',
  props<{ payload: Information }>(),
);
export const setCadRtemplate = createAction(
  '[Company Page] Set customer CADR template',
  props<{ payload: CadrTemplate }>(),
);
export const setSharedObjectsWithNames = createAction(
  '[Company Page] Set CADR shared objects',
  props<{ payload: CadrShareObjectWithName[] }>(),
);
export const setSelectedSharedCadr = createAction(
  '[Company Page] Set selected shared CADR',
  props<{ payload: SharedCadrdto }>(),
);
export const clearSelectedSharedCadr = createAction(
  '[Company Page] Clear selected shared CADR',
);
export const reloadCompanyBusinessCases = createAction(
  '[Company Page] Reload Company Business Cases',
  props<{ payload: string }>(),
);
export const setCadrSharedLatestChosenGroup = createAction(
  '[Company Page] Set CADR shared latest chosen group',
  props<{ payload: string }>(),
);
export const reloadCadrShareObjects = createAction(
  '[Company Page] Reload CADR share objects',
  props<{ payload: Company }>(),
);
export const addCadrShareObject = createAction(
  '[Company Page] Add CADR share object',
  props<{ payload: CadrShareObjectWithName }>(),
);
export const removeCadrShareObject = createAction(
  '[Company Page] Remove CADR share object',
  props<{ payload: string }>(),
);
export const setLastVisitedUrl = createAction(
  '[Company Page] Set last visited url',
  props<{ payload: string }>(),
);
export const deleteFieldBusinessCaseDataRoomCompany = createAction(
  '[Company Business Case Data Room Company Page] Delete field business case data room company',
  props<{
    payload: {
      fieldDto: FieldDto;
      information: Information;
    };
  }>(),
);
export const getBusinessCaseDataRoomCompanyFieldInformation = createAction(
  '[Company Business Case Data Room Company Page] Get business case data room company field information',
  props<{
    payload: {
      fieldDto: FieldDto;
      information: Information;
    };
  }>(),
);

export const companyDataRoomUploadFile = createAction(
  '[Company - Data Room Page] Upload file',
  props<{ valueChange: ValueChangeModel }>(),
);
