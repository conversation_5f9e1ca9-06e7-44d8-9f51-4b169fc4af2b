import { CadrGroup } from '@fincloud/swagger-generator/company';
import { DocumentCategoryDto } from '@fincloud/swagger-generator/document';
import { createAction, props } from '@ngrx/store';

export const deleteFieldDataRoomCompanySuccess = createAction(
  '[Company Business Case Data Room Company API] Delete field business case data room company Success',
);

export const deleteFieldDataRoomCompanyFailure = createAction(
  '[Company Business Case Data Room Company API] Delete field business case data room company Failure',
  props<{ errorMessage: string }>(),
);

export const updateDataRoomCompanyFieldGroupsSuccess = createAction(
  '[Attach Business Case Data Room Company API] Update business case data room company field groups Success',
  props<{ groupsOrdered: CadrGroup[] }>(),
);

export const updateDataRoomCompanyFieldGroupsFailure = createAction(
  '[Attach Business Case Data Room Company API] Update business case data room company  field groups Failure',
  props<{ errorMessage: string }>(),
);

export const loadDocumentFieldCategoriesSuccess = createAction(
  '[Company Business Case Data Room Company API] Load document field categories Success',
  props<{ payload: DocumentCategoryDto[] }>(),
);

export const loadDocumentFieldCategoriesFailure = createAction(
  '[Company Business Case Data Room Company API] Load document field categories Failure',
);

export const getDataRoomCompanyFieldInformationFailure = createAction(
  '[Attach Business Case Data Room Company API] Update business case data room company  field groups Failure',
  props<{ errorMessage: string }>(),
);
