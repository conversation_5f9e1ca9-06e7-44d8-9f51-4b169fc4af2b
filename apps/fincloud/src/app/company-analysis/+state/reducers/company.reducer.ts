import { StateLibCompanyPageActions } from '@fincloud/state/company-analysis';
import { CompanyAnalysisState } from '@fincloud/types/models';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import { clone, keyBy, mapValues, merge, partition } from 'lodash-es';
import { COMPANY_ANALYSIS_INITIAL_STATE } from '../../utils/company-analysis-initial-state';
import {
  CompanyApiActions,
  CompanyDataRoomBusinessCaseApiActions,
  CompanyPageActions,
} from '../actions';

export const companyReducer: ActionReducer<CompanyAnalysisState, Action> =
  createReducer(
    COMPANY_ANALYSIS_INITIAL_STATE,
    on(
      CompanyApiActions.loadCompanySuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          company: action.payload,
        };
      },
    ),
    on(
      CompanyApiActions.reloadCompanySuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          company: action.payload,
        };
      },
    ),
    on(
      CompanyDataRoomBusinessCaseApiActions.loadDocumentFieldCategoriesSuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          documentFieldCategories: action.payload,
        };
      },
    ),
    on(
      CompanyDataRoomBusinessCaseApiActions.loadDocumentFieldCategoriesFailure,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          documentFieldCategories: [],
        };
      },
    ),
    on(
      StateLibCompanyPageActions.setCompany,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          company: action.payload,
        };
      },
    ),
    on(
      StateLibCompanyPageActions.loadCompany,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyId: action.payload,
        };
      },
    ),
    on(
      CompanyApiActions.loadCompanyBusinessCasesSuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyBusinessCases: action.payload,
          companyBusinessCasesLoaded: true,
        };
      },
    ),
    on(
      CompanyApiActions.loadCompanyBusinessCasesFailure,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          companyBusinessCases: [],
          companyBusinessCasesLoaded: true,
        };
      },
    ),

    on(CompanyApiActions.loadCompanyFailure, (state): CompanyAnalysisState => {
      return {
        ...state,
        company: null,
      };
    }),
    on(
      CompanyApiActions.loadCompanyProfileFailure,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          companyProfile: null,
        };
      },
    ),
    on(
      CompanyApiActions.loadCompanyProfileSuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyProfile: action.payload,
        };
      },
    ),
    on(
      CompanyApiActions.loadCompanyDocumentsSuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyDocuments: action.payload,
        };
      },
    ),
    on(
      CompanyPageActions.loadDocumentTypesAvailability,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          documentTypesAvailability: action.payload,
        };
      },
    ),
    on(
      CompanyApiActions.loadCompanyDocumentsFailure,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          companyDocuments: null,
        };
      },
    ),
    on(
      CompanyPageActions.setSelectedCompanyDocuments,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          selectedCompanyDocuments: action.payload,
        };
      },
    ),
    on(
      CompanyPageActions.clearSelectedCompanyDocuments,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          selectedCompanyDocuments: [],
        };
      },
    ),
    on(
      CompanyPageActions.setSectionIntoView,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          sectionIntoView: action.payload,
        };
      },
    ),
    on(
      CompanyApiActions.loadDocumentTypesAvailabilityFailure,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          documentTypesAvailability: null,
        };
      },
    ),
    on(CompanyPageActions.clearCompanyState, (): CompanyAnalysisState => {
      return {
        ...COMPANY_ANALYSIS_INITIAL_STATE,
      };
    }),
    on(
      CompanyApiActions.loadCompanyBranchesSuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyBranches: action.payload || [],
        };
      },
    ),
    on(
      CompanyPageActions.addCompanyBranch,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyBranches: [...state.companyBranches, action.payload],
        };
      },
    ),
    on(
      CompanyPageActions.updateCompanyBranch,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyBranches: state.companyBranches.map((b) => {
            if (b.id === action.payload.id) {
              return action.payload;
            }
            return b;
          }),
        };
      },
    ),
    on(
      CompanyPageActions.removeCompanyBranch,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyBranches: state.companyBranches.filter((b) => {
            return b.id !== action.payload.id;
          }),
        };
      },
    ),
    on(
      CompanyApiActions.loadCompanyBranchesFailure,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          companyBranches: [],
        };
      },
    ),
    on(
      CompanyApiActions.loadCompaniesOnSameAddressSuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companiesOnSameAddress: merge(
            clone(state.companiesOnSameAddress),
            mapValues(
              keyBy(action.payload, 'branchId'),
              (v) => v.companiesOnSameAddress,
            ),
          ),
        };
      },
    ),
    on(
      CompanyApiActions.loadCompaniesOnSameAddressFailure,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          companiesOnSameAddress: null,
        };
      },
    ),
    on(
      CompanyPageActions.setInformation,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          information: action.payload,
        };
      },
    ),
    on(
      CompanyPageActions.setCadRtemplate,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          cadrTemplate: action.payload,
        };
      },
    ),
    on(
      CompanyPageActions.setSelectedSharedCadr,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          selectedSharedCadr: action.payload,
        };
      },
    ),
    on(
      CompanyPageActions.clearSelectedSharedCadr,
      (state): CompanyAnalysisState => {
        return {
          ...state,
          selectedSharedCadr: null,
        };
      },
    ),
    on(
      CompanyPageActions.setCadrSharedLatestChosenGroup,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          sharedCADRLatestChosenGroup: action.payload,
        };
      },
    ),
    on(
      CompanyPageActions.setSharedObjectsWithNames,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          shareObjectsWithNames: action.payload,
        };
      },
    ),
    on(
      CompanyPageActions.addCadrShareObject,
      (state, action): CompanyAnalysisState => {
        const storeCadrShareObjects =
          structuredClone(state.shareObjectsWithNames) || [];
        storeCadrShareObjects.push(action.payload);
        return {
          ...state,
          shareObjectsWithNames: storeCadrShareObjects,
        };
      },
    ),
    on(
      CompanyPageActions.removeCadrShareObject,
      (state, action): CompanyAnalysisState => {
        const storeCadrShareObjects =
          structuredClone(state.shareObjectsWithNames) || [];
        const [explicitlyShared, implicitlyShared] = partition(
          storeCadrShareObjects,
          (p) => p.type === 'EXPLICIT',
        );
        const explicitlySharedFiltered = explicitlyShared.filter(
          (es) => es.customerKey !== action.payload,
        );

        return {
          ...state,
          shareObjectsWithNames: [
            ...explicitlySharedFiltered,
            ...implicitlyShared,
          ],
        };
      },
    ),
    on(
      CompanyPageActions.deleteFieldBusinessCaseDataRoomCompany,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          companyFieldInProcessDelete: action.payload,
        };
      },
    ),
    on(
      CompanyApiActions.loadCompanyCrawlErrorSuccess,
      (state, { hasCrawlError }): CompanyAnalysisState => {
        return {
          ...state,
          hasCrawlError,
        };
      },
    ),
    on(
      CompanyPageActions.companyDataRoomUploadFile,
      (state, { valueChange }): CompanyAnalysisState => {
        const uploadingFileBreakdown = state.uploadFilesBreakdownList.find(
          ({ key }) => key === valueChange.information.field.key,
        );

        if (uploadingFileBreakdown) {
          return {
            ...state,
            uploadFilesBreakdownList: state.uploadFilesBreakdownList.map(
              (uploadFileBreakdown) => {
                if (
                  uploadFileBreakdown.key === valueChange.information.field.key
                ) {
                  return {
                    ...uploadFileBreakdown,
                    isUploading: true,
                    hasError: false,
                  };
                }

                return uploadFileBreakdown;
              },
            ),
          };
        }

        return {
          ...state,
          uploadFilesBreakdownList: [
            ...state.uploadFilesBreakdownList,
            {
              key: valueChange.information.field.key,
              isUploading: true,
              hasError: false,
            },
          ],
        };
      },
    ),
    on(
      CompanyApiActions.companyDataRoomUploadFileSuccess,
      (state, { fieldKey }): CompanyAnalysisState => ({
        ...state,
        uploadFilesBreakdownList: state.uploadFilesBreakdownList.filter(
          ({ key }) => key !== fieldKey,
        ),
      }),
    ),
    on(
      CompanyApiActions.companyDataRoomUploadFileFailure,
      (state, { fieldKey }): CompanyAnalysisState => ({
        ...state,
        uploadFilesBreakdownList: state.uploadFilesBreakdownList.map(
          (uploadFileBreakdown) => {
            if (uploadFileBreakdown.key === fieldKey) {
              return {
                ...uploadFileBreakdown,
                isUploading: false,
                hasError: true,
              };
            }

            return uploadFileBreakdown;
          },
        ),
      }),
    ),

    on(
      CompanyDataRoomBusinessCaseApiActions.updateDataRoomCompanyFieldGroupsSuccess,
      (state, action): CompanyAnalysisState => {
        return {
          ...state,
          company: {
            ...state.company,
            companyTemplate: {
              ...state.company.companyTemplate,
              template: {
                ...state.company.companyTemplate.template,
                groupsOrdered: action.groupsOrdered,
              },
            },
          },
        };
      },
    ),
  );
