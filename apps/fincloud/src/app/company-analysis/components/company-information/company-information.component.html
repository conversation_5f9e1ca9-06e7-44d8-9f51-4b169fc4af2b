@if (userCustomerKey$ | async; as customerKey) {
  <div class="page-container" cdkDropListGroup>
    <div class="side-section">
      @if (isAdditionalInfoTab) {
        <app-sidebar-nav></app-sidebar-nav>
      }
      @if (activeId === 'data-room' && isUserFromCompany) {
        <ng-scrollbar class="side-scroll"
          ><app-left-side-navigation
            [editMode]="editMode"
            [isOwnCADR]="isOwnDataRoomSelected"
            cdkDropList
          ></app-left-side-navigation
        ></ng-scrollbar>
      }
    </div>
    <div class="center-section">
      <div class="container">
        <div class="row">
          <div class="col col-12">
            <ui-header-tabs
              [hasTopBar]="isUserFromCompany"
              appSyncPositionSource="data-room"
              [appSyncSourceMinThreshold]="1501"
            >
              @if (isUserFromCompany) {
                <div class="lead-partner-line" topBar>
                  <div>
                    <ng-template
                      [ngxPermissionsOnly]="permissionHtml.PERM_0044"
                    >
                      @if (isOwnDataRoomSelected && activeId === 'data-room') {
                        <ui-switch
                          class="view-mode-switch"
                          label="Bearbeitungsmodus"
                          i18n-label="@@companyInformation.switch.dataRoom"
                          labelPosition="right"
                          [(ngModel)]="editMode"
                          (ngModelChange)="emitEditModeChange()"
                          [style]="'inverted'"
                          labelColor="white"
                        >
                        </ui-switch>
                      }
                    </ng-template>
                  </div>
                </div>
              }
              <app-info-card cdkDropList cardContent></app-info-card>
              <div headerTabs>
                <div class="nav-wrapper sticky-top">
                  <nav class="nav-container">
                    <ul
                      appMetaKeyOrCtrlKey
                      [path]="'company-analysis'"
                      [id]="companyId"
                      [tab]="selectedTabId"
                      (executeOnClickChange)="
                        changeOrOpenNewTab($event, customerKey)
                      "
                      ngbNav
                      #nav="ngbNav"
                      [activeId]="activeNavigationItem$ | async"
                      class="nav"
                      (navChange)="onTabChange($event)"
                    >
                      @if (
                        isUserFromCompany &&
                        (isUserFromGuestOrganization$ | async | isFalsy)
                      ) {
                        <li ngbNavItem="data-room">
                          <a
                            ngbNavLink
                            appAnchorPathBuilder
                            [anchorPathSegments]="[
                              'company-analysis',
                              companyId,
                            ]"
                            i18n="
                              @@companyAnalysis.companyInformation.navLink.dataRoom"
                            >Data Room</a
                          >
                          <ng-template ngbNavContent>
                            <app-data-room-tabs
                              [editMode]="editMode"
                              [customerKey]="customerKey"
                              [companyId]="companyId"
                              [selectedTab]="selectedDataRoomTab"
                              (tabChanged)="
                                isOwnDataRoomSelected =
                                  $event === 'OWN_DATA_ROOM'
                              "
                            ></app-data-room-tabs>
                          </ng-template>
                        </li>
                      }
                      <li ngbNavItem="network">
                        <a
                          ngbNavLink
                          appAnchorPathBuilder
                          [anchorPathSegments]="[
                            'company-analysis',
                            companyId,
                            'network',
                          ]"
                          i18n="@@companyAnalysis.companyInformation.navLink"
                          >KYC</a
                        >
                        <ng-template ngbNavContent>
                          @if (activeId === networkTab) {
                            <app-company-graph-container></app-company-graph-container>
                          }
                        </ng-template>
                      </li>
                      <li ngbNavItem="documents">
                        <a
                          ngbNavLink
                          appAnchorPathBuilder
                          [anchorPathSegments]="[
                            'company-analysis',
                            companyId,
                            'documents',
                          ]"
                          i18n="
                            @@companyAnalysis.companyInformation.navLink.documents"
                          >Dokumente</a
                        >
                        <ng-template ngbNavContent>
                          <app-commercial-register-documents></app-commercial-register-documents>
                        </ng-template>
                      </li>
                      @if (
                        isUserFromCompany ||
                        (isUserFromGuestOrganization$ | async)
                      ) {
                        <li ngbNavItem="branches">
                          <a
                            ngbNavLink
                            appAnchorPathBuilder
                            [anchorPathSegments]="[
                              'company-analysis',
                              companyId,
                              'branches',
                            ]"
                            i18n="@@companyInformation.navLInk.branches"
                            >Zweigstellen</a
                          >
                          <ng-template ngbNavContent>
                            <app-company-branches></app-company-branches>
                          </ng-template>
                        </li>
                      }
                      @if (isUserFromGuestOrganization$ | async | isFalsy) {
                        <li ngbNavItem="business-cases">
                          <a
                            ngbNavLink
                            appAnchorPathBuilder
                            [anchorPathSegments]="[
                              'company-analysis',
                              companyId,
                              'business-cases',
                            ]"
                            i18n="
                              @@companyAnalysis.companyInformation.navLink.cases"
                            >Finanzierungsfälle</a
                          >
                          <ng-template ngbNavContent>
                            <app-other-business-cases
                              [companyId]="companyId"
                            ></app-other-business-cases>
                          </ng-template>
                        </li>
                      }
                      <li ngbNavItem="additional-information">
                        <a
                          ngbNavLink
                          appAnchorPathBuilder
                          [anchorPathSegments]="[
                            'company-analysis',
                            companyId,
                            'additional-information',
                          ]"
                          i18n="
                            @@companyAnalysis.companyInformation.navLink.information"
                          >Zusatzinformation</a
                        >
                        <ng-template ngbNavContent>
                          <app-company-information-sections
                            [companyId]="companyId"
                            [isCustomerLeader]="isUserFromCompany"
                            [isUserFromGuestOrganization]="
                              isUserFromGuestOrganization$ | async
                            "
                          ></app-company-information-sections>
                        </ng-template>
                      </li>
                    </ul>
                  </nav>
                </div>
                <div class="nav-outlet" [ngbNavOutlet]="nav"></div>
              </div>
            </ui-header-tabs>
          </div>
        </div>
      </div>
    </div>
    <div class="side-section side-section-right">
      <ng-scrollbar class="side-scroll" position="invertY" visibility="hover">
        @if (editMode && hasGroups && isOwnDataRoomSelected) {
          <app-data-room-template-fields></app-data-room-template-fields>
        }
        @if (
          isUserFromCompany &&
          (showCadrActions$ | async) &&
          isOwnDataRoomSelected &&
          activeId === 'data-room'
        ) {
          <div class="download-container">
            <div
              class="title"
              i18n="@@companyAnalysis.companyInformation.navLink.action"
            >
              Aktionen
            </div>
            @if (
              editMode &&
              company?.companyTemplate?.template?.groupsOrdered?.length
            ) {
              <app-group-visibility-card
                class="tw-mb-3 tw-block"
                [company]="company"
                [groups]="company?.companyTemplate?.template.groupsOrdered"
                [participants]="sharedWithCustomers$ | async"
              ></app-group-visibility-card>
            }
            <app-download-files-as-zip
              page="company"
              [customerKey]="customerKey"
              [isCADR]="true"
            ></app-download-files-as-zip>
          </div>
        }
      </ng-scrollbar>
    </div>
  </div>
}
