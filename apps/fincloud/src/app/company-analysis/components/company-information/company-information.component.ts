import { Component, DestroyRef, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DataRoomVisibilityCustomer } from '@fincloud/neoshare/data-room';
import {
  selectCompany,
  selectCompanyDataRoomInformation,
} from '@fincloud/state/company-analysis';
import { selectIsCustomerGuest } from '@fincloud/state/customer';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { Company } from '@fincloud/swagger-generator/company';
import { Permission } from '@fincloud/types/enums';
import {
  AppState,
  CompanyAnalysisNavigationItems,
} from '@fincloud/types/models';
import { NgbNavChangeEvent } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  filter,
  map,
  withLatestFrom,
} from 'rxjs';
import {
  selectActiveNavigationItem,
  selectSelectedDataRoomOwn,
  selectSelectedDataRoomShared,
  selectShareObjectsWithNames,
} from '../../+state';
import { CompanyPageActions } from '../../+state/actions';
import { DataRoomTab } from '../../enums/data-room-tab';
import { NavTab } from '../../enums/nav-tabs';
import { CompanyTabNavigationService } from '../../services/company-tab-navigation.service';

@Component({
  selector: 'app-company-information',
  templateUrl: './company-information.component.html',
  styleUrls: ['./company-information.component.scss'],
})
export class CompanyInformationComponent implements OnInit, OnDestroy {
  activeId: NavTab;
  companyId: string;
  company: Company;
  isOwnDataRoomSelected = true;
  editMode: boolean;
  hasGroups: boolean;
  networkTab = NavTab.NETWORK;

  isUserFromCompany: boolean;
  loaded = false;
  selectedTabId: string;
  selectedDataRoomTab: DataRoomTab | null = null;
  activeNavigationItem$: Observable<CompanyAnalysisNavigationItems>;

  userCustomerKey$ = this.store.select(selectUserCustomerKey);

  editModeChanged = new BehaviorSubject(false);

  showCadrActions$ = combineLatest([
    this.store.select(selectCompany),
    this.store.select(selectCompanyDataRoomInformation),
    this.editModeChanged,
  ]).pipe(
    takeUntilDestroyed(this.destroyRef),
    map(([company, information]) => {
      return (
        this.activeId === 'data-room' &&
        ((company &&
          Object.values(information || {})?.some(
            (info) => info.field.fieldType === 'DOCUMENT' && info.value,
          )) ||
          (this.editMode &&
            this.company?.companyTemplate?.template?.groupsOrdered?.length))
      );
    }),
  );

  sharedWithCustomers$: Observable<DataRoomVisibilityCustomer[]> = this.store
    .select(selectShareObjectsWithNames)
    .pipe(
      filter(Boolean),
      takeUntilDestroyed(this.destroyRef),
      map((shareObjects) => {
        if (!shareObjects?.length) {
          return [];
        }

        const customersExplicitShare = shareObjects.filter(
          (so) => so.type === 'EXPLICIT',
        );
        const customersImplicitShareWithoutExplicit = shareObjects.filter(
          (so) =>
            so.type === 'IMPLICIT' &&
            !customersExplicitShare
              .map((ces) => ces.customerKey)
              .includes(so.customerKey),
        );

        return [
          ...customersExplicitShare,
          ...customersImplicitShareWithoutExplicit,
        ].map((c) => {
          return {
            customerKey: c.customerKey,
            customerName: c.customerName,
            shareType: c.type,
          };
        });
      }),
    );

  isUserFromGuestOrganization$ = this.store.select(selectIsCustomerGuest);

  get permissionHtml(): typeof Permission {
    return Permission;
  }

  constructor(
    private destroyRef: DestroyRef,
    private navigationService: CompanyTabNavigationService,
    private route: ActivatedRoute,
    private store: Store<AppState>,
    private router: Router,
    private companyTabNavigationService: CompanyTabNavigationService,
  ) {}

  ngOnInit(): void {
    this.route.paramMap
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((params) => {
        const id = params.get('id');
        this.companyId = id;
        this.store.dispatch(CompanyPageActions.loadCadrTemplate());
      });

    this.store
      .select(selectCompany)
      .pipe(
        filter(Boolean),
        takeUntilDestroyed(this.destroyRef),
        withLatestFrom(this.store.select(selectUserCustomerKey)),
      )
      .subscribe(([company, userCustomerKey]) => {
        this.company = company;
        this.hasGroups =
          !!company?.companyTemplate?.template?.groupsOrdered?.length;
        this.isUserFromCompany = this.company?.customerKey === userCustomerKey;
      });

    this.route.firstChild.url
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((url) => {
        this.activeId = url[0].path as NavTab;
      });

    this.route.queryParams
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((params) => {
        this.editMode = params?.editMode;
        this.editModeChanged.next(this.editMode);
      });

    const pathToCompanyAnalysisTab =
      this.companyTabNavigationService.parsePathToCompanyAnalysisTab(
        this.router.url,
      );
    this.companyTabNavigationService.selectCompanyTab(pathToCompanyAnalysisTab);
    this.hookIntoNavigationEvents();
    this.hookIntoActiveNavigationItemsUpdates();
    this.hookIntoSelectedDataRoomUpdates();
  }
  get isAdditionalInfoTab(): boolean {
    return this.activeId === 'additional-information';
  }

  private hookIntoSelectedDataRoomUpdates() {
    combineLatest([
      this.store.select(selectSelectedDataRoomShared),
      this.store.select(selectSelectedDataRoomOwn),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([dataRoomShared]) => {
        if (dataRoomShared) {
          this.selectedDataRoomTab = DataRoomTab.SHARED_DATA_ROOM;
        } else {
          this.selectedDataRoomTab = DataRoomTab.OWN_DATA_ROOM;
        }
      });
  }

  emitEditModeChange() {
    this.editModeChanged.next(this.editMode);
  }

  private hookIntoNavigationEvents() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map((e) => {
          return e as NavigationEnd;
        }),
      )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((event: NavigationEnd) => {
        const pathToCompanyAnalysisTab =
          this.companyTabNavigationService.parsePathToCompanyAnalysisTab(
            event.url,
          );
        this.activeId = pathToCompanyAnalysisTab.tab as NavTab;
        this.selectedTabId = this.activeId;
        this.companyTabNavigationService.selectCompanyTab(
          pathToCompanyAnalysisTab,
        );
      });
  }

  private hookIntoActiveNavigationItemsUpdates() {
    this.activeNavigationItem$ = this.store
      .select(selectActiveNavigationItem)
      .pipe(takeUntilDestroyed(this.destroyRef));
  }

  changeOrOpenNewTab(shouldExecuteOnClick: boolean, customerKey: string) {
    if (shouldExecuteOnClick && this.selectedTabId !== undefined) {
      this.activeId = this.selectedTabId as NavTab;
      if (this.activeId === 'data-room') {
        this.store.dispatch(
          CompanyPageActions.setCadrSharedLatestChosenGroup({ payload: null }),
        );
        this.store.dispatch(CompanyPageActions.clearSelectedSharedCadr());
      }

      this.editMode = false;
      this.navigationService.addTabNameToCompanyPageURL(
        this.companyId,
        this.selectedTabId,
        customerKey,
      );

      if (this.activeId === 'data-room' && !this.isOwnDataRoomSelected) {
        this.editMode = false;
      }
    }
  }

  onTabChange(event: NgbNavChangeEvent) {
    this.selectedTabId = event.nextId;
    event.preventDefault();
  }

  ngOnDestroy(): void {
    this.store.dispatch(CompanyPageActions.clearCompanyState());
  }
}
