import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { ContractDto } from '@fincloud/swagger-generator/contract-management';
import { BusinessCaseResultDto } from '@fincloud/swagger-generator/exchange';

import { BusinessCaseModelService } from '@fincloud/core/business-case';
import { DateService } from '@fincloud/core/date';
import { ModalService } from '@fincloud/core/modal';
import { ScrollCommunicationService } from '@fincloud/core/scroll';
import {
  User,
  UserManagementControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import { Permission } from '@fincloud/types/enums';
import { TableRow } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { pick } from 'lodash-es';
import { NgxPermissionsService } from 'ngx-permissions';
import { Observable, catchError, forkJoin, of, shareReplay, tap } from 'rxjs';
import { selectHighlightedContract } from '../../+state/selectors/contract-management.selectors';
import { ContractTableRow } from '../../models/contract-table-row';
import { ALLOWED_FIELDS_TO_SEARCH } from '../../utils/allowed-fields-to-search';
import { getContractTableColumnsConfig } from '../../utils/get-contract-table-columns-config';
import { getDefaultSort } from '../../utils/get-default-sort';
import { ContractDetailsModalComponent } from '../contract-details-modal/contract-details-modal.component';

@Component({
  selector: 'app-contract-table',
  templateUrl: './contract-table.component.html',
  styleUrls: ['./contract-table.component.scss'],
})
export class ContractTableComponent implements OnChanges {
  @Input() contracts: ContractDto[];
  @Input() businessCases: BusinessCaseResultDto[];
  @Input() searchTerm: string;
  @Output() triggerRefresh = new EventEmitter<boolean>();
  rowsToShow: ContractTableRow[];
  allRows: ContractTableRow[];
  columns = getContractTableColumnsConfig();
  defaultSort = getDefaultSort();
  userNames = new Map<string, string>();

  getHighlightedContract$ = this.store.select(selectHighlightedContract).pipe(
    tap((highlightedContract) => {
      if (highlightedContract) {
        this.scrollToHighlightedContract(highlightedContract);
      }
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    private dateService: DateService,
    private userManagementControllerService: UserManagementControllerService,
    private modalService: ModalService,
    private businessCaseModelService: BusinessCaseModelService,
    private ngxPermissionsService: NgxPermissionsService,
    private scrollCommunicationService: ScrollCommunicationService,
    private store: Store,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if ('contracts' in changes) {
      const requests = new Map<string, Observable<User>>();

      this.contracts.forEach((contract) => {
        const userId = contract.createdBy;
        if (this.userNames.has(userId)) {
          return;
        }
        requests.set(
          userId,
          this.userManagementControllerService
            .getUserById({ userId: contract.createdBy })
            .pipe(
              catchError(() => of({ firstName: '', lastName: '' } as User)),
            ),
        );
      });

      requests.size
        ? forkJoin([...requests.values()]).subscribe({
            next: ([...users]) => {
              users.forEach((user) => this.setUserNameById(user));
              this.generateRows();
            },
          })
        : this.generateRows();
    }

    if ('searchTerm' in changes && !changes.searchTerm.firstChange) {
      this.searchByAllFields(this.searchTerm);
    }
  }

  generateRows() {
    this.allRows = this.contracts.map((contract) => {
      const businessCaseForContract = this.businessCases.find(
        (businessCase) => businessCase.id === contract.caseId,
      );
      const financingVolume = businessCaseForContract
        ? this.businessCaseModelService
            .getFinancingVolume(businessCaseForContract)
            .toString()
        : '';
      return {
        autogeneratedBusinessCaseName:
          businessCaseForContract?.autoGeneratedBusinessCaseName,
        companyName:
          businessCaseForContract?.company?.companyInfo?.legalName || '',
        financingVolume: financingVolume,
        createdDate: new Date(contract.signingProcess?.creationDate),
        createdDateFormatted: this.dateService.transformDate(
          contract.signingProcess?.creationDate,
        ),
        createdDateFull: contract.signingProcess?.creationDate,
        createdFrom: this.userNames.get(contract.createdBy),
        expireDate: new Date(contract.dueDate),
        expireDateFormatted: this.dateService.transformDate(contract.dueDate),
        status: this.getContractStatus(contract),
        users: contract.users,
        title: contract.title,
        id: contract.id,
        signingProcess: contract.signingProcess,
        voidedReason: contract.signingProcess.voidedReason,
        contract,
      };
    });

    this.rowsToShow = this.allRows;

    this.scrollToHighlightedContract('highlighted');
  }

  getContractStatus(contract: ContractDto) {
    if (contract.status === 'VOIDED') {
      return 'VOIDED';
    }
    return contract.signingProcess?.status ?? contract.status;
  }

  setUserNameById(user: User) {
    this.userNames.set(user.id, user.firstName + ' ' + user.lastName);
  }

  searchByAllFields(searchTerm: string) {
    this.rowsToShow = this.allRows?.filter((contractRow) => {
      return Object.values(pick(contractRow, ALLOWED_FIELDS_TO_SEARCH))
        .toString()
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
    });
  }

  onRowSelected(row: TableRow) {
    if (!this.ngxPermissionsService.getPermission(Permission.PERM_0054)) {
      return;
    }
    this.modalService.openComponent(
      ContractDetailsModalComponent,
      {
        contractInformation: row.contract,
        tableRow: row,
      },
      { windowClass: 'contract-details-contract' },
      (res) => {
        if (res.success) {
          this.triggerRefreshOnData();
        }
      },
    );
  }

  triggerRefreshOnData() {
    this.triggerRefresh.emit(true);
  }

  private scrollToHighlightedContract(highlightedContract: string) {
    this.cdr.detectChanges();

    // The -250 offset ensures that the target element is scrolled into view with 250 pixels of space above it.
    this.scrollCommunicationService.scrollToElementByClass(
      highlightedContract,
      -250,
    );
  }
}
