<div class="settings-header notifications-context">
  <ui-icon
    (click)="exitSettings()"
    class="go-back notifications-context"
    size="medium"
    name="left"
    color="primary"
  ></ui-icon>
  <h4 class="title" i18n="@@notificationSystem.settings.title">
    Einstellungen
  </h4>
</div>

<ui-horizontal-divider color="gray"></ui-horizontal-divider>

@if (settingsGroup) {
  <div
    class="tw-px-[1.8rem] tw-pt-6 notifications-context"
    [formGroup]="settingsGroup"
  >
    <fin-slide-toggle
      dynamicErrorSpace="dynamic"
      formControlName="USER_ASSIGNMENT"
      label="To-do-Liste"
      i18n-label="@@notificationSystem.settings.todos"
      class="fin-block tw-mb-6 notifications-context"
    ></fin-slide-toggle>
    <fin-slide-toggle
      dynamicErrorSpace="dynamic"
      formControlName="CHAT_RELATED"
      label="Chat"
      i18n-label="@@notificationSystem.settings.chat"
      class="fin-block tw-mb-6 notifications-context"
    ></fin-slide-toggle>
  </div>
}
