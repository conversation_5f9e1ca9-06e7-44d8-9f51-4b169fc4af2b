import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { NotificationType } from '@fincloud/types/models';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'app-notification-icon',
  templateUrl: './notification-icon.component.html',
  styleUrls: ['./notification-icon.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationIconComponent implements OnInit {
  protected size = FinSize;
  @Input() type: NotificationType;

  protected iconName = '';

  ngOnInit() {
    this.iconName = this.getIconName();
  }

  private getIconName(): string {
    switch (this.type) {
      case 'USER_ASSIGNMENT':
        return 'checklist';
      default:
        return '';
    }
  }
}
