import { DatePipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { Router } from '@angular/router';
import { Toast } from '@fincloud/core/toast';
import { StateLibTodosManagementPageActions } from '@fincloud/state/todos-management';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import { ChatManagementControllerService } from '@fincloud/swagger-generator/communication';
import { Notification } from '@fincloud/swagger-generator/platform-notification';
import {
  NotificationMessageType,
  TodosStatus,
  TodosType,
} from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { Store } from '@ngrx/store';
import { catchError, of, take, tap } from 'rxjs';
@Component({
  selector: 'app-notification-system-card',
  templateUrl: './notification-system-card.component.html',
  styleUrls: ['./notification-system-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSystemCardComponent implements OnInit, OnChanges {
  @Input() notification: Notification;

  @Input() customerKey: string;

  @Input() businessCaseName: string;

  @Output() closeNotificationPanel = new EventEmitter();

  @Output() hideNotification = new EventEmitter<Notification>();

  @Output() toggleReadStatus = new EventEmitter<Notification>();

  isCardHovered = false;

  isMenuOpened = false;

  notificationMessage: string;

  boldUserName: string;

  boldBusinessCaseName: string;

  elapsedTime: number;

  timePeriod: 'minutes' | 'hours' | 'days' | 'weeks';

  notificationMessageType = NotificationMessageType;

  private readonly customerIsNotAPartOfTheCaseErrorCode = 'CMNS-1012';
  private readonly generalErrorText = $localize`:@@toast.message.error:Es ist ein Fehler aufgetreten`;
  private readonly noPermissionsToEnterCaseErrorText = $localize`:@@notificationSystem.notification.noPermissionsToEnterCase:Sie haben nicht mehr länger Zugang zu diesem Fall`;

  constructor(
    private router: Router,
    private finToastService: FinToastService,
    private chatManagementControllerService: ChatManagementControllerService,
    private datePipe: DatePipe,
    private store: Store,
  ) {}

  ngOnInit(): void {
    if (this.notification.type !== 'CHAT_RELATED') {
      this.boldUserName = this.boldText(
        this.notification.parameters.autoGeneratedBusinessCaseName,
      );
      this.buildNotificationMessage();
      this.calculateNotificationElapsedTime();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes.businessCaseName &&
      changes.businessCaseName.currentValue &&
      this.notification.type === 'CHAT_RELATED'
    ) {
      this.boldBusinessCaseName = this.boldText(this.businessCaseName);

      if (this.boldUserName) {
        this.buildNotificationMessage();
      }
    }

    if (
      changes.notification &&
      changes.notification.currentValue &&
      this.notification.type === 'CHAT_RELATED'
    ) {
      this.boldUserName = `${this.boldText(this.notification.parameters.name)} `;
      this.calculateNotificationElapsedTime();

      if (this.businessCaseName) {
        this.buildNotificationMessage();
      }
    }
  }

  toggleIsCardHovered() {
    this.isCardHovered = !this.isCardHovered;
  }

  handleMenuContext(isOpened: boolean) {
    this.isMenuOpened = isOpened;
  }

  navigateToNotificationOrigin() {
    switch (this.notification.type) {
      case 'CHAT_RELATED':
        this.chatNotificationRedirects();
        break;
      case 'USER_ASSIGNMENT':
        this.userAssignmentNotificationRedirects();
        break;
    }

    this.closeNotificationPanel.emit();
  }

  private buildNotificationMessage() {
    switch (this.notification.message) {
      case NotificationMessageType.CHAT_MESSAGE_SENT:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.messageSent:${this.boldUserName} hat im Finanzierungsfall ${this.boldBusinessCaseName} eine neue Nachricht gesendet.`;
        break;
      case NotificationMessageType.CHAT_USER_TAGGED:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.userTagged:${this.boldUserName} hat Sie im Chat im Finanzierungsfall ${this.boldBusinessCaseName} getaggt.`;
        break;
      case NotificationMessageType.CHAT_ARCHIVED_AUTOMATICALLY:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.archivedAutomatically:Im Finanzierungsfall ${this.boldBusinessCaseName} wurde ein Chat automatisch archiviert.`;
        break;
      case NotificationMessageType.CHAT_REACTIVATED_BY_USER:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.reactivatedByUser:${this.boldUserName} hat einen Chat im Finanzierungsfall ${this.boldBusinessCaseName} reaktiviert.`;
        break;
      case NotificationMessageType.CHAT_CREATED_AUTOMATICALLY:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.createdAutomatically:Im Finanzierungsfall ${this.boldBusinessCaseName} wurde ein Chat automatisch erstellt.`;
        break;
      case NotificationMessageType.CHAT_REACTIVATED_AUTOMATICALLY:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.reactivatedAutomatically:Im Finanzierungsfall ${this.boldBusinessCaseName} wurde ein Chat automatisch reaktiviert.`;
        break;
      case NotificationMessageType.CHAT_ARCHIVED_MANUALLY:
        this.notificationMessage = $localize`:@@notificationSystem.notification.chatRelated.archivedManually:${this.boldUserName} hat einen Chat im Finanzierungsfall ${this.boldBusinessCaseName} archiviert.`;
        break;
      case NotificationMessageType.USER_ASSIGNMENT_EXPIRY_AFTER_ONE:
        this.notificationMessage = $localize`:@@notificationSystem.notification.userAssignment.expiryAfterOne:Das mit ${this.boldText(this.notification.parameters.autoGeneratedBusinessCaseName)} verknüpfte To-do ist bis heute zu erledigen. Stellen Sie bitte sicher, dass es erledigt wird`;
        break;
      case NotificationMessageType.USER_ASSIGNMENT_EXPIRY_AFTER_THREE:
        this.notificationMessage = $localize`:@@notificationSystem.notification.userAssignment.expiryAfterThree:Das mit ${this.boldText(this.notification.parameters.autoGeneratedBusinessCaseName)} verknüpfte To-do ist bis zum ${this.datePipe.transform(this.notification.parameters.userAssignmentDueDate)} zu erledigen.`;
        break;
      case NotificationMessageType.USER_ASSIGNMENT_EXPIRY_BEFORE_FIVE:
        this.notificationMessage = $localize`:@@notificationSystem.notification.userAssignment.expiryBeforeFive:Das mit ${this.boldText(this.notification.parameters.autoGeneratedBusinessCaseName)} verknüpfte To-do war bis ${this.datePipe.transform(this.notification.parameters.userAssignmentDueDate)} zu erledigen.`;
        break;
      case NotificationMessageType.USER_ASSIGNMENT_REASSIGNED:
        this.notificationMessage = $localize`:@@notificationSystem.notification.userAssignment.reassigned:Eine Aufgabe für ${this.boldText(this.notification.parameters.autoGeneratedBusinessCaseName)} wurde von ${this.boldText(this.notification.parameters.creatorUserFullName)} neu zugewiesen. Für diese Aufgabe ist keine Aktion Ihrerseits mehr erforderlich.`;
        break;
      case NotificationMessageType.USER_ASSIGNMENT_CANCELLED:
        this.notificationMessage = $localize`:@@notificationSystem.notification.manualTodo.todoCanceled:Eine Aufgabe für ${this.boldText(this.notification.parameters.autoGeneratedBusinessCaseName)} wurde von ${this.boldText(this.notification.parameters.closedByUserFullName)} storniert.`;
        break;
    }
  }

  private calculateNotificationElapsedTime() {
    const now = new Date();
    const notificationDate = new Date(this.notification.timestamp);

    // Calculate the elapsed time and if negative we use 0
    const elapsedTimeInSeconds = Math.max(
      0,
      (now.getTime() - notificationDate.getTime()) / 1000,
    );

    if (elapsedTimeInSeconds < 3600) {
      this.timePeriod = 'minutes';
      this.elapsedTime = Math.floor(elapsedTimeInSeconds / 60);
    } else if (elapsedTimeInSeconds < 86400) {
      this.timePeriod = 'hours';
      this.elapsedTime = Math.floor(elapsedTimeInSeconds / 3600);
    } else if (elapsedTimeInSeconds < 604800) {
      this.timePeriod = 'days';
      this.elapsedTime = Math.floor(elapsedTimeInSeconds / 86400);
    } else {
      this.timePeriod = 'weeks';
      this.elapsedTime = Math.floor(elapsedTimeInSeconds / 604800);
    }
  }

  private boldText(text: string): string {
    return `<b>${text}</b>`;
  }

  private userAssignmentNotificationRedirects() {
    let status = TodosStatus.PENDING;

    if (
      this.notification.message ===
      NotificationMessageType.USER_ASSIGNMENT_REASSIGNED
    ) {
      return;
    }

    if (
      this.notification.message ===
      NotificationMessageType.USER_ASSIGNMENT_CANCELLED
    ) {
      status = TodosStatus.CLOSED;
    }

    this.store.dispatch(
      StateLibTodosManagementPageActions.redirectToTodo({
        perspective: TodosType.MY_TASKS,
        status,
        businessCaseId: this.notification.parameters.businessCaseId,
        todoId: this.notification.parameters.userAssignmentId,
        highlight: true,
      }),
    );
  }

  private chatNotificationRedirects() {
    this.chatManagementControllerService
      .getAllChats({
        businessCaseId: this.notification.parameters.caseId,
        includeArchived: true,
      })
      .pipe(
        take(1),
        tap((caseChats) => {
          let route: string;
          if (this.notification.type === 'CHAT_RELATED') {
            const chat = caseChats.find(
              (caseChat) => caseChat.id === this.notification.parameters.chatId,
            );
            switch (chat.chatType) {
              case 'CONSORTIUM':
                route = `/${this.customerKey}/business-case/${this.notification.parameters.caseId}/chat/business-case`;
                break;
              case 'INTERNAL':
              case 'INTERNAL_GROUP':
              case 'INTERNAL_BILATERAL':
                route = `/${this.customerKey}/business-case/${this.notification.parameters.caseId}/chat/internal/${this.notification.parameters.chatId}`;
                break;
              case 'ON_TOPIC':
                route = `/${this.customerKey}/business-case/${this.notification.parameters.caseId}/chat/topic/${this.notification.parameters.chatId}`;
                break;
              case 'ONE_ON_ONE':
                route = `/${this.customerKey}/business-case/${this.notification.parameters.caseId}/chat/bilateral/${this.notification.parameters.chatId}`;
                break;
            }
          }
          this.router.navigate([route]);
        }),
        catchError((err) => {
          this.finToastService.show(
            Toast.error(
              err?.error?.code === this.customerIsNotAPartOfTheCaseErrorCode // if you aren't a part of the case, via checking for a specific error code (request usually provides 401)
                ? this.noPermissionsToEnterCaseErrorText // show a specific error toast
                : this.generalErrorText, // otherwise show the typical error toast
            ),
          );

          return of(StateLibNoopPageActions.noop());
        }),
      )
      .subscribe();
  }
}
