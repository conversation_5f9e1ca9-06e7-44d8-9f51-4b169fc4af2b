@if (notification) {
  <div
    class="card-container notifications-context"
    [class.tw-cursor-pointer]="
      notification.message !==
      notificationMessageType.USER_ASSIGNMENT_REASSIGNED
    "
    (click)="navigateToNotificationOrigin()"
    (mouseenter)="toggleIsCardHovered()"
    (mouseleave)="toggleIsCardHovered()"
  >
    <ui-avatar
      class="avatar notifications-context"
      [names]="notification.parameters?.name"
      [id]="notification.initiatorUserId"
      size="xl"
      backgroundColorType="darkBlue"
      [withIcon]="true"
    >
      <app-notification-icon
        class="notifications-context"
        [type]="notification.type"
      ></app-notification-icon>
    </ui-avatar>
    <div class="mid-section notifications-context">
      @if (boldUserName && notificationMessage) {
        <div class="message" [innerHTML]="notificationMessage"></div>
      }
      @switch (timePeriod) {
        @case ('minutes') {
          <div
            class="notification-time"
            i18n="@@notificationSystem.notification.elapsedTime.minutes"
          >
            {elapsedTime, plural,
              =0 {jetzt}
              =1 {vor 1 Minute}
              other {vor {{ elapsedTime }} Minuten}
            }
          </div>
        }
        @case ('hours') {
          <div
            class="notification-time"
            i18n="@@notificationSystem.notification.elapsedTime.hours"
          >
            {elapsedTime, plural,
              =1 {vor 1 Stunde}
              other {vor {{ elapsedTime }} Stunden}
            }
          </div>
        }
        @case ('days') {
          <div
            class="notification-time"
            i18n="@@notificationSystem.notification.elapsedTime.days"
          >
            {elapsedTime, plural,
              =1 {vor 1 Tag}
              other {vor {{ elapsedTime }} Tagen}
            }
          </div>
        }
        @case ('weeks') {
          <div
            class="notification-time"
            i18n="@@notificationSystem.notification.elapsedTime.weeks"
          >
            {elapsedTime, plural,
              =1 {vor 1 Woche}
              other {vor {{ elapsedTime }} Wochen}
            }
          </div>
        }
      }
    </div>
    @if (isCardHovered || isMenuOpened) {
      <div class="action-menu-wrapper notifications-context">
        <ui-actions-menu
          class="action-menu-wrapper"
          [hideArrow]="true"
          [optionsTemplate]="options"
          [menuOffset]="-60"
          (visibilityChange)="handleMenuContext($event)"
        ></ui-actions-menu>
      </div>
      <ng-template #options>
        <div class="notification-card-options">
          @if (!notification.read) {
            <ui-actions-menu-item
              class="notifications-context"
              iconName="check"
              iconSize="medium"
              (clicked)="toggleReadStatus.emit(notification)"
              i18n-label="@@notificationSystem.notification.actions.markAsRead"
              label="Als gelesen markieren"
            ></ui-actions-menu-item>
          }
          @if (notification.read) {
            <ui-actions-menu-item
              class="notifications-context"
              iconName="svgBell"
              (clicked)="toggleReadStatus.emit(notification)"
              i18n-label="
                @@notificationSystem.notification.actions.markAsUnread"
              label="Als ungelesen markieren"
            ></ui-actions-menu-item>
          }
          <ui-actions-menu-item
            class="notifications-context"
            iconName="svgDeleteV2"
            iconSize="medium"
            (clicked)="hideNotification.emit(notification)"
            i18n-label="@@notificationSystem.notification.actions.hide"
            label="Ausblenden"
          ></ui-actions-menu-item>
        </div>
      </ng-template>
    } @else {
      <div class="status-wrapper notifications-context">
        <ui-message-status [isRead]="notification.read"></ui-message-status>
      </div>
    }
  </div>
}
