@use 'styles/src/lib/common';
:host {
  display: block;

  .card-container {
    display: flex;
    border-radius: common.$border-radius;
    width: fit-content;
    height: fit-content;
    padding: 1rem;
    gap: 1rem;

    &:hover {
      background-color: theme('colors.color-surface-secondary');
    }
  }
  .avatar {
    align-self: center;
    padding-right: 1rem;
  }

  .mid-section {
    display: flex;
    flex-direction: column;
    align-self: flex-start;
    flex-grow: 1;
    gap: 0.5rem;
  }

  .message {
    @include common.heading7(500);
    margin-top: 0.5rem;
    align-self: start;
  }

  .notification-time {
    @include common.heading8(500);
    color: theme('colors.color-text-disabled');
  }

  .action-menu-wrapper {
    margin-top: 0.8rem;
    align-self: start;
    width: 1.5rem;
    padding-left: 0.5rem;
  }

  .status-wrapper {
    width: 2rem;
    align-self: start;
    margin: 1rem 0 0 0;
  }

  ::ng-deep {
    :hover {
      ui-actions-menu .menu-trigger .menu-icon {
        background-color: theme('colors.color-surface-primary');
        color: black;
        height: 2.6rem;
      }
    }

    ui-actions-menu .menu-trigger .menu-icon {
      border-radius: 50%;
    }
  }
}
.notification-card-options {
  width: max-content;
}
