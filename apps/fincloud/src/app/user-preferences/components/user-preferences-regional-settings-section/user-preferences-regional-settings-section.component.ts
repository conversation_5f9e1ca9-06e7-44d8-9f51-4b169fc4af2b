import { Component, Input } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { DateFormats, DateService } from '@fincloud/core/date';
import { LANGUAGE_OPTIONS, LanguageSelectOption } from '@fincloud/core/user';
import { UserRegion } from '@fincloud/types/enums';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'app-user-preferences-regional-settings-section',
  templateUrl: './user-preferences-regional-settings-section.component.html',
  styleUrl: './user-preferences-regional-settings-section.component.scss',
})
export class UserPreferencesRegionalSettingsSectionComponent {
  @Input() formGroup: FormGroup;
  @Input() selectedLanguage: LanguageSelectOption;

  protected readonly radioButtonSize = FinSize.S;
  protected readonly languageOptions = LANGUAGE_OPTIONS;
  protected readonly regionOptions = [
    {
      name: $localize`:@@userSettings.preferences.regionalSettings.region.germany:Deutschland`,
      value: UserRegion.GERMANY,
      dateFormat: this.dateService.formatDate(
        new Date(),
        DateFormats.DD_DOT_MM_DOT_YYYY,
      ),
      numberFormat: '123.456.789,99',
      currencyFormat: '100.000,50 €',
      percentageFormat: '88,355 %',
    },
    {
      name: $localize`:@@userSettings.preferences.regionalSettings.region.unitedKingdom:Vereinigtes Königreich`,
      value: UserRegion.UNITED_KINGDOM,
      dateFormat: this.dateService.formatDate(
        new Date(),
        DateFormats.DD_SLASH_MM_SLASH_YYYY,
      ),
      numberFormat: '123,456,789.99',
      currencyFormat: '€100,000.50',
      percentageFormat: '88.355 %',
    },
  ];

  constructor(private dateService: DateService) {}

  get regionFormControl(): FormControl {
    return this.formGroup.get('userRegion') as FormControl;
  }
}
