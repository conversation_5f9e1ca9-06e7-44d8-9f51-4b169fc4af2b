@if (uniqueInstanceId$ | async) {
  @if (
    {
      navigation: selectNavigationAndChatAreOpen$ | async,
      templatePortals: templatePortals$ | async,
    };
    as data
  ) {
    <div class="header-nav tw-block tw-fixed tw-w-full tw-z-[3]">
      <app-header-nav>
        <ng-container slot="create-business-case">
          @if (createBusinessCaseAllowed | async) {
            <button
              fin-button
              [size]="finSize.M"
              [appearance]="finButtonAppearance.PRIMARY"
              (click)="
                onCreateBusinessCase(customerKey, 'create-business-case')
              "
            >
              <span i18n="@@layout.mainLayout.createBusinessCaseTxt"
                >Fall erstellen</span
              >
            </button>
          }
        </ng-container>
        <ng-container slot="todos-management">
          @if (isTodosFeatureAvailable$ | async) {
            <app-todos-management-badge></app-todos-management-badge>
          }
        </ng-container>
        <ng-container slot="notifications">
          <app-notification-system-editor
            *ngxPermissionsOnly="[permissionHtml.PERM_0062]"
          ></app-notification-system-editor>
        </ng-container>
        <ng-container slot="user">
          @if (user$ | async; as user) {
            <button fin-button-icon [appearance]="finButtonAppearance.STEALTH">
              <fin-avatar-default
                [firstName]="user.firstName"
                [lastName]="user.lastName"
                [size]="finSize.M"
                appAnchorPathBuilder
                (click)="toggleUserMenu()"
              ></fin-avatar-default>
            </button>
          }
          @if (isUserMenuOpened) {
            <ui-user-menu
              @fade
              class="tw-block tw-absolute tw-top-24 tw-right-8 tw-w-[22rem] tw-h-[12rem] tw-rounded-[1.6rem] tw-bg-color-surface-primary"
              [menuOpened]="isUserMenuOpened"
              (closeMenu)="toggleUserMenu()"
            ></ui-user-menu>
          }
        </ng-container>
      </app-header-nav>
    </div>

    <div
      [ngClass]="{
        expanded: data.navigation.platformNavigationIsOpen,
        collapsed: !data.navigation.platformNavigationIsOpen,
      }"
    >
      <fin-side-panel
        #sidePanel
        [open]="false"
        [mode]="finSidePanelMod.OVER"
        [position]="finSidePanelPosition.END"
        [disableClose]="false"
        [hasBackdrop]="true"
        [transparentBackdrop]="true"
        [elevation]="true"
        class="side-panel"
        (isOpen)="onLayoutVisibilityChange($event)"
      >
        <ng-template finSidePanelSlide>
          <div class="content">
            @if (
              (rightSideOverlayPanelOpened$ | async) &&
              data.templatePortals[
                sidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL
              ]
            ) {
              <ng-template
                [cdkPortalOutlet]="
                  data.templatePortals[
                    sidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL
                  ]
                "
              ></ng-template>
            }
          </div>
        </ng-template>
        <ng-template finSidePanelContent>
          <div class="sidenav-wrapper tw-h-dvh">
            <fin-side-panel
              [open]="data.navigation.platformNavigationIsOpen"
              [partiallyOpenSize]="70"
              [fixedInViewport]="false"
            >
              <ng-template finSidePanelSlide>
                <div
                  class="tw-flex tw-flex-col tw-justify-between tw-h-full tw-border-r tw-border-color-border-default-primary"
                >
                  <fin-scrollbar>
                    <app-side-nav-menu-items
                      class="tw-h-full"
                      [collapsed]="!data.navigation.platformNavigationIsOpen"
                    >
                    </app-side-nav-menu-items>
                  </fin-scrollbar>
                  <div
                    class="sidebar-footer tw-flex tw-px-[1.2rem] tw-pt-[1.2rem] tw-pb-[3.2rem]"
                    [ngClass]="{
                      'tw-justify-start tw-pl-[1.8rem]':
                        data.navigation.platformNavigationIsOpen,
                      'tw-justify-center':
                        !data.navigation.platformNavigationIsOpen,
                    }"
                  >
                    <button
                      fin-button-icon
                      [appearance]="sidebarToggleButtonAppearance"
                      (click)="toggleNavigations(data.navigation)"
                    >
                      <fin-icon
                        [name]="
                          data.navigation.platformNavigationIsOpen
                            ? 'keyboard_arrow_left'
                            : 'keyboard_arrow_right'
                        "
                      ></fin-icon>
                    </button>
                  </div>
                </div>
              </ng-template>

              <ng-template finSidePanelContent>
                <div class="page-container tw-flex tw-flex-col">
                  <fin-scrollbar class="tw-h-full" #mainScrollContainer>
                    <div class="scroll-content tw-mb-[3.5rem]">
                      <main class="main tw-flex-grow tw-px-[3.2rem]" #main>
                        <div class="container-fluid tw-p-0">
                          <router-outlet></router-outlet>
                        </div>
                      </main>
                      <ui-footer></ui-footer>
                    </div>
                  </fin-scrollbar>

                  <!-- We keep this because of scrollbar limitation -->
                  @if (data.templatePortals?.bottom) {
                    <div class="section bottom">
                      <ng-template
                        [cdkPortalOutlet]="data.templatePortals?.bottom"
                      ></ng-template>
                    </div>
                  }
                </div>
              </ng-template>
            </fin-side-panel>
          </div>
        </ng-template>
      </fin-side-panel>
    </div>
  }
}

<!-- <app-feedback></app-feedback> -->
