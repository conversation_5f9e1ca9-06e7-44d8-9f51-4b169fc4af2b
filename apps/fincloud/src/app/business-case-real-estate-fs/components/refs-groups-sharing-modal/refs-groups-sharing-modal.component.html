<fin-modal-header>
  <span i18n="@@dataRoom.accessModal.title">Zugriffsrechte verwalten</span>
  <button fin-button-action fin-modal-close [size]="finSize.L">
    <fin-icon name="close"></fin-icon>
  </button>
</fin-modal-header>
<fin-modal-slots-container>
  <fin-modal-slot [size]="finSize.L">
    <form class="tw-flex tw-flex-col tw-gap-6" [formGroup]="groupsSharingForm">
      <fin-warning-message
        class="tw-w-full"
        i18n-label="@@sharingModal.information"
        label="Steuern Sie die Sichtbarkeit, fordern Sie Informationen an und verfolgen Sie Statusaktualisierungen für ausgewählte Bereiche innerhalb Ihrer Finanzierungsstruktur."
        [appearance]="finWarningMessageAppearance.INFORMATIVE"
      ></fin-warning-message>

      <fin-dropdown
        autocomplete
        labelPropertyName="name"
        valuePropertyName="userKey"
        i18n-label="@@fs.selectParticipant"
        label="Organisation auswählen"
        class="tw-mx-[2.4rem]"
        formControlName="selectedParticipantKey"
        [size]="finSize.L"
        [options]="
          participants$
            | async
            | filterByTermWithCollection
              : 'userKey'
              : groupsSharingForm.controls.selectedParticipantKey.value
        "
        (selectionChange)="onSelectionChange($event)"
        (autoCompleteInputChange)="onAutoCompleteChange($event)"
      ></fin-dropdown>
      <div class="tw-h-[26.5rem]">
        @if (
          sharedGroupsWithParticipant$ | async;
          as sharedGroupsWithParticipant
        ) {
          <app-select-group-tree
            #selectGroupTree
            [sharedGroups]="sharedGroupsWithParticipant"
          ></app-select-group-tree>
        }
      </div>
    </form>
  </fin-modal-slot>
</fin-modal-slots-container>
<fin-modal-footer>
  <button
    fin-button
    fin-modal-close
    i18n="@@button.label.cancel"
    [size]="finSize.L"
    [appearance]="finButtonAppearance.SECONDARY"
    (click)="onCancel()"
  >
    Abbrechen
  </button>

  <button
    fin-button
    i18n="@@button.label.save"
    [size]="finSize.L"
    [disabled]="groupsSharingForm.invalid"
    [showLoader]="loading$ | async"
    (click)="onSubmit()"
  >
    Speichern
  </button>
</fin-modal-footer>
