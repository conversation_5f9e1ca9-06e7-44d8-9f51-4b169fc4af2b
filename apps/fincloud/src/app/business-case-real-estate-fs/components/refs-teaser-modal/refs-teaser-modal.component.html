<fin-modal-header>
  <span i18n="@@dashboard.businessCase.pdfExport">PDF-Export</span>
  <button fin-button-action fin-modal-close [size]="finSize.L">
    <fin-icon name="close"></fin-icon>
  </button>
</fin-modal-header>

<fin-modal-slots-container>
  <fin-modal-slot [size]="finSize.L">
    <form [formGroup]="formGroup" class="tw-flex tw-flex-col tw-gap-6">
      <fin-warning-message
        class="tw-w-full"
        i18n-label="@@teaserConfigModal.message"
        label="Wählen Sie die Organisation und die entsprechenden Gruppen für den Export aus. Beachten Sie, dass Gruppen ohne Informationen deaktiviert sind und nicht exportiert werden können."
        [appearance]="finWarningMessageAppearance.INFORMATIVE"
      ></fin-warning-message>

      @if (participants$ | async; as participants) {
        @if (userCustomerKey$ | async; as userCustomerKey) {
          <fin-dropdown
            class="tw-mx-[2.4rem]"
            formControlName="selectedParticipantKey"
            i18n-label="@@fs.selectParticipant"
            label="Teilnehmer auswählen"
            autocomplete
            [size]="finSize.L"
            [readonly]="participants.length === 1"
            [options]="
              participants
                | filterByTermWithCollection
                  : 'value'
                  : formGroup.controls.selectedParticipantKey.value
            "
            (selectionChange)="onSelectionChange($event, userCustomerKey)"
            (autoCompleteInputChange)="onAutoCompleteChange($event)"
          ></fin-dropdown>
        }
      }

      @if (finStructureTeaserExport$ | async; as finStructure) {
        <app-select-group-tree
          class="tw-h-[26.5rem]"
          #selectGroupTree
          [disabledGroups]="finStructure.disabledGroups"
          [sharedGroups]="{
            staticGroups: finStructure.staticGroups,
            sharedEntities: finStructure.idsForExport,
          }"
        ></app-select-group-tree>
      }
    </form>
  </fin-modal-slot>
</fin-modal-slots-container>

<fin-modal-footer>
  <button
    fin-button
    fin-modal-close
    i18n="@@button.label.cancel"
    [size]="finSize.L"
    [appearance]="finButtonAppearance.SECONDARY"
  >
    Abbrechen
  </button>
  @if (finStructureTeaserExport$ | async; as finStructure) {
    <button
      fin-button
      fin-modal-close
      i18n="@@contracts.contractsDetailsModal.downloadButton"
      [size]="finSize.L"
      [disabled]="
        formGroup.invalid ||
        !selectGroupTreeComponent?.collectedGroupIds?.length
      "
      (click)="
        downloadTeaser(finStructure.staticGroups, finStructure.disabledGroups)
      "
    >
      Herunterladen
    </button>
  }
</fin-modal-footer>
