import {
  Component,
  DestroyRef,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
} from '@angular/core';
import {
  Information,
  ParticipantCasePermissionSetEntity,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseTeaserGeneratorService,
  GroupInfo,
  TableFieldValue,
  TeaserConfiguration,
  UserInformation,
} from '@fincloud/swagger-generator/document-generator';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { Store } from '@ngrx/store';
import { isEmpty, isObject, keyBy } from 'lodash-es';
import { filter, forkJoin, map, switchMap, take, tap } from 'rxjs';

import { CurrencyPipe, DatePipe, DecimalPipe } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  CellData,
  DataGridConfig,
  EMPTY_CELL_DATA,
} from '@fincloud/components/data-grid';
import {
  BusinessCaseModelService,
  DataRoomGroupsFieldsService,
  FieldType,
  GroupTemplateFields,
} from '@fincloud/core/business-case';
import { DateFormats, DateService } from '@fincloud/core/date';
import { FileService } from '@fincloud/core/files';
import { BusinessCaseLocation, FieldTypeEnum } from '@fincloud/core/formly';
import { ModalService } from '@fincloud/core/modal';
import { InitialsPipe, RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import { AddressHelperService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import {
  selectBusinessCaseParticipantsPermissions,
  selectCaseFieldsAccess,
  selectCustomerNamesByKey,
  selectUsersById,
} from '@fincloud/state/business-case';
import { selectCustomer } from '@fincloud/state/customer';
import {
  Customer,
  CustomerManagementControllerService,
  CustomerSharedInformationResponse,
  User,
} from '@fincloud/swagger-generator/authorization-server';
import { CaseFieldAccess } from '@fincloud/swagger-generator/portal';
import {
  BusinessCasePermission,
  FinancingStructureType,
  Locale,
} from '@fincloud/types/enums';
import { AppState, Dictionary } from '@fincloud/types/models';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { TABLE_NUMERIC_FIELD_TYPES } from '@fincloud/utils';
import { BusinessCaseTeaserDtoExtender } from '../../models/business-case-teaser-dto-extender';
import { TeaserExportPartnerInfoExtender } from '../../models/teaser-export-partner-info-extender';
import { TeaserConfigurationModalComponent } from '../teaser-configuration-modal/teaser-configuration-modal.component';

@Component({
  selector: 'app-teaser-download',
  templateUrl: './teaser-download.component.html',
  styleUrls: ['./teaser-download.component.scss'],
})
export class TeaserDownloadComponent implements OnInit {
  @Input() businessCase: ExchangeBusinessCase;

  private groupsFields: GroupTemplateFields[];
  private participantPermissions: Record<
    string,
    ParticipantCasePermissionSetEntity
  >;

  caseFieldsCompanyAccessDict: Dictionary<CaseFieldAccess>;

  constructor(
    private destroyRef: DestroyRef,
    @Inject(LOCALE_ID) public locale: Locale,
    private fileService: FileService,
    private store: Store<AppState>,
    private modalService: ModalService,
    private businessCaseModelService: BusinessCaseModelService,
    private datePipe: DatePipe,
    private initialsPipe: InitialsPipe,
    private customerManagementControllerService: CustomerManagementControllerService,
    private addressHelperService: AddressHelperService,
    private businessCaseTeaserGeneratorService: BusinessCaseTeaserGeneratorService,
    private dateService: DateService,
    private finToastService: FinToastService,
    private businessCaseGroupsFieldsService: DataRoomGroupsFieldsService,
    private currencyPipe: CurrencyPipe,
    private decimalPipe: DecimalPipe,
    private removeTrailingZerosPipe: RemoveTrailingZerosPipe,
    private finModalService: FinModalService,
  ) {}

  ngOnInit() {
    this.store
      .select(selectBusinessCaseParticipantsPermissions)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((permissions) => {
        this.participantPermissions = permissions;
      });

    this.store
      .select(selectCaseFieldsAccess)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(
        (caseFieldsAccess) =>
          (this.caseFieldsCompanyAccessDict = keyBy(
            caseFieldsAccess.filter((cfa) => cfa.requestType === 'COMPANY'),
            'fieldKey',
          )),
      );
  }

  openTeaserConfigurationModal() {
    const information = keyBy(
      Object.values(this.businessCase?.information || {}),
      'key',
    );

    this.groupsFields =
      this.businessCaseGroupsFieldsService.getGroupsTemplateFields(
        this.businessCase.businessCaseTemplate.template.fields,
        this.businessCase.businessCaseTemplate.template.groupsOrdered,
        information,
        null,
      );

    this.finModalService
      .open<TeaserConfiguration, TeaserConfigurationModalComponent>(
        TeaserConfigurationModalComponent,
        {
          data: { groups: this.groupsFields },
          disableClose: true,
          size: FinSize.M,
        },
      )
      .afterClosed()
      .pipe(
        filter(Boolean),
        map((teaserData) => {
          const isBusinessCaseRealEstate =
            this.businessCase?.structuredFinancingConfiguration
              .financingStructureType === FinancingStructureType.REAL_ESTATE;

          if (isBusinessCaseRealEstate) {
            teaserData.breakdown = false;
          }

          teaserData.locale = this.locale;
          return teaserData;
        }),
        switchMap((teaserData) =>
          forkJoin([
            this.customerManagementControllerService.getCustomerByKeyPublic({
              customerKey: this.businessCase.leadCustomerKey,
            }),
            this.store.select(selectUsersById).pipe(take(1)),
            this.store.select(selectCustomerNamesByKey).pipe(take(1)),
            this.store.select(selectCustomer).pipe(filter(Boolean), take(1)),
          ]).pipe(
            tap(() => {
              this.finToastService.show(
                Toast.info(
                  $localize`:@@pdfExport.toast.info:PDF-Export gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.`,
                ),
              );
            }),
            map(([customerInfo, usersInfo, customersInfo, currentCustomer]) =>
              this.getTeaserInfo(
                teaserData,
                this.businessCase,
                customerInfo,
                usersInfo,
                customersInfo,
                currentCustomer,
              ),
            ),
          ),
        ),
        switchMap((teaserInfo) =>
          this.businessCaseTeaserGeneratorService
            .businessCaseTeaserControllerGenerateBusinessCaseTeaser({
              businessCaseId: this.businessCase.id,
              body: teaserInfo,
            })
            .pipe(
              tap({
                next: (pdfBinary) => {
                  this.fileService.downloadBinary(
                    pdfBinary as Blob,
                    this.getFileName(),
                  );
                },
                error: () => this.finToastService.show(Toast.error()),
              }),
            ),
        ),
      )
      .subscribe();
  }

  private getFileName() {
    const exportDate = this.dateService.formatDate(
      this.dateService.todaysDate(),
      DateFormats.DD_DOT_MM_DOT_YYYY_WITH_HOURS,
    );

    return `Export DR ${this.businessCase.autoGeneratedBusinessCaseName} - ${exportDate}.pdf`;
  }

  private getTeaserInfo(
    teaserConfig: TeaserConfiguration,
    businessCase: ExchangeBusinessCase,
    customerInfo: Customer,
    usersInfo: Dictionary<User>,
    customersInfo: Dictionary<Customer>,
    currentCustomer: Customer,
  ) {
    const teaserInfo = {} as BusinessCaseTeaserDtoExtender;

    teaserInfo.configuration = teaserConfig;

    teaserInfo.companyName = businessCase.company?.companyInfo?.legalName;
    teaserInfo.createdDate = this.datePipe.transform(businessCase.creationDate);
    teaserInfo.groups = this.getGroupsFields(
      teaserConfig,
      businessCase,
    ) as GroupInfo[];

    if (teaserConfig.businessCaseKey) {
      teaserInfo.key = businessCase.autoGeneratedBusinessCaseName;
    }

    if (teaserConfig.customerInfo) {
      teaserInfo.customerInformation = this.getCustomerInfo(
        businessCase,
        customerInfo,
        usersInfo,
        customersInfo,
      );
    }

    teaserInfo.businessCaseBreakdown = this.getTeaserBreakdown(
      businessCase,
      customersInfo,
    );

    if (!teaserConfig.breakdown && !teaserConfig.includeFinancingAmountsChart) {
      teaserInfo.businessCaseBreakdown.partners =
        teaserInfo.businessCaseBreakdown.partners.filter(
          (x: TeaserExportPartnerInfoExtender) => {
            const currentCustomerPermissions =
              this.participantPermissions[currentCustomer.key].permissions;
            const indexCustomerPermissions =
              this.participantPermissions[x.customerKey].permissions;
            return !currentCustomerPermissions?.includes(
              BusinessCasePermission.BCP_00063,
            ) ||
              !currentCustomerPermissions?.includes(
                BusinessCasePermission.BCP_00140,
              )
              ? x.customerKey === currentCustomer.key
              : indexCustomerPermissions?.includes(
                  BusinessCasePermission.BCP_00063,
                ) ||
                  indexCustomerPermissions?.includes(
                    BusinessCasePermission.BCP_00140,
                  ) ||
                  x.customerKey === currentCustomer.key;
          },
        );

      teaserConfig.breakdown = true;
    }

    return teaserInfo;
  }

  private getCustomerInfo(
    businessCase: ExchangeBusinessCase,
    customerInfo: Customer,
    usersInfo: Dictionary<User>,
    customersInfo: Dictionary<CustomerSharedInformationResponse>,
  ) {
    const leadCustomer = this.businessCase.participants.find((p) => p.lead);
    const leadCustomerInfo = customersInfo[leadCustomer.customerKey];
    return {
      totalAssets: this.removeTrailingZerosPipe.transform(
        this.currencyPipe.transform(leadCustomerInfo?.baseInfo?.totalAssets),
      ),
      logoUrl: leadCustomerInfo?.imageUrl ?? '',
      customerName: leadCustomerInfo?.name,
      customerWebsite: leadCustomerInfo?.website,
      contactPeople: this.getContactPeopleInfo(businessCase, usersInfo),
      address: this.addressHelperService.asCustomerAddressString(
        leadCustomerInfo?.address,
      ),
      employeeCount: leadCustomerInfo?.baseInfo?.numEmployees,
    };
  }

  private getTeaserBreakdown(
    businessCase: ExchangeBusinessCase,
    customersInfo: Dictionary<Customer>,
  ) {
    const businessCaseBreakdown =
      this.businessCaseModelService.toBusinessCaseBreakdownViewModel(
        businessCase,
        Object.values(customersInfo),
        this.participantPermissions,
      );

    const outstandingAmount = Math.abs(
      +businessCaseBreakdown.outstandingAmount,
    );
    const investedAmount = +businessCaseBreakdown.investedAmount;
    const financingVolume = businessCaseBreakdown.financingVolume;

    return {
      leadPartner: {
        ...businessCaseBreakdown.leadPartner,
        percent: this.getPercentLabel(
          financingVolume,
          businessCaseBreakdown.leadPartner?.totalParticipationAmount ?? 0,
        ),
        totalParticipationAmount: this.formatValue(
          businessCaseBreakdown.leadPartner?.totalParticipationAmount?.toString() ??
            '0',
          'MONETARY',
        ),
      },
      partners: businessCaseBreakdown.partners.map((partner) => {
        return {
          ...partner,
          percent: this.getPercentLabel(
            financingVolume,
            partner?.totalParticipationAmount ?? 0,
          ),
          totalParticipationAmount: this.formatValue(
            partner?.totalParticipationAmount?.toString() ?? '0',
            'MONETARY',
          ),
        };
      }),
      investedPercentLabel: this.getPercentLabel(
        financingVolume,
        investedAmount,
      ),
      investedPercentValue: this.getPercentValue(
        financingVolume,
        investedAmount,
        false,
      ),
      outstandingPercentLabel: this.getPercentLabel(
        financingVolume,
        outstandingAmount,
      ),
      outstandingPercentValue: this.getPercentValue(
        financingVolume,
        outstandingAmount,
        false,
      ),
      outstandingAmount: this.formatValue(
        outstandingAmount.toString(),
        'MONETARY',
      ),
      investedAmount: this.formatValue(investedAmount.toString(), 'MONETARY'),
      financingVolume,
    };
  }

  private getGroupsFields(
    teaserConfig: TeaserConfiguration,
    businessCase: ExchangeBusinessCase,
  ) {
    const selectedGroupsKeys = teaserConfig.groups
      .filter((g) => g.include)
      .map((g) => g.key);
    const groupsToInclude = this.groupsFields.filter((g) =>
      selectedGroupsKeys.includes(g.key),
    );

    const result = groupsToInclude.map((group) => {
      return {
        name: group.value,
        fields: group.templateFields
          .filter(
            (tf) =>
              this.hasFieldValue(tf.information?.value as string) &&
              (!teaserConfig.companyInfo
                ? tf.information.key !== 'companyId'
                : true),
          )
          .map((field) => {
            const fieldInfo = field.information;
            return {
              fieldType: this.getFieldType(fieldInfo as Information),
              label: fieldInfo.field.label,
              value: this.formatValue(
                fieldInfo.value as string,
                fieldInfo.field.fieldType,
                fieldInfo.key,
              ),
              tableValue:
                fieldInfo.field.fieldType === 'TABLE'
                  ? this.formatTable(
                      fieldInfo.value as unknown as DataGridConfig,
                    )
                  : undefined,
              additionalInfo: this.getAdditionalInfo(
                fieldInfo.key,
                businessCase,
              ),
            };
          }),
      };
    });

    return result;
  }

  private getFieldType(fieldInfo: Information) {
    // TODO: pretend that commision fee is primitive field type until proper supprot for it is added in doc-gen srv
    if (fieldInfo.key === 'commissionFee') {
      return FieldTypeEnum.SHORT_TEXT;
    }
    return fieldInfo.field.fieldType as FieldType;
  }

  private hasFieldValue(value: string) {
    if (
      !value ||
      ((isObject(value) || Array.isArray(value)) && isEmpty(value))
    ) {
      return false;
    }

    return true;
  }

  private getPercentLabel(
    totalVolume: number,
    participationVolume: number,
  ): string {
    if (totalVolume === 0) {
      return '0 %';
    }
    const percent = (participationVolume / totalVolume) * 100;
    const rounded = percent.toFixed(3);
    return this.removeTrailingZerosPipe.transform(
      this.decimalPipe.transform(rounded) + '%',
    );
  }

  private getPercentValue(
    totalVolume: number,
    participationVolume: number,
    roundDecimal = true,
  ): number {
    if (totalVolume === 0) {
      return 0;
    }
    const percent = participationVolume / totalVolume;
    const rounded = roundDecimal
      ? Math.round(percent * 10) / 10
      : Math.round(percent);
    return rounded;
  }

  private formatValue(
    value: string,
    subType: string | undefined,
    key?: string,
  ) {
    // This check is necessary because the table fields do not have validations.
    if (
      TABLE_NUMERIC_FIELD_TYPES.includes(subType?.toUpperCase()) &&
      isNaN(Number(value))
    ) {
      return value;
    }
    if (subType?.toUpperCase() === 'PERCENT') {
      return this.removeTrailingZerosPipe.transform(
        this.decimalPipe.transform(value) + '%',
      );
    }
    if (subType === 'MONETARY' || subType === 'currency') {
      return this.removeTrailingZerosPipe.transform(
        this.currencyPipe.transform(value),
      );
    }
    if (subType?.toUpperCase() === 'DECIMAL') {
      return this.removeTrailingZerosPipe.transform(
        this.decimalPipe.transform(value),
      );
    }
    if (subType?.toUpperCase() === 'INTEGER') {
      return this.decimalPipe.transform(value);
    }
    if (subType === 'MONTHS') {
      return this.businessCaseModelService.formatMonths(value);
    }
    if (subType === 'DATE') {
      return this.datePipe.transform(value) as string;
    }
    if (subType === 'BOOLEAN') {
      return Boolean(value) === true ? 'Ja' : 'Nein';
    }
    if (subType === 'LOCATION') {
      return this.addressHelperService.asFormlyLocationString(
        value as unknown as BusinessCaseLocation,
      );
    }
    if (subType === 'COMPOSITE' && key === 'commissionFee') {
      const { inputValue, dropdownOption } = value as unknown as {
        inputValue: number;
        dropdownOption: { label: string };
      };

      let unit = '';
      if (
        dropdownOption.label === $localize`:@@composite.field.numeric:Numerisch`
      ) {
        unit = '€';
      } else if (
        dropdownOption.label ===
        $localize`:@@composite.field.percent:Prozentsatz`
      ) {
        unit = '%';
      }

      if (!inputValue) {
        return '';
      }

      return this.businessCaseModelService.formatCommissionFee(
        inputValue,
        unit,
      );
    }

    return value;
  }

  private formatTable(tableFieldValue: DataGridConfig): TableFieldValue {
    return {
      headers: tableFieldValue.columnDefs.map((col) => col?.headerName ?? ''),
      rows: tableFieldValue?.rowData.map((row) =>
        tableFieldValue.columnDefs.map(
          (col) =>
            this.formatValue(
              (row[col.field] as CellData)?.value,
              (row[col.field] as CellData)?.formatting.dataType,
            ) ?? EMPTY_CELL_DATA?.value,
        ),
      ),
    };
  }

  private getAdditionalInfo(
    informationKey: string,
    businessCase: ExchangeBusinessCase,
  ) {
    if (informationKey === 'companyId' && businessCase.company) {
      const company = businessCase.company;
      return {
        name: company.companyInfo.legalName,
        industry: company.companyInfo.industryInfo,
        address: company.address,
      };
    }
    return null;
  }

  private getContactPeopleInfo(
    businessCase: ExchangeBusinessCase,
    usersInfo: Dictionary<User>,
  ): UserInformation[] {
    return businessCase.contactPersonIds.map((userId) => {
      const userInfo = usersInfo[userId];
      const userFullName = `${userInfo.firstName} ${userInfo.lastName}`;
      return {
        name: userFullName,
        email: userInfo.username,
        academicTitle: userInfo.attributes.academicTitle,
        department: userInfo.attributes.department,
        landlineNumber: userInfo.attributes.landlineNumber,
        mobileNumber: userInfo.attributes.mobileNumber,
        position: userInfo.attributes.position,
        salutation: userInfo.attributes.salutation,
        initials: this.initialsPipe.transform(userFullName) as string,
      };
    });
  }
}
