import { Cdk<PERSON><PERSON>, CdkDropList } from '@angular/cdk/drag-drop';
import { AfterViewInit, Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { DRAGGABLE_TEMPLATE_FIELDS } from '@fincloud/components/data-room';
import {
  DataRoomGroupsFieldsService,
  DraggableTemplateField,
  GroupTemplateFields,
  TemplateFieldViewModel,
} from '@fincloud/core/business-case';
import {
  FOLDER_CONTENT_BASE_SERVICE_TOKEN,
  ManageFolderModalComponent,
} from '@fincloud/neoshare/folder-structure';
import {
  StateLibFolderStructureFolderPageActions,
  folderStructureFeature,
} from '@fincloud/state/folder-structure';

import { FieldTypeEnum } from '@fincloud/core/formly';
import { HotkeyService } from '@fincloud/core/hotkeys';
import { ScrollCommunicationService } from '@fincloud/core/scroll';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { BusinessCaseModalService } from '@fincloud/neoshare/business-case-fields';
import {
  BusinessCaseDataRoomHelperService,
  CaseDataRoomCustomHandlerService,
  DataRoomBaseFunctionalityProvider,
} from '@fincloud/neoshare/data-room';
import {
  selectAccessRights,
  selectHasDataRoomWriteAccess,
} from '@fincloud/state/access';
import {
  StateLibBusinessCaseDataRoomPageActions,
  StateLibBusinessCasePageActions,
  selectBusinessCase,
  selectBusinessCaseContentHeightDataRoom,
  selectBusinessCaseSidePanelHeightDefault,
  selectBusinessCaseWrapperHeightDataRoom,
  selectCaseFieldsAccess,
  selectDataRoomEditToggle,
  selectFilteredCaseParticipants,
  selectGetDraggerDataRoomItemType,
  selectHasAnyBusinessCasePermission,
  selectHasBusinessCasePermission,
  selectHighlighted,
  selectIsCompanyDataRoomTab,
  selectIsEmployeeOfLeadCustomer,
  selectMirroredFieldKeys,
} from '@fincloud/state/business-case';
import { selectExistingChats } from '@fincloud/state/chat';
import { selectDataRoomActionsButtonVisibility } from '@fincloud/state/data-room';
import { StateLibDocumentInboxPageActions } from '@fincloud/state/document-inbox';
import { selectIsNeoGptVisible } from '@fincloud/state/neogpt-chat';
import {
  StateLibSideNavigationsPageActions,
  sideNavigationsFeature,
} from '@fincloud/state/side-navigations';
import {
  StateLibUserPageActions,
  selectUserCustomerKey,
  selectUserDracconCredentials,
  selectUserId,
  selectUserNextfolderCredentials,
} from '@fincloud/state/user';
import { FieldDto } from '@fincloud/swagger-generator/business-case-manager';
import { Chat } from '@fincloud/swagger-generator/communication';
import { Information } from '@fincloud/swagger-generator/demo';
import {
  DocumentSyncControllerService,
  DocumentThirdPartySyncLink,
  NextFolderDocument,
  NextFolderManagementControllerService,
} from '@fincloud/swagger-generator/document';
import {
  BusinessCaseGroup,
  ExchangeBusinessCase,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import {
  BusinessCasePermission,
  ChatType,
  DataRoomDraggedItemType,
  FolderStructureContext,
} from '@fincloud/types/enums';
import {
  AccessRights,
  AppState,
  Dictionary,
  ValueChangeModel,
} from '@fincloud/types/models';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { SCROLL_TO_DELAY } from '@fincloud/utils';
import { Store } from '@ngrx/store';

import { DocumentInteractionsService } from '@fincloud/core/services';
import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';
import { FinEmptyStateType } from '@fincloud/ui/empty-state';
import { FinModalService } from '@fincloud/ui/modal';
import {
  DATA_ROOM_MIN_SEARCH_LENGTH,
  NEXT_FOLDER_DESTINATION,
  checkCustomerExplicitGroupVisibility,
} from '@fincloud/utils';
import { cloneDeep, isEqual, keyBy } from 'lodash-es';
import { Observable, asapScheduler, combineLatest, merge, of } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  scan,
  shareReplay,
  startWith,
  subscribeOn,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import {
  selectCanShowDataRoomEditToggle,
  selectDataRoomTemplateFieldData,
} from '../../+state';
import { BusinessCaseDataRoomPageActions } from '../../+state/actions';
import { DataRoomSidebarTabs } from '../../enums/data-room-sidebar-tabs';
import { BusinessCaseDataRoomFolderContentBaseService } from '../../services/business-case-data-room-folder-content-base.service';

@Component({
  selector: 'app-business-case-data-room',
  styleUrl: './business-case-data-room.component.scss',
  templateUrl: './business-case-data-room.component.html',
  providers: [
    {
      provide: FOLDER_CONTENT_BASE_SERVICE_TOKEN,
      useClass: BusinessCaseDataRoomFolderContentBaseService,
    },
  ],
})
export class BusinessCaseDataRoomComponent
  extends DataRoomBaseFunctionalityProvider
  implements OnInit, AfterViewInit
{
  readonly emptyGeneralGroupText = $localize`:@@businessCase.dataRoom.noFieldsInGeneralGroupText:Fügen Sie Informationen per Drag & Drop aus der linken Spalte hinzu`;
  readonly dataRoomDraggedItemType = DataRoomDraggedItemType;
  readonly finEmptyStateType = FinEmptyStateType;
  readonly hoverDelay = 200;

  businessCase: ExchangeBusinessCase;
  userCustomerKey: string;
  userId: string;
  existingTopicChats: Dictionary<Chat>;
  existingArchivedTopicChats: Dictionary<Chat>;
  searchFilterFocused$ = of(true);
  searchControl = new FormControl('');
  draggedItemType: DataRoomDraggedItemType = DataRoomDraggedItemType.NONE;

  isNeoGptVisible$ = this.store
    .select(selectIsNeoGptVisible)
    .pipe(distinctUntilChanged((prev, curr) => isEqual(prev, curr)));

  caseParticipants$ = this.store
    .select(selectFilteredCaseParticipants)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  isCompanyDataRoomTab$ = this.store
    .select(selectIsCompanyDataRoomTab)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  userCustomerKey$ = this.store
    .select(selectUserCustomerKey)
    .pipe(
      distinctUntilChanged(),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

  isActionButtonVisible$ = this.store
    .select(selectDataRoomActionsButtonVisibility)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  hasPermissionForGroupAccess$ = this.store
    .select(
      selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00017]),
    )
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  hasPermissionForDocumentExports$ = this.store
    .select(
      selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00014]),
    )
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  canSeeInbox$ = this.store
    .select(selectHasBusinessCasePermission(BusinessCasePermission.BCP_00022))
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  hasPermissionsForTeaserExport$ = this.store
    .select(
      selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00015]),
    )
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  highlighted$ = this.store
    .select(selectHighlighted)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  canShowEditToggle$ = this.store.select(selectCanShowDataRoomEditToggle);

  accessRights: AccessRights;
  readonly folderStructureContext = FolderStructureContext;
  readonly finButtonShape = FinButtonShape;
  readonly dataRoomSidebarTabs = DataRoomSidebarTabs;
  readonly groupsText = $localize`:@@businessCase.participantAccess.rights.editAccess.text.groupVisibility:Gruppen`;
  readonly addDataText = $localize`:@@dataRoom.addDataText:Info hinzufügen`;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly dataRoomSidebarOptions = [
    { label: this.groupsText, value: this.dataRoomSidebarTabs.GROUPS },
    { label: this.addDataText, value: this.dataRoomSidebarTabs.ADD_DATA },
  ];
  activeDataRoomSidebarTabControl = new FormControl({
    value: this.dataRoomSidebarTabs.GROUPS,
    disabled: true,
  });

  navigationsAreOpen$ = this.store.select(
    sideNavigationsFeature.selectNavigationAndChatAreOpen,
  );

  isEmployeeOfLeadCustomer$ = this.store.select(selectIsEmployeeOfLeadCustomer);

  hasDataRoomWriteAccess$ = this.store
    .select(selectHasDataRoomWriteAccess)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  mirroredFieldKeys$ = this.store.select(selectMirroredFieldKeys);

  pointerEventsOnDocumentSection$ =
    this.documentInteractionsService.canHightlightSectionOnDrag$.pipe(
      debounceTime(this.hoverDelay),
    );

  syncLinkForBank$ = merge(
    this.store.select(selectUserNextfolderCredentials),
    this.store.select(selectUserDracconCredentials),
  ).pipe(
    filter((credentials) => !!credentials && !credentials.credentialsExpired),
    switchMap(() =>
      this.documentSyncControllerService.getSyncLinkForBank({
        customerKey: this.userCustomerKey,
      }),
    ),
    take(1),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  isServiceSynchronizedWithNextfolder$ = this.isServiceSynchronizedWith(
    'NEXTFOLDER',
  ).pipe(shareReplay({ bufferSize: 1, refCount: true }));
  isServiceSynchronizedWithDracoon$ = this.isServiceSynchronizedWith(
    'DRACOON',
  ).pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  currentFolderFieldKeysPerGroup$ = this.store.select(
    folderStructureFeature.selectCurrentFolderFieldKeysPerGroup,
  );

  dataRoomTemplateFieldData$ = this.store
    .select(selectDataRoomTemplateFieldData)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  getCurrentFolderDocumentsForGroup(
    currentFolderFieldKeysPerGroup: {
      [groupKey: string]: string[];
    },
    group: GroupTemplateFields,
  ): TemplateFieldViewModel[] {
    return group.documents.filter((documentField) =>
      currentFolderFieldKeysPerGroup?.[group.key]?.includes(
        documentField.field.key,
      ),
    );
  }

  private isServiceSynchronizedWith(
    serviceName: 'DRACOON' | 'NEXTFOLDER',
  ): Observable<boolean> {
    return this.syncLinkForBank$.pipe(
      map((response) => this.checkIsServiceEnabled(response, serviceName)),
      startWith(false),
      catchError(() => of(false)),
      distinctUntilChanged(),
    );
  }

  toggleNavigation(navigation: {
    isChatVisible: boolean;
    platformNavigationIsOpen: boolean;
  }) {
    if (navigation.isChatVisible && navigation.platformNavigationIsOpen) {
      this.store.dispatch(
        StateLibSideNavigationsPageActions.toggleOpenForPlatformNavigation(),
      );
    }
    this.store.dispatch(
      StateLibSideNavigationsPageActions.toggleOpenForPageNavigation(),
    );
  }

  addGroup() {
    this.store.dispatch(
      BusinessCaseDataRoomPageActions.dataRoomAddGroupModal({
        businessCase: cloneDeep(this.businessCase),
        isRepresentingLeadPartner:
          this.accessRights.granular.isRepresentingLeadPartner,
      }),
    );
  }

  // Function to check if service is enabled
  private checkIsServiceEnabled(
    syncLink: DocumentThirdPartySyncLink,
    service: 'DRACOON' | 'NEXTFOLDER',
  ) {
    const linkedService = syncLink.linkedServices.find(
      (service) => service.businessCaseId === this.businessCase.id,
    );

    return linkedService?.isSyncEnabled && linkedService?.service === service;
  }

  private nextfolderDocumentMessage$$: Observable<NextFolderDocument[]> =
    this.socketService
      .getMessagesByDestination$(
        NEXT_FOLDER_DESTINATION,
        SocketType.PLATFORM_NOTIFICATION,
      )
      ?.pipe(
        map((msg): NextFolderDocument[] => {
          return [msg];
        }),
        catchError(() => of([])),
        startWith([]),
        takeUntilDestroyed(this.destroyRef),
      ) || of([]);

  private nextfolderDocumentsFromRequest$$: Observable<NextFolderDocument[]> =
    this.isServiceSynchronizedWithNextfolder$.pipe(
      switchMap(() =>
        this.nextFolderManagementControllerService.getAllNextFolderDocumentsState(
          {
            contextId: this.businessCase.id,
          },
        ),
      ),
      catchError(() => of([])),
    );

  nextfolderDocuments$: Observable<NextFolderDocument[]> = combineLatest([
    this.nextfolderDocumentMessage$$,
    this.nextfolderDocumentsFromRequest$$,
  ]).pipe(
    scan(
      (accumulator, [messageDocuments, nextFolderDocuments]) =>
        [
          ...nextFolderDocuments.filter(
            (reqEntity) =>
              !accumulator.find((entity) => reqEntity.id === entity.id),
          ),
          ...accumulator,
        ]
          .filter((entity) => entity.id !== messageDocuments[0]?.id)
          .concat(messageDocuments),
      [],
    ),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  dataRoomFieldType = DRAGGABLE_TEMPLATE_FIELDS;

  editMode$ = this.store.select(selectDataRoomEditToggle).pipe(
    tap((mode) => {
      this.editMode = mode;

      if (!mode) {
        this.activeDataRoomSidebarTabControl.disable();
        this.activeDataRoomSidebarTabControl.patchValue(
          DataRoomSidebarTabs.GROUPS,
        );
      } else {
        this.activeDataRoomSidebarTabControl.enable();
        this.activeDataRoomSidebarTabControl.patchValue(
          DataRoomSidebarTabs.ADD_DATA,
        );
      }
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  draggableitem$ = this.store.select(selectGetDraggerDataRoomItemType).pipe(
    tap((draggedItemType: DataRoomDraggedItemType) => {
      this.draggedItemType = draggedItemType;
    }),
  );

  readonly sidePanelHeight$ = this.store.select(
    selectBusinessCaseSidePanelHeightDefault,
  );

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightDataRoom,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightDataRoom,
  );

  constructor(
    store: Store<AppState>,
    scrollCommunicationService: ScrollCommunicationService,
    public destroyRef: DestroyRef,
    public documentInteractionsService: DocumentInteractionsService,
    private businessCaseGroupsFieldsService: DataRoomGroupsFieldsService,
    private businessCaseModalService: BusinessCaseModalService,
    private businessCaseHelperService: BusinessCaseDataRoomHelperService,
    private caseDataRoomCustomHandlerService: CaseDataRoomCustomHandlerService,
    private documentSyncControllerService: DocumentSyncControllerService,
    private nextFolderManagementControllerService: NextFolderManagementControllerService,
    private socketService: SocketService,
    private finModalService: FinModalService,
    protected hotkeysService: HotkeyService,
  ) {
    super(destroyRef, store, scrollCommunicationService, hotkeysService);
  }

  ngOnInit(): void {
    this.store.dispatch(StateLibUserPageActions.getAppsCredentials());
    this.listenForSearchChange();

    // not great but terrible
    this.store
      .select(selectUserId)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([userId]) => {
        this.userId = userId;
      });

    this.store
      .select(selectCaseFieldsAccess)
      .pipe(
        filter(() => !!this.businessCase),
        tap(() => this.reinitializeGroups()),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.store
      .select(selectExistingChats)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((chats) => {
        //topic is label, component is id of field
        this.existingTopicChats = keyBy(
          chats.filter(
            (chat) =>
              chat?.chatType === ChatType.ON_TOPIC && chat.status === 'ACTIVE',
          ),
          'component',
        );

        this.existingArchivedTopicChats = keyBy(
          chats.filter(
            (chat) =>
              chat?.chatType === ChatType.ON_TOPIC &&
              chat.status === 'ARCHIVED',
          ),
          'component',
        );
      });

    this.store
      .select(selectBusinessCase)
      .pipe(filter((c) => !!c))
      .pipe(
        filter(
          (businessCaseModified) =>
            !isEqual(this.businessCase, businessCaseModified),
        ),
        withLatestFrom(this.store.select(selectUserCustomerKey)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([businessCaseModified, userCustomerKey]) => {
        this.userCustomerKey = userCustomerKey;
        this.businessCase = cloneDeep(businessCaseModified);
        this.reinitializeGroups();
      });

    this.businessCaseHelperService.editValueEmitter$
      .pipe(takeUntilDestroyed(this.destroyRef), debounceTime(1000))
      .subscribe((businessCaseInformation) => {
        this.store.dispatch(
          StateLibBusinessCasePageActions.setBusinessCaseInformation({
            payload: businessCaseInformation as unknown as InformationRecord,
          }),
        );
      });

    this.store.dispatch(
      StateLibBusinessCasePageActions.loadMirroredFieldKeys({}),
    );

    this.store
      .select(selectAccessRights)
      .pipe(
        filter(Boolean),
        tap((accessRights) => {
          this.accessRights = accessRights;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  ngAfterViewInit(): void {
    this.highlighted$
      .pipe(
        filter(({ key }) => !!key),
        subscribeOn(asapScheduler, SCROLL_TO_DELAY),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(({ key }) => {
        // Makes sure the DOM is updated so that the element could be found
        // When navigated to nested folders or documents
        setTimeoutUnpatched(
          () => this.scrollCommunicationService.scrollToElementByClass(key),
          0,
        );
      });
  }

  addDocumentToSection(group: GroupTemplateFields, field: FileList) {
    const groupField = {
      expression: '',
      fieldMetaData: '',
      fieldType: FieldTypeEnum.DOCUMENT,
      isRequired: false,
      key: '',
      label: '',
      priority: 1,
    } as FieldDto;

    this.openManageFieldModal(groupField, group, field);
  }

  toggleDataRoomInbox() {
    this.store.dispatch(
      StateLibDocumentInboxPageActions.openDocumentsInboxPanelModal(),
    );
  }

  openManageTableModal(group: GroupTemplateFields): void {
    this.businessCaseModalService
      .openManageTableModal(
        this.businessCase,
        group,
        this.accessRights.granular.isRepresentingLeadPartner,
      )
      .ref.result.then((success: string) => {
        if (success) {
          this.clearSearch();
        }
      });
  }

  // Create NEW business case field
  openManageFieldModal(
    field: FieldDto,
    group: GroupTemplateFields,
    initialFile?: FileList,
  ): void {
    this.businessCaseModalService
      .openManageFieldModal(
        'edit',
        {
          field,
          orderedGroups:
            this.businessCase.businessCaseTemplate.template.groupsOrdered.filter(
              (g) =>
                !checkCustomerExplicitGroupVisibility(
                  g,
                  this.userCustomerKey,
                  this.accessRights.granular.isEmployeeOfLeadCustomer,
                ),
            ),
          fieldGroup: group as BusinessCaseGroup,
        },
        cloneDeep(this.businessCase),
        initialFile,
      )
      .afterClosed()
      .pipe(filter(Boolean))
      .subscribe(() => {
        this.clearSearch();
        this.store.dispatch(
          StateLibBusinessCasePageActions.loadBusinessCase({
            payload: this.businessCase.id,
          }),
        );
      });
  }

  openManageFolderModal(group: GroupTemplateFields): void {
    this.finModalService
      .open<string, ManageFolderModalComponent>(ManageFolderModalComponent, {
        data: { groupKey: group.key },
        size: FinSize.S,
      })
      .afterClosed()
      .pipe(filter(Boolean))
      .subscribe({
        next: (folderName): void => {
          this.store.dispatch(
            StateLibFolderStructureFolderPageActions.addFolder({
              name: folderName,
              groupKey: group.key,
            }),
          );
        },
      });
  }

  openDeleteFieldModal(field: Information | CompanyInformation): void {
    const isMirrored$ = this.mirroredFieldKeys$.pipe(
      map((keys) => keys.includes(field.field.key)),
      takeUntilDestroyed(this.destroyRef),
    );
    const deleteMessage = $localize`:@@dataRoom.delete.input.warning:Sind Sie sicher, dass Sie die Eingabe löschen möchten?`;
    const deleteSubMessage = $localize`:@@dataRoom.delete.synced.input.warning:Wenn Sie die Eingabe löschen, wird die Synchronisierung mit den Finanzierungsdetails gestoppt, und Sie können die Synchronisierung nicht wiederherstellen.`;

    this.businessCaseModalService
      .openDeleteFieldModal(isMirrored$, deleteMessage, deleteSubMessage)
      .afterClosed()
      .subscribe((response) => {
        if (response) {
          this.store.dispatch(
            StateLibBusinessCaseDataRoomPageActions.deleteFieldBusinessCaseDataRoom(
              {
                field,
                chat: {
                  existingTopicChats: this.existingTopicChats,
                  existingArchivedTopicChats: this.existingArchivedTopicChats,
                },
              },
            ),
          );
        }
      });
  }

  openEditModal(tab: 'edit' | 'revisions', field: FieldDto) {
    this.store.dispatch(
      StateLibBusinessCaseDataRoomPageActions.editFieldBusinessCaseDataRoom({
        fieldKey: field.key,
        activeTab: tab,
      }),
    );
  }

  private reinitializeGroups() {
    const information = keyBy(
      Object.values(this.businessCase.information),
      'key',
    );

    this.groupsTemplateFields =
      this.businessCaseGroupsFieldsService.getGroupsTemplateFields(
        this.businessCase.businessCaseTemplate.template?.fields,
        this.businessCase.businessCaseTemplate.template.groupsOrdered,
        information,
        null,
      );

    this.groupsTemplateFieldsInitial = cloneDeep(this.groupsTemplateFields);

    if (this.searchControl.value) {
      this.onSearch(this.searchControl.value);
    }
  }

  onFieldValueChanged(data: ValueChangeModel) {
    this.caseDataRoomCustomHandlerService.handleEditValue(data);
  }

  onFileUpload(data: ValueChangeModel) {
    this.store.dispatch(
      StateLibBusinessCaseDataRoomPageActions.dataRoomUploadFile({
        valueChange: data,
      }),
    );
  }

  dropListHover(hovered: boolean, groupIndex: number) {
    if (
      checkCustomerExplicitGroupVisibility(
        this.businessCase.businessCaseTemplate.template.groupsOrdered[
          groupIndex
        ],
        this.userCustomerKey,
        this.accessRights.granular.isEmployeeOfLeadCustomer,
      )
    ) {
      return;
    }

    super.dropListHover(hovered, groupIndex);
  }

  dropListEnterPredicate(
    groupIndex: number,
    isNonFolderOrDocumentSection: boolean,
  ) {
    return (_: CdkDrag, _1: CdkDropList) => {
      // nothing being dragged or searchTerm present
      if (
        this.draggedItemType === this.dataRoomDraggedItemType.NONE ||
        this.searchControl.value?.length >= DATA_ROOM_MIN_SEARCH_LENGTH
      ) {
        return false;
      }

      // dragging long/short/date..etc (except document and folder) over document/folder section
      if (
        isNonFolderOrDocumentSection &&
        this.draggedItemType !== this.dataRoomDraggedItemType.OTHER
      ) {
        return false;
      }

      // dragging document or folder over the default field section (dragging long/short/date..etc)
      if (
        !isNonFolderOrDocumentSection &&
        this.draggedItemType !== this.dataRoomDraggedItemType.DOCUMENT &&
        this.draggedItemType !== this.dataRoomDraggedItemType.FOLDER
      ) {
        return false;
      }

      return !checkCustomerExplicitGroupVisibility(
        this.businessCase.businessCaseTemplate.template.groupsOrdered[
          groupIndex
        ],
        this.userCustomerKey,
        this.accessRights.granular.isEmployeeOfLeadCustomer,
      );
    };
  }

  dragStart(draggableField: DraggableTemplateField) {
    let draggedFieldType: DataRoomDraggedItemType;
    switch (draggableField.fieldType) {
      case 'DOCUMENT':
        draggedFieldType = DataRoomDraggedItemType.DOCUMENT;
        break;
      case 'FOLDER':
        draggedFieldType = DataRoomDraggedItemType.FOLDER;
        break;
      default:
        draggedFieldType = DataRoomDraggedItemType.OTHER;
        break;
    }

    this.store.dispatch(
      StateLibBusinessCaseDataRoomPageActions.dataRoomFieldDraggingStarted({
        payload: draggedFieldType,
      }),
    );
  }

  dragEnded() {
    this.store.dispatch(
      StateLibBusinessCaseDataRoomPageActions.dataRoomFieldDraggingEnded(),
    );
  }
}
