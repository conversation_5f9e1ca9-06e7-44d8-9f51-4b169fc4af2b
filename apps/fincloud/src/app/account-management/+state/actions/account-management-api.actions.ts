import { HttpErrorResponse } from '@angular/common/http';
import {
  AllCustomersForAccountManagerResponse,
  AllUsersForAccountManagerResponse,
  Customer,
  CustomerDto,
  InternalPortalCustomerCount,
  InternalPortalUserCount,
  User,
  UserDetailsUpdateRequest,
  UserDto,
} from '@fincloud/swagger-generator/authorization-server';
import {
  Template,
  TemplateDto,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  CadrTemplate,
  ContactPersonCompanyDto,
} from '@fincloud/swagger-generator/company';
import { Snapshot } from '@fincloud/swagger-generator/demo';
import { DocumentEntity } from '@fincloud/swagger-generator/document';
import {
  CaseParticipation,
  CustomerWithUsername,
  UserWithCreatorName,
} from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

/* ------ Customers ------- */

export const fetchCustomersSuccess = createAction(
  '[Account-management - API] Fetch customers Success',
  props<{ customersResponse: AllCustomersForAccountManagerResponse }>(),
);

export const fetchCustomersFailure = createAction(
  '[Account-management - API] Fetch customers Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const fetchInitialCustomersSuccess = createAction(
  '[Account-management - API] Fetch initial customers Success',
  props<{
    regularCustomersResponse: AllCustomersForAccountManagerResponse;
    guestCustomersResponse: AllCustomersForAccountManagerResponse;
  }>(),
);

export const fetchInitialCustomersFailure = createAction(
  '[Account-management - API] Fetch initial customers Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const setCustomersSuccess = createAction(
  '[Account-management - API] Set customers Success',
  props<{
    customerList: CustomerWithUsername[];
    customersResponse: AllCustomersForAccountManagerResponse;
  }>(),
);

export const createCustomerSuccess = createAction(
  '[Account-management - API] Create customer Success',
  props<{ customer: Customer; logo?: any }>(),
);

export const createCustomerFailure = createAction(
  '[Account-management - API] Create customer Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const editCustomerSuccess = createAction(
  '[Account-management - API] Edit customer Success',
  props<{ customer: Customer; logo?: any }>(),
);

export const editCustomerFailure = createAction(
  '[Account-management - API] Edit customer Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const checkIsExistingCustomerFailure = createAction(
  '[Account-management - API] Check is existing customer Failure',
  props<{ customerDto: CustomerDto }>(),
);

export const checkIsExistingCustomerSuccess = createAction(
  '[Account-management - API] Check is existing customer Success',
);

export const loadCustomerLogoSuccess = createAction(
  '[Account-management - API] Load customer logo Success',
  props<{ logoData: DocumentEntity }>(),
);

export const loadCustomerLogoFailure = createAction(
  '[Account-management - API] Load customer logo Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const uploadCustomerLogoSuccess = createAction(
  '[Account-management - API] Upload customer logo Success',
  props<{ logoData: DocumentEntity }>(),
);

export const uploadCustomerLogoFailure = createAction(
  '[Account-management - API] Upload customer logo Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const customerLogoUploadSuccess = createAction(
  '[Account-management - API] Customer logo upload Success',
);

export const customerLogoUploadFailure = createAction(
  '[Account-management - API] Customer logo upload Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const deleteCustomerLogoSuccess = createAction(
  '[Account-management - API] Delete customer logo Success',
);

export const deleteCustomerLogoFailure = createAction(
  '[Account-management - API] Delete customer logo Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const fetchSelectedCustomerSuccess = createAction(
  '[Account-management - API] Fetch selected customer Success',
  props<{ customer: Customer }>(),
);

export const fetchSelectedCustomerFailure = createAction(
  '[Account-management - API] Fetch selected customer Failure',
  props<{ error: HttpErrorResponse }>(),
);

/* ------ Users ------- */

export const fetchUsersSuccess = createAction(
  '[Account-management - API] Fetch users Success',
  props<{ usersResponse: AllUsersForAccountManagerResponse }>(),
);

export const fetchUsersFailure = createAction(
  '[Account-management - API] Fetch users Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const fetchInitialUsersSuccess = createAction(
  '[Account-management - API] Fetch initial users Success',
  props<{
    activeUsersResponse: AllUsersForAccountManagerResponse;
    deactivatedUsersResponse: AllUsersForAccountManagerResponse;
  }>(),
);

export const fetchInitialUsersFailure = createAction(
  '[Account-management - API] Fetch initial users Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const setUsersSuccess = createAction(
  '[Account-management - API] Set users Success',
  props<{
    usersList: UserWithCreatorName[];
    usersResponse: AllUsersForAccountManagerResponse;
  }>(),
);

export const createUserSuccess = createAction(
  '[Account-management - API] Create user Success',
  props<{ user: User }>(),
);

export const createUserFailure = createAction(
  '[Account-management - API] Create user Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const editUserSuccess = createAction(
  '[Account-management - API] Edit user Success',
  props<{ user: User }>(),
);

export const editUserFailure = createAction(
  '[Account-management - API] Edit user Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const checkIsCompanyEmailTakenSuccess = createAction(
  '[Account-management - API] Check if company e-mail is taken Success',
  props<{ companyEmailTakenName: string }>(),
);

export const checkIsCompanyEmailTakenFailure = createAction(
  '[Account-management - API] Check if company e-mail is taken Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const initiateUserEditSuccess = createAction(
  '[Account-management - API] Initiate user edit Success',
  props<{
    request: {
      userId: string;
      managerUserId: string;
      body: UserDetailsUpdateRequest;
    };
    contactPersonCompanyDto: ContactPersonCompanyDto;
  }>(),
);

export const initiateUserEditFailure = createAction(
  '[Account-management - API] Initiate user edit Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const initiateUserCreationSuccess = createAction(
  '[Account-management - API] Initiate user creation Success',
  props<{
    userDto: UserDto;
    contactPersonCompanyDto: ContactPersonCompanyDto;
  }>(),
);

export const initiateUserCreationFailure = createAction(
  '[Account-management - API] Initiate user creation Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const fetchUserParticipationInCasesSuccess = createAction(
  '[Account-management - API] Fetch user participation in cases Success',
  props<{
    caseParticipation: CaseParticipation;
  }>(),
);

export const fetchUserParticipationInCasesFailure = createAction(
  '[Account-management - API] Fetch user participation in cases Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const fetchActiveAccountManagersSuccess = createAction(
  '[Account-management - API] Fetch account managers Success',
  props<{ activePlatformManagers: User[] }>(),
);

export const fetchActiveAccountManagersFailure = createAction(
  '[Account-management - API] Fetch account managers Failure',
  props<{ error: HttpErrorResponse }>(),
);

/* ------ Templates ------- */

export const fetchBusinessCaseTemplatesSuccess = createAction(
  '[Account-management - API] Fetch Templates Success',
  props<{
    businessCaseTemplatesResponse: Template[];
  }>(),
);

export const fetchBusinessCaseTemplatesFailure = createAction(
  '[Account-management - API] Fetch initial Templates Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const fetchCadrTemplatesSuccess = createAction(
  '[Account-management - API] Fetch Cadr Templates Success',
  props<{
    cadrTemplateResponse: CadrTemplate;
  }>(),
);

export const fetchCadrTemplatesFailure = createAction(
  '[Account-management - API] Fetch initial Cadr Templates Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const initiateBusinessCaseTemplateEditSuccess = createAction(
  '[Account-management - API] Initiate Template Edit Success',
  props<{
    template: TemplateDto;
    isCadrSelected: boolean;
    isNewTemplateMode: boolean;
  }>(),
);

export const initiateBusinessCaseTemplateEditFailure = createAction(
  '[Account-management - API] Update Template Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const initiateCadrTemplateEditSuccess = createAction(
  '[Account-management - API] Initiate Cadr template edit Success',
  props<{
    template: TemplateDto;
    isCadrSelected: boolean;
    isNewTemplateMode: boolean;
  }>(),
);

export const initiateCadrTemplateEditFailure = createAction(
  '[Account-management - API] Initiate Cadr template edit Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const initiateCreateNewBusinessCaseTemplateSuccess = createAction(
  '[Account-management - API] Initiate Business Case Template create Success',
  props<{
    template: TemplateDto;
  }>(),
);

export const initiateCreateNewBusinessCaseTemplateFailure = createAction(
  '[Account-management - API] Initiate Business Case Template create Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const initiateCreateNewCadrTemplateSuccess = createAction(
  '[Account-management - API] Initiate Cadr Template create Success',
  props<{
    template: CadrTemplate;
  }>(),
);

export const initiateCreateNewCadrTemplateFailure = createAction(
  '[Account-management - API] Initiate Cadr Template create Failure',
  props<{ error: string }>(),
);

export const activateUserSuccess = createAction(
  '[Account-management - API] Activate user Success',
);

export const activateUserFailure = createAction(
  '[Account-management - API] Activate user Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const deactivateUserSuccess = createAction(
  '[Account-management - API] Deactivate user Success',
);

export const deactivateUserFailure = createAction(
  '[Account-management - API] Deactivate user Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const fetchUsersCountsSuccess = createAction(
  '[Account-management - API] Fetch user counts Success',
  props<{ usersCount: InternalPortalUserCount }>(),
);

export const fetchInitialUsersCountsSuccess = createAction(
  '[Account-management - API] Fetch initial user counts Success',
  props<{ usersCount: InternalPortalUserCount }>(),
);

export const fetchUsersCountsFailure = createAction(
  '[Account-management - API] Fetch user counts Failure',
);

export const fetchTotalUsersCountsSuccess = createAction(
  '[Account-management - API] Fetch total user counts Success',
  props<{ totalUsersCount: InternalPortalUserCount }>(),
);

export const fetchTotalUsersCountsFailure = createAction(
  '[Account-management - API] Fetch total user counts Failure',
);

export const fetchCustomersCountsSuccess = createAction(
  '[Account-management - API] Fetch Customers counts Success',
  props<{ customersCount: InternalPortalCustomerCount }>(),
);

export const fetchInitialCustomersCountsSuccess = createAction(
  '[Account-management - API] Fetch Initial customers counts Success',
  props<{ customersCount: InternalPortalCustomerCount }>(),
);

export const fetchCustomersCountsFailure = createAction(
  '[Account-management - API] Fetch Customers counts Failure',
);
export const fetchSnapshotForCustomerSuccess = createAction(
  '[Account-management - API] Fetch customer snapshot Success',
  props<{
    snapshot: Snapshot;
    customerKey: string;
  }>(),
);

export const fetchSnapshotForCustomerFailure = createAction(
  '[Account-management - API] Fetch customer snapshot Failure',
);

export const fetchTotalCustomersCountsSuccess = createAction(
  '[Account-management - API] Fetch total Customers counts Success',
  props<{ totalCustomersCount: InternalPortalCustomerCount }>(),
);

export const fetchTotalCustomersCountsFailure = createAction(
  '[Account-management - API] Fetch total Customers counts Failure',
);
