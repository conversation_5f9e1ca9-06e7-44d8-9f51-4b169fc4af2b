import { Component, DestroyRef, OnInit } from '@angular/core';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DateFormats, DateService } from '@fincloud/core/date';

import { CurrencyPipe } from '@angular/common';
import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import {
  DataZoomComponentOption,
  EChartsOption,
  SeriesOption,
  TooltipComponentOption,
  XAXisComponentOption,
  YAXisComponentOption,
  graphic,
} from 'echarts';
import { filter, tap } from 'rxjs';
import { selectOwnIvestmentOverviewData } from '../../+state/selectors/dashboard.selectors';

@Component({
  selector: 'app-bank-historical-investment-widget',
  templateUrl: './bank-historical-investment-widget.component.html',
  styleUrls: ['./bank-historical-investment-widget.component.scss'],
})
export class BankHistoricalInvestmentWidgetComponent implements OnInit {
  chartOptions: EChartsOption;
  isEmpty = false;

  constructor(
    private destroyRef: DestroyRef,
    private currencyPipe: CurrencyPipe,
    private removeTrailingZerosPipe: RemoveTrailingZerosPipe,
    private store: Store<AppState>,
    private dateService: DateService,
  ) {}

  ngOnInit(): void {
    this.store
      .select(selectOwnIvestmentOverviewData)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((data) => !!data),
        tap((ownIvestmentOverviewData) => {
          this.isEmpty = false;

          this.buildHistoricalInvestmentChart(
            Object.keys(ownIvestmentOverviewData).map((key) => [
              key,
              ownIvestmentOverviewData[key],
            ]),
          );
        }),
      )
      .subscribe();
  }

  getHistoricalInvestmentTooltip() {
    return <TooltipComponentOption>{
      trigger: 'axis',
      axisPointer: {
        axis: 'auto',
      },
      formatter: (params: any) => {
        return this.removeTrailingZerosPipe.transform(
          this.currencyPipe.transform(params[0].value[1]),
        );
      },
      position: (position) => {
        return [position[0], '10%'];
      },
      borderRadius: 4,
      textStyle: {
        fontWeight: 600,
        fontSize: 12,
        lineHeight: 18,
        color: '#fff',
      },
      className: 'chart-tooltip',
    };
  }

  getHistoricalInvestmentChartXAxis(): XAXisComponentOption {
    return {
      type: 'category',
      boundaryGap: false,
      axisLabel: {
        formatter: (axisValue) => {
          return this.dateService.formatDate(
            axisValue,
            DateFormats.MMM_SPACE_YY,
          );
        },
      },
      splitLine: {
        show: false,
      },
    };
  }

  getHistoricalInvestmentChartYAxis(): YAXisComponentOption {
    return {
      type: 'value',
      position: 'right',
      axisLabel: {
        formatter: (params: any) => {
          return this.removeTrailingZerosPipe.transform(
            this.currencyPipe.transform(params),
          );
        },
      },
      splitLine: {
        show: false,
      },
    };
  }

  getHistoricalInvestmentChartDataZoom(
    data: (string | number)[][],
  ): DataZoomComponentOption[] {
    return [
      {
        type: 'inside',
        startValue: data.length - 5, // show last 6 months
        endValue: data.length,
        zoomLock: true,
      },
    ];
  }

  getHistoricalInvestmentChartSeries(
    data: (string | number)[][],
  ): SeriesOption[] {
    const series = <SeriesOption[]>[
      {
        data: data,
        type: 'line',
        smooth: true,
        triggerLineEvent: true,
        showSymbol: false,
        color: ['#00675D'],
        lineStyle: {
          color: '#00675D',
        },
        areaStyle: {
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: '#cbedd1',
            },
            {
              offset: 1,
              color: '#FFFFFF',
            },
          ]),
        },
      },
    ];

    // If only one entry, then add a vertical line to highlight the only entry in the chart
    if (data.length === 1) {
      series.push({
        data: data,
        type: 'line',
        color: ['black'],
        markLine: {
          symbol: 'none',
          lineStyle: {
            color: 'black',
          },
          data: [
            [
              { xAxis: data[0][0], yAxis: 0 },
              { xAxis: data[0][0], yAxis: 'max' },
            ],
          ],
        },
      });
    }

    return series;
  }

  getHistoricalInvestmentChartGrid() {
    return {
      left: 30,
      right: 20,
      bottom: 30,
      containLabel: true,
    };
  }

  getHistoricalInvestmentChartEmphasis() {
    return {
      scale: true,
      scaleSize: 20,
    };
  }

  buildHistoricalInvestmentChart(data: (string | number)[][]) {
    if (data.length === 0) {
      this.isEmpty = true;
      return;
    }

    this.chartOptions = {
      grid: this.getHistoricalInvestmentChartGrid(),
      tooltip: this.getHistoricalInvestmentTooltip(),
      xAxis: this.getHistoricalInvestmentChartXAxis(),
      yAxis: this.getHistoricalInvestmentChartYAxis(),
      dataZoom: this.getHistoricalInvestmentChartDataZoom(data),
      series: this.getHistoricalInvestmentChartSeries(data),
      emphasis: this.getHistoricalInvestmentChartEmphasis(),
    };
  }
}
