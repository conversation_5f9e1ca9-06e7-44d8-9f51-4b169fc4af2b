import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { NonNullableFormBuilder, Validators } from '@angular/forms';
import { DateFormats, DateService } from '@fincloud/core/date';
import { Snapshot } from '@fincloud/swagger-generator/demo';
import { DefaultDemoType } from '@fincloud/types/enums';
import { Store } from '@ngrx/store';
import { DemoSnapshotCRUDPageActions } from '../../+state/actions';
import { SNAPSHOT_TYPE_OPTIONS } from '../../utils/snapshot-type-options';

@Component({
  selector: 'app-general-demo-information',
  templateUrl: './general-demo-information.component.html',
  styleUrls: ['./general-demo-information.component.scss'],
})
export class GeneralDemoInformationComponent implements OnChanges {
  @Input() snapshot = {} as Snapshot;
  @Output() updateCompleted = new EventEmitter();

  readonly todayDate = this.dateService.formatDate(
    new Date(),
    DateFormats.YYYY_DASH_MM_DASH_DD,
  );
  readonly todayDateStruct = this.dateService.getFormattedTimeNgbDateStruct(
    new Date().toISOString(),
  );

  generalInformationForm = this.fb.group({
    name: ['', [Validators.required]],
    demoType: ['' as NonNullable<Snapshot['demoType']>, [Validators.required]],
    autoDeletionDate: [''],
    potentialCustomerName: [''],
    comment: [''],
  });

  typeOptions = SNAPSHOT_TYPE_OPTIONS;

  get autoDeletionDateControl() {
    return this.generalInformationForm.get('autoDeletionDate');
  }

  constructor(
    private store: Store,
    private fb: NonNullableFormBuilder,
    private dateService: DateService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.snapshot.currentValue) {
      this.generalInformationForm.patchValue(changes.snapshot.currentValue);
      this.onDemoTypeChanged(this.generalInformationForm.value.demoType);
    }
  }

  // TODO: Demo - move to a service
  updateGeneralInformation() {
    if (!this.generalInformationForm.valid) {
      return;
    }

    const generalInfoPayload = {
      ...this.generalInformationForm.getRawValue(),
    };

    if (generalInfoPayload.autoDeletionDate) {
      const paddedDate = this.dateService.formatDate(
        generalInfoPayload.autoDeletionDate,
        DateFormats.YYYY_DASH_MM_DASH_DD,
      );

      generalInfoPayload.autoDeletionDate = `${paddedDate}T23:59:59.000Z`; // ignore timezone offset
    }

    this.store.dispatch(
      DemoSnapshotCRUDPageActions.setGeneralInformationForCreateSnapshot(
        generalInfoPayload,
      ),
    );
    this.updateCompleted.emit(generalInfoPayload);
  }

  triggerValidation() {
    this.generalInformationForm.markAllAsTouched();
    Object.keys(this.generalInformationForm.controls).forEach((controlName) => {
      this.generalInformationForm.get(controlName).updateValueAndValidity();
    });
  }

  onDemoTypeChanged(type: string) {
    if (type === DefaultDemoType.MASTER) {
      this.autoDeletionDateControl.disable();
      this.autoDeletionDateControl.setValue('');
    } else {
      this.autoDeletionDateControl.enable();
    }
  }
}
