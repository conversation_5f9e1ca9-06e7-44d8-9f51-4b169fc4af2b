import { HttpErrorResponse } from '@angular/common/http';
import { DocumentEntity } from '@fincloud/swagger-generator/document';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { BusinessCase } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const loadBusinessCaseSuccess = createAction(
  '[BusinessCase API] Load Success',
  props<{ payload: ExchangeBusinessCase }>(),
);

export const loadMirroredFieldKeysSuccess = createAction(
  '[Business Case API] Load and set mirrored field keys Success',
  props<{ eligibleFieldKeys: string[]; dataRoomTemplateFieldKeys: string[] }>(),
);

export const loadMirroredFieldKeysFailure = createAction(
  '[Business Case API] Load and set mirrored field keys Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const loadMirroredCalculatableFieldsSuccess = createAction(
  '[Business Case API] Load and set mirrored calculatable field keys Success',
  props<{ calculatableFieldKeys: string[] }>(),
);

export const loadMirroredCalculatableFieldsFailure = createAction(
  '[Business Case API] Load and set mirrored calculatable field keys Failure',
  props<{ error: HttpErrorResponse }>(),
);

// Since there is delay updating the business case (polling) we have to do it beforehand
// TODO: Revise after sockets are implemented for updating business case data
export const businessCaseDocumentUploadedSuccess = createAction(
  '[BusinessCase API] Document upload Success',
  props<{ document: DocumentEntity }>(),
);

export const partialRefreshBusinessCaseSuccess = createAction(
  '[BusinessCase API] Refresh Success',
  props<{ payload: BusinessCase }>(),
);
