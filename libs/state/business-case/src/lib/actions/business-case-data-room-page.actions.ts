import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';
import { Information } from '@fincloud/swagger-generator/demo';
import { DataRoomDraggedItemType, FieldType } from '@fincloud/types/enums';
import { DataRoomChats, ValueChangeModel } from '@fincloud/types/models';
import { FinTreeNode } from '@fincloud/ui/tree-menu';
import { createAction, props } from '@ngrx/store';

export const deleteFieldBusinessCaseDataRoom = createAction(
  '[Business case - Data Room Page]  Delete field',
  props<{
    field: Information | CompanyInformation;
    chat: DataRoomChats;
    successMessage?: string;
  }>(),
);

export const editFieldBusinessCaseDataRoom = createAction(
  '[Business case - Data Room Page]  Edit field',
  props<{
    fieldKey: string;
    activeTab: 'edit' | 'revisions';
  }>(),
);

export const dataRoomFieldDraggingStarted = createAction(
  '[Business case - Data Room Page]  Field dragging started',
  props<{
    payload: DataRoomDraggedItemType;
  }>(),
);

export const dataRoomFieldDraggingEnded = createAction(
  '[Business case - Data Room Page]  Field dragging ended',
);

export const highlightDataRoomItem = createAction(
  '[Business case - Data Room Page] Highlight data room item',
  props<{ node: FinTreeNode<any> }>(),
);

export const setHighlightedField = createAction(
  '[Business case - Data Room Page] Set highlighted field',
  props<{ highlight: string; groupKey: string; fieldType?: FieldType }>(),
);

export const dataRoomSideNavigationItemSelected = createAction(
  '[Business case - Data Room Page] Side navigation item selected',
  props<{ node: FinTreeNode<any> }>(),
);

export const dataRoomSideNavigationToggleItem = createAction(
  '[Business case - Data Room Page] Side navigation toggle item',
  props<{ [key: string]: boolean }>(),
);

export const dataRoomUploadFile = createAction(
  '[Business case - Data Room Page] Upload file',
  props<{ valueChange: ValueChangeModel }>(),
);
