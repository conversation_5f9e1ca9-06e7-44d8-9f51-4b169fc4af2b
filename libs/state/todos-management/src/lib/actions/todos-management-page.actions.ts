import { UserAssignment } from '@fincloud/swagger-generator/internal-tools';
import {
  TodosApiPerspective,
  TodosStatus,
  TodosType,
} from '@fincloud/types/enums';
import { TodosRouterParams } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const todosSummarySocketConnect = createAction(
  '[Todos Management Summary Page] Connect to websocket',
);

export const requestTodosSummaryCount = createAction(
  '[Todos Management Summary Page] Request todos count for summary',
);

export const toggleTodosSummary = createAction(
  '[Todos Management Summary Page] Toggle todo summary menu',
);

export const closeTodosSummary = createAction(
  '[Todos Management Summary Page] Close todo summary menu',
);

export const changeSummaryTab = createAction(
  '[Todos Management Summary Page] Change tab in todos summary',
  props<{ perspective: TodosApiPerspective }>(),
);

export const loadTodosSummary = createAction(
  '[Todos Management Summary Page] Load todos in summary',
);

export const redirectByTodoType = createAction(
  '[Todos Management Page] Redirect by todo type',
  props<{
    todo: UserAssignment;
  }>(),
);

export const redirectToTodo = createAction(
  '[Todos Management Page] Redirect to todo',
  props<{
    perspective: TodosType;
    status: TodosStatus;
    businessCaseId: string;
    todoId: string;
    highlight: boolean;
  }>(),
);

export const setCurrentRouteParams = createAction(
  '[Todos Page Page] Set current route params',
  props<{
    currentRouteParams: TodosRouterParams;
  }>(),
);
