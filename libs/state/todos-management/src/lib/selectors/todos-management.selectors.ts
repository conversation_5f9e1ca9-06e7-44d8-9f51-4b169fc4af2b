import {
  selectCustomerType,
  selectIsCustomerGuest,
} from '@fincloud/state/customer';
import { selectQueryParams, selectRouteParams } from '@fincloud/state/router';
import { CustomerType, TodosApiPerspective } from '@fincloud/types/enums';
import { TodosManagementBadgeState } from '@fincloud/types/models';
import { createSelector } from '@ngrx/store';
import { BaseSelectors } from '@ngrx/store/src/feature_creator';

// eslint-disable-next-line @ngrx/prefix-selectors-with-select
export const todosManagementSelectors = (
  todosManagementBadge: BaseSelectors<
    'todosManagementBadge',
    TodosManagementBadgeState
  >,
) => {
  const selectTotalPendingTodosCount = createSelector(
    todosManagementBadge.selectCounts,
    ({ delegatedTasksCount, myTasksCount }) =>
      delegatedTasksCount + myTasksCount || 0,
  );

  const selectSelectedSummaryTab = createSelector(
    todosManagementBadge.selectFilters,
    ({ perspective }) => perspective,
  );

  const selectSummaryShowEmptyView = createSelector(
    todosManagementBadge.selectTodos,
    todosManagementBadge.selectCounts,
    todosManagementBadge.selectFilters,
    todosManagementBadge.selectIsSummaryLoading,
    (
      todos,
      { myTasksCount, delegatedTasksCount },
      { perspective },
      isLoading,
    ) => {
      const count =
        perspective === TodosApiPerspective.MY_TASKS
          ? myTasksCount
          : delegatedTasksCount;

      return todos.length === 0 && count === 0 && !isLoading;
    },
  );

  const selectSummaryShowLoadingView = createSelector(
    todosManagementBadge.selectTodos,
    todosManagementBadge.selectIsSummaryLoading,
    (todos, isLoading) => todos.length === 0 && isLoading,
  );

  const selectIsTodosFeatureAvailable = createSelector(
    selectIsCustomerGuest,
    selectCustomerType,
    (isGuest, customerType) => {
      const isInternalCustomer = customerType === CustomerType.INTERNAL;

      return !isGuest && !isInternalCustomer;
    },
  );

  const selectCurrentRouteParams = createSelector(
    selectRouteParams,
    ({ todoType, todoStatus }) => ({
      todoType,
      todoStatus,
    }),
  );

  const selectHighlightedTodo = createSelector(
    selectQueryParams,
    (queryParams) => queryParams['highlighted'] ?? 'false',
  );

  return {
    selectTotalPendingTodosCount,
    selectSelectedSummaryTab,
    selectIsTodosFeatureAvailable,
    selectSummaryShowEmptyView,
    selectSummaryShowLoadingView,
    selectCurrentRouteParams,
    selectHighlightedTodo,
  };
};
