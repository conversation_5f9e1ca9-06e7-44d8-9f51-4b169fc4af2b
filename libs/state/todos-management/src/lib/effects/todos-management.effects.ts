import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { selectCustomerKey } from '@fincloud/state/customer';
import { selectRouteParams } from '@fincloud/state/router';
import {
  StateLibUserApiActions,
  StateLibUserPageActions,
} from '@fincloud/state/user';
import { UserAssignmentControllerService } from '@fincloud/swagger-generator/internal-tools';
import {
  TodosApiPerspective,
  TodosApiStatus,
  TodosApiUserAssignmentType,
} from '@fincloud/types/enums';
import { FinToastService, FinToastType } from '@fincloud/ui/toast';
import { PLATFORM_NOTIFICATION_TODO_SYSTEM_UPDATE_DESTINATION } from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { isNil } from 'lodash-es';
import {
  catchError,
  debounceTime,
  filter,
  map,
  of,
  switchMap,
  tap,
} from 'rxjs';
import {
  StateLibTodosManagementApiActions,
  StateLibTodosManagementPageActions,
} from '../actions';
import { todosManagementBadgeFeature } from '../reducers/todos-management.reducer';

@Injectable()
export class StateLibTodosManagementEffects {
  constructor(
    private actions$: Actions,
    private store: Store,
    private router: Router,
    private userAssignmentControllerService: UserAssignmentControllerService,
    private socketService: SocketService,
    private finToastService: FinToastService,
  ) {}

  todoUpdatedMessage = $localize`:@@todoItem.completed.message:Aufgabe erledigt`;

  requestTodosSummaryCount$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibTodosManagementPageActions.requestTodosSummaryCount,
        StateLibTodosManagementPageActions.toggleTodosSummary,
        StateLibUserPageActions.setUser,
      ),
      switchMap(() => {
        return this.userAssignmentControllerService
          .getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary({
            statuses: [TodosApiStatus.PENDING],
          })
          .pipe(
            map((counts) =>
              StateLibTodosManagementApiActions.getTodosCounterSuccess({
                counts,
              }),
            ),
            catchError((error) =>
              of(
                StateLibTodosManagementApiActions.getTodosCounterFailure({
                  error,
                }),
              ),
            ),
          );
      }),
    );
  });

  requestTodosSummaryData$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibTodosManagementPageActions.toggleTodosSummary,
        StateLibTodosManagementPageActions.changeSummaryTab,
        StateLibTodosManagementPageActions.loadTodosSummary,
      ),
      concatLatestFrom(() => [
        this.store.select(todosManagementBadgeFeature.selectIsSummaryOpened),
        this.store.select(todosManagementBadgeFeature.selectFilters),
        this.store.select(todosManagementBadgeFeature.selectTodos),
        this.store.select(todosManagementBadgeFeature.selectCounts),
      ]),
      filter(([, isSummaryOpened]) => isSummaryOpened),
      filter(
        ([
          ,
          ,
          { perspective },
          todos,
          { myTasksCount, delegatedTasksCount },
        ]) => {
          // When open the summary does not have counts yet
          if (isNil(myTasksCount) && isNil(delegatedTasksCount)) {
            return true;
          }

          return perspective === TodosApiPerspective.MY_TASKS
            ? todos.length < myTasksCount || myTasksCount === 0
            : todos.length < delegatedTasksCount || delegatedTasksCount === 0;
        },
      ),
      switchMap(([, , filters, todos]) => {
        return this.userAssignmentControllerService
          .getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary({
            ...filters,
            offset: todos?.length,
          })
          .pipe(
            map((todos) =>
              StateLibTodosManagementApiActions.getTodosSummarySuccess({
                todos,
              }),
            ),
            catchError((error) =>
              of(
                StateLibTodosManagementApiActions.getTodosSummaryFailure({
                  error,
                }),
              ),
            ),
          );
      }),
    );
  });

  listenWebSocketData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserApiActions.connectUploadFilesWebSocketSuccess),
      switchMap(() =>
        this.socketService
          .getMessagesByDestination$<{
            action: string;
            sourceService: string;
          }>(
            PLATFORM_NOTIFICATION_TODO_SYSTEM_UPDATE_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          )
          .pipe(
            debounceTime(300),
            tap(({ action, sourceService }) => {
              if (
                action === 'UPDATED' &&
                sourceService === 'srv-contract-management'
              ) {
                this.finToastService.show({
                  type: FinToastType.SUCCESS,
                  message: this.todoUpdatedMessage,
                });
              }
            }),
            map(() =>
              StateLibTodosManagementPageActions.requestTodosSummaryCount(),
            ),
          ),
      ),
    ),
  );

  // Redirect to specific place in the platform based on the todo type
  setCurrentRouteParams$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibTodosManagementPageActions.redirectByTodoType),
      concatLatestFrom(() =>
        this.store.select(todosManagementBadgeFeature.selectCurrentRouteParams),
      ),
      map(([action, currentRouteParams]) =>
        StateLibTodosManagementPageActions.setCurrentRouteParams({
          currentRouteParams,
        }),
      ),
    ),
  );

  reviewContractRedirect$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibTodosManagementPageActions.redirectByTodoType),
        filter(
          ({ todo: { type } }) =>
            type === TodosApiUserAssignmentType.REVIEW_CONTRACT,
        ),
        concatLatestFrom(() => [
          this.store.select(selectCustomerKey),
          this.store.select(
            todosManagementBadgeFeature.selectSelectedSummaryTab,
          ),
        ]),
        tap(
          ([
            {
              todo: { referenceRecordId: highlighted },
            },
            customerKey,
            perspective,
          ]) => {
            const urlSegment =
              perspective === TodosApiPerspective.DELEGATED
                ? 'contract-management'
                : 'digital-signature';

            this.router.navigate(['/', customerKey, urlSegment], {
              queryParams: { highlighted },
              /**
               * If the user clicks on the todo, scrolls away from the
               * highlighted item and then clicks on the todo again,
               * they should be scrolled back to the highlighted item
               * */
              onSameUrlNavigation: 'reload',
            });
          },
        ),
      ),
    { dispatch: false },
  );

  provideDataRedirect$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibTodosManagementPageActions.redirectByTodoType),
        filter(({ todo: { type } }) =>
          [
            TodosApiUserAssignmentType.PROVIDE_DATA,
            TodosApiUserAssignmentType.MANUAL_ASSIGNMENT,
          ].includes(type as TodosApiUserAssignmentType),
        ),
        concatLatestFrom(() => this.store.select(selectCustomerKey)),

        tap(
          ([
            {
              todo: {
                businessCaseId,
                metadata: { fieldInputRequestFieldKey: highlighted },
              },
            },
            customerKey,
          ]) => {
            this.router.navigate(
              [
                '/',
                customerKey,
                'business-case',
                businessCaseId,
                'data-room',
                'case',
              ],
              {
                queryParams: { highlighted },
                /**
                 * If the user clicks on the todo, scrolls away from the
                 * highlighted item and then clicks on the todo again,
                 * they should be scrolled back to the highlighted item
                 * */
                onSameUrlNavigation: 'reload',
              },
            );
          },
        ),
      ),
    { dispatch: false },
  );

  redirectToToDo$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibTodosManagementPageActions.redirectToTodo),
        concatLatestFrom(() => [
          this.store.select(selectCustomerKey),
          this.store.select(selectRouteParams),
        ]),
        map(
          ([
            { businessCaseId, todoId, status, perspective, highlight },
            customerKey,
          ]) => {
            this.router.navigate(
              [
                '/',
                customerKey,
                'todos-management',
                perspective,
                status,
                businessCaseId,
                todoId,
              ],
              {
                queryParams: { highlighted: highlight },
              },
            );
          },
        ),
      ),
    { dispatch: false },
  );
}
