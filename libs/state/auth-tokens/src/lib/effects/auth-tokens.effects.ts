import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
  AuthService,
  AuthenticationService,
  LOGIN_REDIRECT_URL,
  SESSION_CUSTOMER_KEY,
  SESSION_STORAGE_UNIQUE_INSTANCE_ID,
  TokenManagementService,
} from '@fincloud/core/auth';
import {
  StateLibLoginApiActions,
  StateLibLoginPageActions,
} from '@fincloud/state/login';
import { selectRouteCustomerKey } from '@fincloud/state/router';
import {
  AutomationControllerService,
  TokenControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import {
  Actions,
  ROOT_EFFECTS_INIT,
  createEffect,
  ofType,
} from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import {
  catchError,
  exhaustMap,
  filter,
  map,
  of,
  switchMap,
  tap,
  timer,
} from 'rxjs';

import { ENVIRONMENT_TOKEN } from '@fincloud/core/config';
import { UserUpdatedLogService } from '@fincloud/core/user';
import { AccessTokenStatus } from '@fincloud/types/enums';
import {
  AuthTokensState,
  Environment,
  LocalEnvironment,
} from '@fincloud/types/models';
import { concatLatestFrom } from '@ngrx/operators';
import { v4 as uuidv4 } from 'uuid';
import {
  StateLibAuthTokensApiActions,
  StateLibAuthTokensPageActions,
} from '../actions';

@Injectable()
export class StateLibAuthTokensEffects {
  constructor(
    private actions$: Actions,
    private tokenManagementService: TokenManagementService,
    private authService: AuthService,
    private sessionStorageService: SessionStorageService,
    private localStorageService: LocalStorageService,
    private automationControllerService: AutomationControllerService,
    private authenticationService: AuthenticationService,
    private router: Router,
    private store: Store<AuthTokensState>,
    private tokenControllerService: TokenControllerService,
    private userUpdatedLogService: UserUpdatedLogService,
    @Inject(ENVIRONMENT_TOKEN)
    private environmentConfig: Environment | LocalEnvironment,
  ) {}

  obtainToken$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibAuthTokensPageActions.obtainToken),
      switchMap(({ code }) =>
        this.authService.tokenObtain(code).pipe(
          map(({ access_token: accessToken, refresh_token: refreshToken }) => {
            const decodedToken =
              this.tokenManagementService.decodeToken(accessToken);
            const customerKey = decodedToken.customer_key;

            return StateLibAuthTokensApiActions.obtainTokenSuccess({
              token: {
                accessToken,
                refreshToken,
              },
              customerKey,
              decodedToken,
            });
          }),
          catchError((error) =>
            of(
              StateLibAuthTokensApiActions.obtainTokenFailure({
                error,
              }),
            ),
          ),
        ),
      ),
    );
  });

  /* This effect requests token only for localhost environment */
  localTokenObtain$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibAuthTokensPageActions.obtainTokenLocal),
      switchMap(({ password, username }) => {
        return this.automationControllerService
          .tokenObtain({
            username,
            password,
            securityCode: this.environmentConfig.automationToken,
          })
          .pipe(
            map(
              ({ access_token: accessToken, refresh_token: refreshToken }) => {
                const decodedToken =
                  this.tokenManagementService.decodeToken(accessToken);
                const customerKey = decodedToken.customer_key;

                return StateLibAuthTokensApiActions.obtainTokenSuccess({
                  token: { accessToken, refreshToken },
                  customerKey,
                  decodedToken,
                });
              },
            ),
            catchError((error) =>
              of(
                StateLibAuthTokensApiActions.obtainTokenFailure({
                  error,
                }),
              ),
            ),
          );
      }),
    );
  });

  obtainTokenSuccess$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibAuthTokensApiActions.obtainTokenSuccess),
        tap((data) => {
          this.authenticationService.setTokens(
            data.token.accessToken,
            data.decodedToken,
          );

          this.tokenManagementService.storeToken(data.customerKey, {
            openedAngularInstances: {},
            tokenRaw: data.token,
            decodedToken: data.decodedToken,
            loginTime: new Date().getTime(),
            isRefreshTokenLoading: false,
            refreshRequestId: '',
            status: AccessTokenStatus.VALID,
          });

          const redirectUrl =
            this.localStorageService.retrieve(LOGIN_REDIRECT_URL);
          this.sessionStorageService.store(
            SESSION_CUSTOMER_KEY,
            data.customerKey,
          );

          if (!redirectUrl) {
            this.localStorageService.store(
              LOGIN_REDIRECT_URL,
              `/${data.customerKey}`,
            );
          }

          this.router.navigate(['/', data.customerKey]);
        }),
      );
    },
    { dispatch: false },
  );

  refreshToken$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibAuthTokensPageActions.obtainRefreshToken),
      concatLatestFrom(() => [this.store.select(selectRouteCustomerKey)]),
      tap(([state, customerKey]) => {
        if (document.visibilityState !== 'visible') {
          this.tokenManagementService.updateToken(customerKey, {
            isRefreshTokenLoading: false,
          });
        }
      }),
      filter(() => document.visibilityState === 'visible'),
      exhaustMap(([state, customerKey]) => {
        this.tokenManagementService.updateToken(customerKey, {
          isRefreshTokenLoading: true,
        });
        const refreshToken =
          this.tokenManagementService.getToken(customerKey)?.tokenRaw
            ?.refreshToken;
        return this.authService.tokenRefresh(customerKey, refreshToken).pipe(
          map(({ access_token: accessToken, refresh_token: refreshToken }) => {
            const decodedToken =
              this.tokenManagementService.decodeToken(accessToken);

            return StateLibAuthTokensApiActions.obtainRefreshTokenSuccess({
              token: {
                accessToken,
                refreshToken,
              },
              customerKey,
              decodedToken,
            });
          }),
          catchError(({ error }) => {
            return timer(200).pipe(
              map(() => {
                if (error?.error === 'invalid_grant') {
                  /* If the system starts two times token refresh only one of them will pass.
                  That's why we are checking if some other request already changed the
                  token in local storage and if we have a new token mark the request as successfull */

                  const customerToken =
                    this.tokenManagementService.getToken(customerKey);
                  const currentTime = parseInt(
                    `${new Date().getTime() / 1000}`,
                    10,
                  );

                  if (
                    customerToken.tokenRaw.refreshToken !== refreshToken &&
                    customerToken.decodedToken.exp > currentTime
                  ) {
                    return StateLibAuthTokensApiActions.obtainRefreshTokenSuccess(
                      {
                        token: customerToken.tokenRaw,
                        customerKey,
                        decodedToken: customerToken.decodedToken,
                      },
                    );
                  }
                }

                return StateLibAuthTokensApiActions.obtainRefreshTokenFailure({
                  error,
                  customerKey,
                });
              }),
            );
          }),
        );
      }),
    );
  });

  refreshTokenSuccess$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibAuthTokensApiActions.obtainRefreshTokenSuccess),
        tap((data) => {
          this.authenticationService.setTokens(
            data.token.accessToken,
            data.decodedToken,
          );

          this.tokenManagementService.updateToken(data.customerKey, {
            tokenRaw: data.token,
            decodedToken: data.decodedToken,
            isRefreshTokenLoading: false,
            status: AccessTokenStatus.VALID,
          });
        }),
      );
    },
    { dispatch: false },
  );

  refreshTokenFailure$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibAuthTokensApiActions.obtainRefreshTokenFailure),
      map(() => StateLibLoginPageActions.logout()),
    );
  });

  validateAccessToken = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibAuthTokensPageActions.validateAccessToken),
      concatLatestFrom(() => this.store.select(selectRouteCustomerKey)),
      switchMap(([_, customerKey]) => {
        this.tokenManagementService.updateToken(customerKey, {
          status: AccessTokenStatus.PENDING,
        });

        const token =
          this.tokenManagementService.getToken(customerKey)?.tokenRaw
            ?.accessToken;

        if (token) {
          return this.tokenControllerService.validateToken({ token }).pipe(
            map(() =>
              StateLibAuthTokensApiActions.validateAccessTokenSuccess(),
            ),
            catchError(() =>
              of(StateLibAuthTokensApiActions.validateAccessTokenFailure()),
            ),
          );
        }

        return of(StateLibLoginPageActions.logout());
      }),
    );
  });

  setAccessTokenValidStatus = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibAuthTokensApiActions.validateAccessTokenSuccess),
        concatLatestFrom(() => this.store.select(selectRouteCustomerKey)),
        tap(([_, customerKey]) => {
          this.tokenManagementService.updateToken(customerKey, {
            status: AccessTokenStatus.VALID,
          });
        }),
      );
    },
    { dispatch: false },
  );

  setAccessTokenInvalidStatus = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibAuthTokensApiActions.validateAccessTokenFailure),
      concatLatestFrom(() => this.store.select(selectRouteCustomerKey)),
      tap(([_, customerKey]) => {
        this.tokenManagementService.updateToken(customerKey, {
          status: AccessTokenStatus.INVALID,
          isRefreshTokenLoading: true,
        });
      }),
      map(([_, customerKey]) =>
        StateLibAuthTokensPageActions.obtainRefreshToken({ customerKey }),
      ),
    );
  });

  clearStateOnLogout$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibLoginApiActions.logoutSuccess),
      map(({ customerKey }) =>
        StateLibAuthTokensPageActions.clearAuthTokensState({ customerKey }),
      ),
    );
  });

  redirectToLoginOnStateClear$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibAuthTokensPageActions.clearAuthTokensState),
        tap(() => {
          this.userUpdatedLogService.clearActivityLogInterval();
          return this.router.navigate(['/', 'login']);
        }),
      );
    },
    { dispatch: false },
  );

  generateUniqueInstanceId$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(ROOT_EFFECTS_INIT),
        tap(() => {
          const uniqueInstanceId = this.sessionStorageService.retrieve(
            SESSION_STORAGE_UNIQUE_INSTANCE_ID,
          );

          if (!uniqueInstanceId) {
            this.sessionStorageService.store(
              SESSION_STORAGE_UNIQUE_INSTANCE_ID,
              uuidv4(),
            );
          }
        }),
        concatLatestFrom(() => [this.store.select(selectRouteCustomerKey)]),
        filter(
          ([action, customerKey]) =>
            this.tokenManagementService.getToken(customerKey)
              ?.isRefreshTokenLoading ?? false,
        ),
        tap(([action, customerKey]) => {
          // This will unblock the requests then they are stuck and refresh the page.
          this.tokenManagementService.updateToken(customerKey, {
            isRefreshTokenLoading: false,
          });
        }),
      );
    },
    { dispatch: false },
  );

  storeUniqueInstanceInLocalStorage$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibAuthTokensPageActions.addUniqueInstanceStatus),
        tap(({ customerKey, status }): void => {
          this.tokenManagementService.updateToken(customerKey, {
            openedAngularInstances: {
              [this.sessionStorageService.retrieve(
                SESSION_STORAGE_UNIQUE_INSTANCE_ID,
              )]: status,
            },
          });
        }),
      );
    },
    { dispatch: false },
  );
}
