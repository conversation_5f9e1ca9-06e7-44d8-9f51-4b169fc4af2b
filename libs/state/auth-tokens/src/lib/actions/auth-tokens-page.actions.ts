import { UniqueInstanceStatusType } from '@fincloud/types/enums';
import { UserToken } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const setDecodedToken = createAction(
  '[State Lib Auth Tokens Page] Set Decoded Token',
  props<{ decodedToken: UserToken; customerKey: string }>(),
);

export const obtainToken = createAction(
  '[State Lib Auth Tokens Page] Obtain token',
  props<{ code: string }>(),
);

export const obtainTokenLocal = createAction(
  '[State Lib Auth Tokens Page] Obtain token local',
  props<{
    password: string;
    username: string;
  }>(),
);

export const validateAccessToken = createAction(
  '[State Lib Auth Tokens Page] Validate access token',
);

export const obtainRefreshToken = createAction(
  '[State Lib Auth Tokens Page] Obtain refresh token',
  props<{ customerKey: string }>(),
);

export const clearLoginSession = createAction(
  '[State Lib Auth Tokens Page] Clear login session',
);

export const clearAuthTokensState = createAction(
  '[State Lib Auth Tokens Page] Clear auth tokens state',
  props<{ customerKey: string }>(),
);

export const addUniqueInstanceStatus = createAction(
  '[State Lib Auth Tokens Page] Add angular unique instance to token',
  props<{ customerKey: string; status: UniqueInstanceStatusType }>(),
);
