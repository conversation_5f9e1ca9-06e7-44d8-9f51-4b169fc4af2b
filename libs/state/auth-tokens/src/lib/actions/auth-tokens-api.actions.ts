import { CustomerToken, UserToken } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const obtainTokenSuccess = createAction(
  '[State Lib Auth Tokens API] Obtain token Success',
  props<{
    token: CustomerToken;
    customerKey: string;
    decodedToken: UserToken;
  }>(),
);

export const obtainTokenFailure = createAction(
  '[State Lib Auth Tokens API] Obtain token Failure',
  props<{ error: unknown }>(),
);

export const obtainRefreshTokenSuccess = createAction(
  '[State Lib Auth Tokens API] Obtain refresh token Success',
  props<{
    token: CustomerToken;
    customerKey: string;
    decodedToken: UserToken;
  }>(),
);

export const obtainRefreshTokenFailure = createAction(
  '[State Lib Auth Tokens API] Obtain refresh token Failure',
  props<{ error: unknown; customerKey: string }>(),
);

export const clearLoginSessionSuccess = createAction(
  '[State Lib Auth Tokens API] Clear Login Session Success',
);

export const clearLoginSessionFailure = createAction(
  '[State Lib Auth Tokens API] Clear Login Session Failure',
);

export const validateAccessTokenSuccess = createAction(
  '[State Lib Auth Tokens API] Validate access token Success',
);

export const validateAccessTokenFailure = createAction(
  '[State Lib Auth Tokens API] Validate access token Failure',
);
