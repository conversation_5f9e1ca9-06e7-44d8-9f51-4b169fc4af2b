import { UserRole, UserState } from '@fincloud/types/enums';
import { UsersState } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { memoize } from 'lodash-es';

export const selectUsersState = createFeatureSelector<UsersState>('users');

export const selectProfileImage = memoize((userId: string) =>
  createSelector(
    selectUsersState,
    (state) => state.profileImagesAsBase64?.[userId],
  ),
);

export const selectProfileImages = createSelector(
  selectUsersState,
  (state) => state.profileImagesAsBase64,
);

export const selectUsers = createSelector(
  selectUsersState,
  (state) => state.users,
);

export const selectUsersIds = createSelector(selectUsersState, (state) =>
  Object.values(state.users)
    .filter(({ attributes }) => attributes.profilePictureId)
    .map((user) => user.id),
);

export const selectActivePlatformManagers = createSelector(
  selectUsersState,
  (state) => {
    return Object.values(state.users)
      ?.filter(
        (user) =>
          user.userState === UserState.ACTIVE &&
          user.userRoles.some(
            (role) => role.name === UserRole.PLATFORM_MANAGER,
          ),
      )
      .map((user) => ({
        fullName: `${user.firstName} ${user.lastName}`,
        id: user.id,
      }));
  },
);

export const selectActivePlatformUsers = createSelector(
  selectUsersState,
  (state) => {
    return Object.values(state.users)
      ?.filter(
        (user) =>
          user?.userState === UserState.ACTIVE &&
          user.userRoles.some((role) => role.name === UserRole.PLATFORM_USER),
      )
      .map((user) => ({
        ...user,
        fullName: `${user.firstName} ${user.lastName}`,
      }));
  },
);

export const selectAllActiveUsers = createSelector(
  selectUsersState,
  (state) => {
    return Object.values(state.users)
      ?.filter((user) => user?.userState === UserState.ACTIVE)
      .map((user) => ({
        ...user,
      }));
  },
);
