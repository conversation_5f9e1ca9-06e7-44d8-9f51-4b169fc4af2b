import { Injectable } from '@angular/core';
import {
  RolePermissionControllerService,
  TokenControllerService,
  UserManagementControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import {
  DracoonManagementControllerService,
  NextFolderManagementControllerService,
} from '@fincloud/swagger-generator/document';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { catchError, filter, map, of, switchMap, tap } from 'rxjs';
import { setUser } from '../actions/user-page.actions';

import { Router } from '@angular/router';
import {
  LOCAL_STORAGE_USER_PERMISSIONS_KEY,
  LOGIN_REDIRECT_URL,
  SESSION_CUSTOMER_KEY,
} from '@fincloud/core/auth';
import { PostHogService } from '@fincloud/core/config';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { UserUpdatedLogService } from '@fincloud/core/user';
import {
  StateLibAuthTokensApiActions,
  StateLibAuthTokensPageActions,
} from '@fincloud/state/auth-tokens';
import { selectIsDemoEnvironment } from '@fincloud/state/environment';
import {
  StateLibLoginApiActions,
  StateLibLoginPageActions,
} from '@fincloud/state/login';
import { selectRouteCustomerKey } from '@fincloud/state/router';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import {
  PLATFORM_NOTIFICATION_DOCUMENT_LINK_GENERATION_SUBSCRIPTION,
  PLATFORM_NOTIFICATION_TODO_SYSTEM_UPDATE_DESTINATION,
  UPLOAD_DESTINATION,
} from '@fincloud/utils';
import { NgxPermissionsService } from 'ngx-permissions';
import { StateLibUserApiActions, StateLibUserPageActions } from '../actions';
import {
  selectUserCustomerKey,
  selectUserId,
} from '../selectors/user.selectors';

@Injectable()
export class StateLibUserEffects {
  loadUserInfoOnUserTokenLoaded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.setUserToken),
      switchMap((action) => {
        return this.userManagementControllerService
          .getUserById({
            userId: action.payload?.sub,
          })
          .pipe(
            map((user) => {
              return StateLibUserPageActions.setUser({ payload: user });
            }),
            catchError(() => of(StateLibNoopPageActions.noop())),
          );
      }),
    ),
  );

  onUserSet$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibUserPageActions.setUser),
        tap(({ payload }) => {
          this.regionalSettingsService.updateRegionalSettings(
            payload.attributes.userRegion,
          );
          this.regionalSettingsService.setLocale(payload.attributes.userRegion);
          this.userUpdatedLogService.updateUser(payload);
        }),
        switchMap(() => {
          return this.userUpdatedLogService.startRefreshCheckInterval();
        }),
      ),
    { dispatch: false },
  );

  clearLoginSession$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibAuthTokensPageActions.clearLoginSession,
        StateLibUserPageActions.setUser,
      ),
      switchMap(() =>
        this.tokenControllerService.clearLoginFlowSession().pipe(
          map(() => StateLibAuthTokensApiActions.clearLoginSessionSuccess()),
          catchError(() =>
            of(StateLibAuthTokensApiActions.clearLoginSessionFailure()),
          ),
        ),
      ),
    );
  });

  shouldRefreshToken$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.setUser),
      switchMap(({ payload }) =>
        this.userUpdatedLogService.shouldRefreshToken$.pipe(
          filter(Boolean),
          map(() =>
            StateLibAuthTokensPageActions.obtainRefreshToken({
              customerKey: payload.customerKey,
            }),
          ),
        ),
      ),
    ),
  );

  updateDemoDevUserOnTokenRefreshSuccess$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibAuthTokensApiActions.obtainRefreshTokenSuccess),
      concatLatestFrom(() => [
        this.store.select(selectUserId),
        this.store.select(selectIsDemoEnvironment),
      ]),
      filter(
        ([token, userId, isDemoDev]) =>
          isDemoDev && userId !== token.decodedToken.sub,
      ),
      map(([token]) =>
        StateLibUserPageActions.userIdChanged({
          userId: token.decodedToken.sub,
        }),
      ),
    );
  });

  triggerTokenTimeouts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.setUser),
      map(() => StateLibAuthTokensPageActions.clearLoginSession()),
    ),
  );

  connectPLatformNotificationSocket$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.setUser),
      switchMap(({ payload }) => {
        return of(
          this.socketService.initializeSocket(SocketType.PLATFORM_NOTIFICATION),
          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `new-tus-upload-user-${payload.id}`,
            UPLOAD_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          ),
          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `download-link-user-${payload.id}`,
            PLATFORM_NOTIFICATION_DOCUMENT_LINK_GENERATION_SUBSCRIPTION,
            SocketType.PLATFORM_NOTIFICATION,
          ),
          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `updated-successfully-user-${payload.id}`,
            PLATFORM_NOTIFICATION_TODO_SYSTEM_UPDATE_DESTINATION,
            SocketType.PLATFORM_NOTIFICATION,
          ),
        ).pipe(
          map(() =>
            StateLibUserApiActions.connectUploadFilesWebSocketSuccess(),
          ),
          catchError(() =>
            of(StateLibUserApiActions.connectUploadFilesWebSocketFailure()),
          ),
        );
      }),
    ),
  );

  deactivateWebSocketConnection$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibLoginPageActions.logout),
        tap(() => {
          this.socketService.deactivateSocket(SocketType.PLATFORM_NOTIFICATION);
        }),
      ),
    { dispatch: false },
  );

  resetPosthog$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibLoginPageActions.logout),
        tap(() => {
          this.postHogService.resetPostHog();
        }),
      ),
    { dispatch: false },
  );

  loadUserPermissions$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.setUser),
      switchMap((action) => {
        return this.rolePermissionControllerService
          .getPermissionsByRoles({
            roles: action.payload.userRoles.map((role) => role.name),
          })
          .pipe(
            map((permissions) => {
              return [
                ...permissions.map((permission) => permission.code),
                ...action.payload.userRoles.map((role) => role.name),
              ];
            }),
          );
      }),
      map((permissionsAndRoles) => {
        return StateLibUserApiActions.userPermissionsLoadedSuccess({
          payload: permissionsAndRoles,
        });
      }),
      catchError(() =>
        of(StateLibUserApiActions.userPermissionsLoadedFailure()),
      ),
    ),
  );

  setUserPermissions$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserApiActions.userPermissionsLoadedSuccess),
      concatLatestFrom(() => this.store.select(selectUserCustomerKey)),
      map(([permissions, customerKey]) => {
        this.setUserPermissionsInService(permissions.payload);
        const permissionsMap =
          this.localStorageService.retrieve(
            LOCAL_STORAGE_USER_PERMISSIONS_KEY,
          ) ?? {};
        permissionsMap[customerKey] = permissions.payload;

        this.localStorageService.store(
          LOCAL_STORAGE_USER_PERMISSIONS_KEY,
          permissionsMap,
        );
        return StateLibUserPageActions.setUserPermissions({
          payload: permissions.payload,
        });
      }),
    ),
  );

  SetUserPermissions$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibUserPageActions.setUserPermissions),
        concatLatestFrom(() => this.store.select(selectUserCustomerKey)),
        tap(([_, customerKey]) => {
          const storageRedirectUrl =
            this.localStorageService.retrieve(LOGIN_REDIRECT_URL);
          if (!storageRedirectUrl) {
            return;
          }

          this.sessionStorageService.store(SESSION_CUSTOMER_KEY, customerKey);
          const redirectUrl = storageRedirectUrl || customerKey;
          this.localStorageService.clear(LOGIN_REDIRECT_URL);
          const parts = redirectUrl.split('/').filter(Boolean);
          this.router.navigate(['/', ...parts]);
        }),
      ),
    { dispatch: false },
  );

  cleanup$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibLoginApiActions.logoutSuccess),
        tap(({ customerKey }) => {
          const permissionsMap = this.localStorageService.retrieve(
            LOCAL_STORAGE_USER_PERMISSIONS_KEY,
          );
          delete permissionsMap[customerKey];

          this.sessionStorageService.clear(SESSION_CUSTOMER_KEY);
          this.localStorageService.store(
            LOCAL_STORAGE_USER_PERMISSIONS_KEY,
            permissionsMap,
          );
          this.permissionsService.flushPermissions();
        }),
      );
    },
    { dispatch: false },
  );

  loadUserDracoonCredentials$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.getAppsCredentials),
      concatLatestFrom(() => this.store.select(selectUserCustomerKey)),
      switchMap(([_, customerKey]) =>
        this.dracoonManagementControllerService.getCredentialsForCustomer1({
          customerKey,
        }),
      ),
      map((dracoonCredentials) =>
        StateLibUserPageActions.setDracoonCredentials({
          payload: dracoonCredentials,
        }),
      ),
      catchError(() => of(StateLibNoopPageActions.noop())),
    ),
  );

  loadUserNextolderCredentials$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.getAppsCredentials),
      switchMap(() =>
        this.nextFolderManagementControllerService.getCredentialsForCustomer(),
      ),
      map((nextfolderCredentials) =>
        StateLibUserPageActions.setNextfolderCredentials({
          payload: nextfolderCredentials,
        }),
      ),
      catchError(() => of(StateLibNoopPageActions.noop())),
    ),
  );

  clearUserState$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibUserPageActions.clearUserState),
        concatLatestFrom(() => this.store.select(selectRouteCustomerKey)),
        tap(([_, customerKey]) => {
          this.router.navigate(['/', customerKey]);
        }),
      );
    },
    { dispatch: false },
  );

  reloadUserFromService$ = createEffect(() =>
    this.actions$.pipe(
      ofType(setUser),
      switchMap(() =>
        this.userUpdatedLogService.updateUser$.pipe(
          filter(Boolean),
          map(() => StateLibUserPageActions.reloadUser()),
        ),
      ),
    ),
  );

  reloadUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibUserPageActions.reloadUser,
        StateLibUserPageActions.userIdChanged,
      ),
      concatLatestFrom(() => this.store.select(selectUserId)),
      switchMap(([_, userId]) =>
        this.userManagementControllerService.getUserById({ userId }).pipe(
          map((user) =>
            StateLibUserApiActions.reloadUserSuccess({
              user,
            }),
          ),
          catchError(() => of(StateLibUserApiActions.reloadUserFailure())),
        ),
      ),
    ),
  );

  reloadUserOrganisations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserApiActions.reloadUserSuccess),
      switchMap(({ user }) =>
        this.userManagementControllerService
          .getListOfCustomersByEmail({
            email: user.username,
            includeDisabled: false,
          })
          .pipe(
            map((customerKeys) =>
              StateLibUserApiActions.reloadUserOrganisationsSuccess({
                customerKeys,
              }),
            ),
            catchError((error) =>
              of(
                StateLibUserApiActions.reloadUserOrganisationsFailure({
                  error,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  clearUserStateOnLogout$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibLoginApiActions.logoutSuccess),
      map(() => StateLibUserPageActions.clearUserState()),
    );
  });

  reloadUserSuccess$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibUserApiActions.reloadUserSuccess),
        tap((action) => {
          this.userUpdatedLogService.updateUser(action.user);
        }),
      );
    },
    { dispatch: false },
  );

  updatePostHogOnSetUserToken$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(StateLibUserPageActions.setUser),
        tap((user) =>
          this.postHogService.updatePostHogTracking(
            user.payload?.userPreferences.hasAcceptedAnalytics.accepted,
          ),
        ),
      );
    },
    { dispatch: false },
  );

  constructor(
    private store: Store,
    private actions$: Actions,
    private userManagementControllerService: UserManagementControllerService,
    private dracoonManagementControllerService: DracoonManagementControllerService,
    private rolePermissionControllerService: RolePermissionControllerService,
    private localStorageService: LocalStorageService,
    private nextFolderManagementControllerService: NextFolderManagementControllerService,
    private permissionsService: NgxPermissionsService,
    private sessionStorageService: SessionStorageService,
    private userUpdatedLogService: UserUpdatedLogService,
    private router: Router,
    private socketService: SocketService,
    private regionalSettingsService: RegionalSettingsService,
    private tokenControllerService: TokenControllerService,
    private postHogService: PostHogService,
  ) {}

  private setUserPermissionsInService(permissionsCodes: string[]) {
    const existingBusinessCasePermissions =
      Object.keys(this.permissionsService.getPermissions())?.filter(
        (permission) => permission.includes('BCP'),
      ) || [];

    this.permissionsService.loadPermissions([
      ...permissionsCodes,
      ...existingBusinessCasePermissions,
    ]);
  }
}
