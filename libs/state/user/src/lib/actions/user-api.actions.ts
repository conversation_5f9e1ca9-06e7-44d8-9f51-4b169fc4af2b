import { HttpErrorResponse } from '@angular/common/http';
import { User } from '@fincloud/swagger-generator/authorization-server';
import { createAction, props } from '@ngrx/store';
export const userPermissionsLoadedSuccess = createAction(
  '[User API] Successfully loaded user permissions Success',
  props<{ payload: string[] }>(),
);
export const userPermissionsLoadedFailure = createAction(
  '[User API] Load user permissions Failure',
);
export const reloadUserSuccess = createAction(
  '[User API] Reload user Success',
  props<{ user: User }>(),
);
export const reloadUserFailure = createAction('[User API] Reload user Failure');
export const reloadUserOrganisationsSuccess = createAction(
  '[User API] Reload user organisations Success',
  props<{ customerKeys: string[] }>(),
);
export const reloadUserOrganisationsFailure = createAction(
  '[User API] Reload user organisations Failure',
  props<{ error: HttpErrorResponse }>(),
);
export const connectUploadFilesWebSocketSuccess = createAction(
  '[USER API] Connect upload files web socket Success',
);
export const connectUploadFilesWebSocketFailure = createAction(
  '[USER API] Connect upload files web socket Failure',
);
export const connectDownloadFilesWebSocketSuccess = createAction(
  '[USER API] Connect download files web socket Success',
);
export const connectDownloadFilesWebSocketFailure = createAction(
  '[USER API] Connect upload files web socket Failure',
);
