import {
  AccessTokenStatus,
  UniqueInstanceStatusType,
} from '@fincloud/types/enums';
import { CustomerToken } from './customer-token';
import { UserToken } from './user-token';

export interface AuthToken {
  tokenRaw: CustomerToken;
  decodedToken: UserToken;
  loginTime: number;
  openedAngularInstances: Record<string, UniqueInstanceStatusType>;
  isRefreshTokenLoading: boolean;
  refreshRequestId: string;
  status: AccessTokenStatus;
}
