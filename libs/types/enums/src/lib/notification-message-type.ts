export enum NotificationMessageType {
  CHAT_MESSAGE_SENT = 'platformNotification.user.sent.message.for.case',
  CHAT_USER_TAGGED = 'platformNotification.user.tagged.in.message.for.case',
  CHAT_ARCHIVED_AUTOMATICALLY = 'platformNotification.chat.archived.automatically',
  CHAT_ARCHIVED_MANUALLY = 'platformNotification.user.manually.archived.chat',
  CHAT_REACTIVATED_BY_USER = 'platformNotification.chat.reactivated.by.user',
  CHAT_CREATED_AUTOMATICALLY = 'platformNotification.chat.created.automatically',
  CHAT_REACTIVATED_AUTOMATICALLY = 'platformNotification.chat.reactivated.automatically',
  USER_ASSIGNMENT_EXPIRY_AFTER_ONE = 'platformNotification.user.assignment.expiry.after.one',
  USER_ASSIGNMENT_EXPIRY_AFTER_THREE = 'platformNotification.user.assignment.expiry.after.three',
  USER_ASSIGNMENT_EXPIRY_BEFORE_FIVE = 'platformNotification.user.assignment.expiry.before.five',
  USER_ASSIGNMENT_REASSIGNED = 'platformNotification.user.assignment.reassigned',
  USER_ASSIGNMENT_CANCELLED = 'platformNotification.user.assignment.cancelled',
}
