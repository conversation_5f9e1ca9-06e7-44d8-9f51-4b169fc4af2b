import { EventEmitter, Injectable } from '@angular/core';
import {
  BehaviorSubject,
  Observable,
  ReplaySubject,
  Subject,
  distinctUntilChanged,
  map,
  merge,
  scan,
  shareReplay,
  startWith,
} from 'rxjs';
import { SidebarLayoutSection } from '../enums/sidebar-layout-section';
import { SidebarLayoutConfiguration } from '../models/sidebar-layout-configuration';

@Injectable({
  providedIn: 'root',
})
export class LayoutCommunicationService {
  private sidebarLayoutConfiguration$$ =
    new Subject<SidebarLayoutConfiguration>();
  sidebarLayoutConfiguration$: Observable<SidebarLayoutConfiguration> =
    this.sidebarLayoutConfiguration$$;

  private scrollToTopEvent$$ = new ReplaySubject<void>(1);
  scrollToTopEvent$: Observable<void> = this.scrollToTopEvent$$;

  private layoutChanged$$ = new Subject<void>();
  layoutChanged$: Observable<void> = this.layoutChanged$$;

  private layoutClosed$$ = new Subject<void>();
  layoutClosed$: Observable<void> = this.layoutClosed$$;

  private toggleRightSideOverlayPanel$$ = new Subject<void>();
  toggleRightSideOverlayPanel$: Observable<void> =
    this.toggleRightSideOverlayPanel$$;
  rightSideOverlayPanelOpened$: Observable<boolean> = merge(
    this.toggleRightSideOverlayPanel$,
    this.layoutClosed$.pipe(map(() => false)),
  ).pipe(
    scan(
      // toggleRightSideOverlayPanel$ void emission - previous value is inverted
      // layoutClosed$ boolean emission - value is always set to false
      (accumulator, value) => (value === false ? value : !accumulator),
      false,
    ),
    startWith(false),
    distinctUntilChanged(),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  private scrollY$$ = new BehaviorSubject(0);
  scrollY$: Observable<number> = this.scrollY$$;

  public lockScrollEvent = new EventEmitter<number>();

  emitSidebarLayoutConfiguration(config: SidebarLayoutConfiguration) {
    this.sidebarLayoutConfiguration$$.next(config);
  }

  clearRightOverlayPanelTemplate(): void {
    this.sidebarLayoutConfiguration$$.next({
      side: SidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL,
      template: null,
    });
  }

  emitScrollToTopEvent() {
    this.scrollToTopEvent$$.next();
  }

  lockScroll(duration = 100) {
    this.lockScrollEvent.emit(duration);
  }

  layoutChanged() {
    this.layoutChanged$$.next();
  }

  layoutClosed() {
    this.layoutClosed$$.next();
  }

  toggleRightSideOverlayPanel() {
    this.toggleRightSideOverlayPanel$$.next();
  }

  setLayoutScrollYPosition(position: number) {
    this.scrollY$$.next(position);
  }
}
