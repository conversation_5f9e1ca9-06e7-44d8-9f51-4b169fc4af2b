import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { DateService } from '@fincloud/core/date';
import { WindowRef } from '@fincloud/core/services';
import { DocumentControllerService } from '@fincloud/swagger-generator/document';
import {
  CommercialRegisterService,
  DocumentDownloadService,
} from '@fincloud/swagger-generator/handelsregister';
import { ChatType, CompanyDocumentType } from '@fincloud/types/enums';
import {
  ChatAttachment,
  CompanyDocument,
  CompanyUniqueIdentification,
} from '@fincloud/types/models';

@Injectable({
  providedIn: 'root',
})
export class FileService {
  constructor(
    private documentService: DocumentControllerService,
    private windowRef: WindowRef,
    private commercialRegisterService: CommercialRegisterService,
    private documentDownloadService: DocumentDownloadService,
    @Inject(DOCUMENT) private document: Document,
    private dateService: DateService,
  ) {}

  public downloadAttachment(attachment: ChatAttachment) {
    this.downloadDocument(attachment.documentId);
  }

  public downloadDocument(documentId: string, fieldLabel?: string) {
    this.documentService
      .retrieveBinary$Response({ documentId: documentId })
      .subscribe({
        next: (response) => {
          const header = response.headers.get('Content-Disposition');

          const [_, fileNameHeader] = header?.split(';') ?? [];
          const [__, fileName] = fileNameHeader?.split('=') ?? [];

          const blob = response.body as Blob;
          this.downloadBinary(
            blob,
            fieldLabel || fileName || 'Unbekannt',
            blob.type,
          );
        },
        error: (err) => console.error(err?.message),
      });
  }

  public downloadCommercialRegisterDocument(
    companyDocument: CompanyDocument,
    companyId: CompanyUniqueIdentification,
  ) {
    let request;
    if (companyDocument.documentType === CompanyDocumentType.EXTRACT) {
      request =
        this.commercialRegisterService.commercialRegisterControllerDownloadCurrentHardCopyPrintout$Response(
          { body: companyId },
        );
    } else if (
      companyDocument.documentType === CompanyDocumentType.CHRONOLOGICAL_EXTRACT
    ) {
      request =
        this.commercialRegisterService.commercialRegisterControllerDownloadChronologicalHardCopyPrintout$Response(
          {
            body: companyId,
          },
        );
    } else if (
      companyDocument.documentType === CompanyDocumentType.HISTORICAL_EXTRACT
    ) {
      request =
        this.commercialRegisterService.commercialRegisterControllerDownloadHistoricHardCopyPrintout$Response(
          { body: companyId },
        );
    } else {
      request =
        this.documentDownloadService.documentControllerDownloadCompanyDocument$Response(
          { id: companyDocument.id, body: companyId },
        );
    }

    request.subscribe({
      next: (response) => {
        const header = response.headers.get('Content-Disposition');
        const fileName = this.getCompanyDocumentFileName(header);
        const blob = response.body;
        this.downloadBinary(blob, fileName || 'Unbekannt', blob.type);
      },
      error: (err) => console.error(err?.message),
    });
  }

  public downloadBinary(
    blob: Blob,
    fileName: string,
    contentType = 'application/octet-stream',
  ) {
    const url = this.getObjectUrl(blob, contentType);
    const anchorElement = this.createHiddenLink(url, fileName);
    anchorElement.click();
    this.clearResources(url, anchorElement);
  }

  public downloadUrl(url: string, fileName: string) {
    const anchorElement = this.createHiddenLink(url, fileName);
    anchorElement.click();
    this.clearResources(url, anchorElement);
  }

  public blobToDataURL(blob: Blob): Promise<string> {
    return new Promise((res, rej) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const dataUrl =
          typeof reader.result === 'string'
            ? reader.result
            : Buffer.from(reader.result).toString();
        res(dataUrl);
      };
      reader.onerror = (err) => rej(err);
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Downloads a binary file from a specified URL and triggers a download in the browser.
   * This method does not use Angular's HTTP client to avoid header interception issues (such as with S3 requests).
   *
   * @param {string} url - The URL of the file to download.
   * @param {string} fileName - The name to use when saving the downloaded file.
   * @returns {Promise<void>} - A promise that resolves when the download is triggered.
   *
   * @throws {Error} If the fetch request fails or returns a non-OK response.
   *
   */
  public async downloadBinaryFromUrl(url: string, fileName: string) {
    try {
      // Fetch the binary data - Intentially not using angular http as it is intercepted with other headers (Auth) which do not work with S3.
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch the file: ${response.statusText}`);
      }

      // Convert the response to a blob
      const blob = await response.blob();

      this.downloadBinary(blob, fileName);
    } catch (error) {
      console.error(
        `Error downloading file with name: ${fileName} from ${url}`,
        (error as Error).message,
      );

      throw error;
    }
  }

  private createHiddenLink(url: string, fileName: string) {
    const anchorElement = this.document.createElement('a');

    anchorElement.style.display = 'none';
    anchorElement.href = url;
    anchorElement.download = fileName;
    this.document.body.appendChild(anchorElement);
    return anchorElement;
  }

  private clearResources(url: string, anchorElement: HTMLAnchorElement) {
    this.windowRef.URL.revokeObjectURL(url);
    this.document.body.removeChild(anchorElement);
  }

  getObjectUrl(blob: Blob, contentType: string) {
    return this.windowRef.URL.createObjectURL(
      new Blob([blob], { type: contentType }),
    );
  }

  getCompanyDocumentFileName(header: string): string {
    return decodeURI(header?.split('"')[1] ?? 'Unbekannt');
  }

  downloadFile(file: File) {
    const url = URL.createObjectURL(file);
    const link = document.createElement('a');

    link.href = url;
    link.download = file.name;

    document.body.appendChild(link);
    link.click();

    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  generateExportChatName({
    autoGeneratedBusinessCaseName,
    chatType,
    chatGroup,
    chatTitle,
  }: {
    autoGeneratedBusinessCaseName: string;
    chatType: string;
    chatGroup: string;
    chatTitle?: string;
  }) {
    return chatType === ChatType.ON_TOPIC
      ? `${autoGeneratedBusinessCaseName} ${chatGroup} ${chatTitle} chat export.pdf`
      : `${autoGeneratedBusinessCaseName} ${chatGroup} chat export.pdf`;
  }
}
