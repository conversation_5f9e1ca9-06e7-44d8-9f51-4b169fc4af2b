import { Injectable } from '@angular/core';
import { jwtDecode } from 'jwt-decode';

import { DateService } from '@fincloud/core/date';
import { AuthToken, UserToken } from '@fincloud/types/models';
import { LocalStorageService } from 'ngx-webstorage';

@Injectable({
  providedIn: 'root',
})
export class TokenManagementService {
  private tokenSuffix = '-token';

  constructor(
    private dateService: DateService,
    private localStorageService: LocalStorageService,
  ) {}

  /* ------------------ General ------------------ */

  getRenewalTimeoutPeriod(decodedToken: UserToken): number {
    return (
      this.dateService.getMillisecondsDifference(
        decodedToken.iat,
        decodedToken.exp,
      ) * 1000
    );
  }

  decodeToken(token: string): UserToken {
    if (token) {
      return jwtDecode<UserToken>(token);
    }

    return {} as UserToken;
  }

  storeToken(customerKey: string, token: AuthToken): void {
    this.localStorageService.store(`${customerKey}${this.tokenSuffix}`, token);
  }

  updateToken(customerKey: string, token: Partial<AuthToken>): void {
    const oldToken = this.localStorageService.retrieve(
      `${customerKey}${this.tokenSuffix}`,
    );

    if (oldToken) {
      this.localStorageService.store(`${customerKey}${this.tokenSuffix}`, {
        ...oldToken,
        ...token,
        openedAngularInstances: {
          ...oldToken.openedAngularInstances,
          ...token.openedAngularInstances,
        },
      } as AuthToken);

      return;
    }
  }

  updateRefreshRequestId(customerKey: string, id: string): void {
    const token = this.getToken(customerKey);

    if (token?.refreshRequestId === '') {
      this.updateToken(customerKey, {
        refreshRequestId: id,
      });
    }
  }

  getToken(customerKey: string): AuthToken {
    return this.localStorageService.retrieve(
      `${customerKey}${this.tokenSuffix}`,
    ) as AuthToken;
  }

  removeToken(customerKey: string) {
    if (
      this.localStorageService.retrieve(`${customerKey}${this.tokenSuffix}`)
    ) {
      this.localStorageService.clear(`${customerKey}${this.tokenSuffix}`);
    }
  }
}
