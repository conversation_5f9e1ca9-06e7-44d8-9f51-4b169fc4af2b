import { Directive, Input } from '@angular/core';
import { FinExpansionPanelComponent } from '@fincloud/ui/expansion-panel';

/**
 * Directive to be used in conjunction with FinExpansionPanelComponent
 * @param {boolean} appExpand - wheter to expanded the panel
 * */
@Directive({
  selector: '[appExpand]',
})
export class ExpandDirective {
  @Input() set appExpand(value: boolean) {
    if (value && this.expansionPanel) {
      this.expansionPanel.open();
      this.expansionPanel.expansionPanel?.open();
    }
  }

  constructor(private expansionPanel: FinExpansionPanelComponent) {}
}
