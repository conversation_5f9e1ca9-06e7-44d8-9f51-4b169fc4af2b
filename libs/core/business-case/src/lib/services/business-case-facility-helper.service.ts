/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { KeyValue } from '@angular/common';
import { Injectable } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { setFacilities } from '@fincloud/core-state/facilities';
import { MathUtils } from '@fincloud/core/math';
import { TEMPLATE_FIELD_KEYS } from '@fincloud/core/types';
import {
  FacilitiesControllerService,
  Facility,
  FacilityField,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseParticipantCustomer,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import { AppState, FacilityViewModel } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import { Observable, of, tap } from 'rxjs';
import { FinancedCase } from '../models/financed-case';

@Injectable({
  providedIn: 'root',
})
export class BusinessCaseFacilityHelperService {
  constructor(
    private formBuilder: UntypedFormBuilder,
    private facilitiesControllerService: FacilitiesControllerService,
    private store: Store<AppState>,
  ) {}

  getFinancingVolume(
    businessCase: FinancedCase | ExchangeBusinessCase,
  ): string {
    if (!businessCase) {
      return '';
    }

    const value = businessCase.financingVolumeValue;
    if (value) {
      return MathUtils.getSafeValue(value).toString();
    }

    const facilities =
      businessCase?.structuredFinancingConfiguration?.facilities || [];

    if (!facilities.length) {
      if (businessCase?.finStructureCommonFields?.totalInvestmentAmount) {
        return (
          businessCase.finStructureCommonFields.totalInvestmentAmount?.value?.toString() ||
          ''
        );
      }

      return businessCase.information?.financingVolume?.value?.toString() || '';
    }

    return (
      facilities
        .map((f) => f.facilityFields)
        ?.flat()
        .find((ff) => ff.key === TEMPLATE_FIELD_KEYS.FinancingVolume)?.value ??
      ''
    );
  }

  getFacilitiesFormArray(facilities: FacilityViewModel[]) {
    return this.formBuilder.array(
      facilities.map((facility: Facility) => {
        return this.formBuilder.group(<KeyValue<string, number>>{
          key: facility?.name,
          value: 0,
        });
      }),
    );
  }

  calculateFacilitiesByOwnParticipationAmount(
    ownParticipationAmount: number,
    facilities: Facility[],
  ): Array<KeyValue<string, number>> {
    const facilityPercentageDistributions =
      this.getFacilityPercentDistributions(facilities);

    const distributedAmounts = MathUtils.distributeProportionally(
      ownParticipationAmount,
      facilityPercentageDistributions.map((p) => p.percentageAmount),
    ).map((v, index) => {
      return {
        key: facilityPercentageDistributions[index].name,
        value: v,
      };
    });

    return distributedAmounts;
  }

  getTotalFacilitiesAmount(facilities: Facility[]) {
    return facilities.reduce((acc1, curr1) => {
      const sum = curr1.facilityFields
        .filter((field: FacilityField) => field?.eligibleForCalculation)
        .reduce((acc, curr) => {
          return acc + MathUtils.getSafeValue(curr.value);
        }, 0);
      return acc1 + sum;
    }, 0);
  }

  getFacilityPercentDistributions(facilities: Facility[]) {
    const totalFacilitiesAmount = this.getTotalFacilitiesAmount(facilities);

    return facilities.map((f) => {
      return {
        name: f.name,
        percentageAmount:
          totalFacilitiesAmount === 0
            ? 0
            : f.facilityFields
                .filter((field: FacilityField) => field?.eligibleForCalculation)
                .reduce((acc, curr) => {
                  return (
                    acc +
                    (100 * MathUtils.getSafeValue(curr?.value)) /
                      totalFacilitiesAmount
                  );
                }, 0),
      };
    });
  }

  distributeFacilitiesBasedOnTotalAmount(
    totalAmount: number,
    enabledFacilities: Facility[],
    facilitiesForm: UntypedFormGroup,
  ) {
    const distributedAmounts = this.calculateFacilitiesByOwnParticipationAmount(
      totalAmount,
      enabledFacilities,
    );
    facilitiesForm.controls['facilities'].setValue(distributedAmounts);
  }

  getRangeValidationsDetails(
    amount: number,
    minParticipationAmount: number,
    maxParticipationAmount: number,
    formTouched: boolean,
    participant?: BusinessCaseParticipantCustomer,
  ) {
    if (participant?.lead) {
      return {
        isOverMax: false,
        isUnderMin: false,
        outOfRangeContribution: false,
        inRangeContribution: true,
      };
    }

    const isOverMax = maxParticipationAmount && amount > maxParticipationAmount;
    const isUnderMin =
      minParticipationAmount && amount < minParticipationAmount;
    const outOfRangeContribution = isUnderMin || isOverMax;

    return {
      isOverMax,
      isUnderMin,
      outOfRangeContribution,
      inRangeContribution: !outOfRangeContribution,
      shouldShowState: amount > 0 || formTouched,
    };
  }

  handleEditValueRequest(
    businessCaseId: string,
    value: string,
    field: FacilityField,
    facilityName: string,
  ): Observable<unknown> {
    if (value == null || isEqual(value, field?.value)) {
      return of();
    }
    return this.facilitiesControllerService
      .addConfiguraitonFacilityFieldValue({
        businessCaseId,
        fieldKey: field.key,
        name: facilityName,
        value,
      })
      .pipe(
        tap((businessCase) => {
          this.store.dispatch(
            setFacilities({
              payload: {
                facilities:
                  businessCase.structuredFinancingConfiguration.facilities,
              },
            }),
          );
        }),
      );
  }
}
