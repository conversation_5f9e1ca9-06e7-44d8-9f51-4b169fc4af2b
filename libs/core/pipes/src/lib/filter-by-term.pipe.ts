import { Pipe, PipeTransform } from '@angular/core';
import { isArray, isString } from 'lodash-es';

@Pipe({
  name: 'filterByTerm',
})
export class FilterByTermPipe<T extends string> implements PipeTransform {
  transform(
    collection: Record<string, T>[],
    key: string,
    term: string | null,
  ): Record<string, T>[] {
    if (!isString(term) || !term || !isArray(collection)) {
      return collection;
    }

    return collection.filter((item) =>
      item[key].toLocaleLowerCase().includes(term.toLocaleLowerCase()),
    );
  }
}
