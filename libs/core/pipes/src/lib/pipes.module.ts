import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { AsTypePipe } from './as-type.pipe';
import { BoldSubstringPipe } from './bold-substring.pipe';
import { CastPipe } from './cast.pipe';
import { CountAllMirroredFieldsForGroupPipe } from './count-all-mirrored-fields-for-group.pipe';
import { DisableRoutePipe } from './disable-route.pipe';
import { ExecuteFuncPipe } from './execute-func.pipe';
import { FileSizePipe } from './file-size.pipe';
import { FilterByTermWithCollectionPipe } from './filter-by-term-with-collection.pipe';
import { FilterByTermPipe } from './filter-by-term.pipe';
import { FindPipe } from './find.pipe';
import { HrefFormatterPipe } from './href-formatter.pipe';
import { IncludesPipe } from './includes.pipe';
import { InitialsPipe } from './initials.pipe';
import { IsFalsyPipe } from './is-falsy.pipe';
import { MatchingElementsCountPipe } from './matching-elements-count.pipe';
import { PartOfFullNamePipe } from './part-of-full-name.pipe';
import { RemoveTrailingZerosPipe } from './remove-trailing-zeros.pipe';
import { SuffixPipe } from './suffix.pipe';
import { TopicPipe } from './topic.pipe';
import { TrimPipe } from './trim.pipe';
import { TruncatePipe } from './truncate.pipe';

@NgModule({
  declarations: [
    AsTypePipe,
    BoldSubstringPipe,
    InitialsPipe,
    IsFalsyPipe,
    TopicPipe,
    TrimPipe,
    TruncatePipe,
    CastPipe,
    ExecuteFuncPipe,
    IncludesPipe,
    SuffixPipe,
    FindPipe,
    RemoveTrailingZerosPipe,
    MatchingElementsCountPipe,
    CountAllMirroredFieldsForGroupPipe,
    HrefFormatterPipe,
    FilterByTermPipe,
    FilterByTermWithCollectionPipe,
    PartOfFullNamePipe,
    DisableRoutePipe,
    FileSizePipe,
  ],
  imports: [CommonModule],
  exports: [
    AsTypePipe,
    BoldSubstringPipe,
    InitialsPipe,
    IsFalsyPipe,
    TopicPipe,
    TrimPipe,
    TruncatePipe,
    CastPipe,
    ExecuteFuncPipe,
    IncludesPipe,
    SuffixPipe,
    FindPipe,
    RemoveTrailingZerosPipe,
    MatchingElementsCountPipe,
    CountAllMirroredFieldsForGroupPipe,
    HrefFormatterPipe,
    PartOfFullNamePipe,
    FilterByTermPipe,
    FilterByTermWithCollectionPipe,
    DisableRoutePipe,
    FileSizePipe,
  ],
})
export class NsCorePipesModule {}
