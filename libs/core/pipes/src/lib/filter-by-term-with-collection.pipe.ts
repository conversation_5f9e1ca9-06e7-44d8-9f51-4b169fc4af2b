import { Pipe, PipeTransform } from '@angular/core';
import { isArray } from 'lodash-es';

@Pipe({
  name: 'filterByTermWithCollection',
})
export class FilterByTermWithCollectionPipe<T extends string>
  implements PipeTransform
{
  transform(
    collection: Record<string, T>[],
    key: string,
    term: string | null,
  ): Record<string, T>[] {
    if (!term || !isArray(collection)) {
      return collection;
    }
    const lowerTerm = term.toLocaleLowerCase();
    if (
      collection.some((item) => item[key].toLocaleLowerCase() === lowerTerm)
    ) {
      // Selected item from collection , we still to show the entire collection
      return collection;
    }

    return collection.filter((item) =>
      item[key].toLocaleLowerCase().includes(lowerTerm),
    );
  }
}
