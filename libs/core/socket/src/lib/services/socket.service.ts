import { inject, Injectable } from '@angular/core';
import { selectUserCustomerKey } from '@fincloud/core-state/user';
import { TokenManagementService } from '@fincloud/core/auth';
import { MonitoringService } from '@fincloud/core/services';
import { Store } from '@ngrx/store';
import { SocketOptions } from 'dgram';
import { noop } from 'lodash-es';
import { EMPTY, map, Observable, Subject, take } from 'rxjs';
import { io, ManagerOptions, Socket } from 'socket.io-client';
import { SocketType } from '../enums/socket-type';
import { ManagedSocket } from '../models/managed-socket';
import { SocketConfig } from '../models/socket-config';
import { SocketQueryParams } from '../models/socket-query-params';
import { SOCKET_CONFIG_MAP } from '../utils/socket-config-map';

@Injectable({
  providedIn: 'root',
})
export class SocketService {
  private sockets = new Map<SocketType, ManagedSocket>();
  private receiveMessagesSubject$$ = new Subject<unknown>();
  receiveMessages$ = this.receiveMessagesSubject$$.asObservable();

  private receiveMessagesByDestination = new Map<string, Subject<unknown>>();
  private manuallyDisconnected = new Set<SocketType>();
  private socketConfigs = new Map<SocketType, SocketConfig>();
  private socketQueryParams = new Map<SocketType, SocketQueryParams>();

  receiveMessageDestination: string | string[];
  sendMessageDestination: string;

  protected monitoringService = inject(MonitoringService);
  protected tokenManagementService: TokenManagementService = inject(
    TokenManagementService,
  );
  protected store: Store = inject(Store);

  initializeSocket(
    socketType: SocketType,
    socketQueryParams?: SocketQueryParams,
  ) {
    const config = SOCKET_CONFIG_MAP[socketType];
    this.configureSocketConnection(socketQueryParams, socketType, config);
  }

  private configureSocketConnection(
    queryParams: SocketQueryParams,
    socketType: SocketType,
    config: SocketConfig,
  ): void {
    const existingSocket = this.sockets.get(socketType);
    if (existingSocket) {
      this.deactivateSocket(socketType, true);
    }

    this.socketConfigs.set(socketType, config);
    this.socketQueryParams.set(socketType, queryParams);

    this.createSocket(socketType, queryParams, config).subscribe({
      next: (socket) => {
        const managedSocket = socket as ManagedSocket;

        managedSocket.rooms = {};

        this.sockets.set(socketType, managedSocket);
      },
      error: (err) => {
        console.error('Socket creation failed:', JSON.stringify(err));
        this.monitoringService.logWebSocketErrorOnConnection(config.context);
      },
    });
  }

  private createSocket(
    socketType: SocketType,
    queryParams: SocketQueryParams,
    config: SocketConfig,
  ): Observable<Socket> {
    return this.store.select(selectUserCustomerKey).pipe(
      take(1),
      map((customerKey: string) => {
        const token =
          this.tokenManagementService.getToken(customerKey).tokenRaw
            .accessToken;

        const options: Partial<ManagerOptions & SocketOptions> = {
          path: `/${config.context}`,
          query: {
            ...queryParams,
            token,
          },
          transports: ['websocket', 'polling'],
        };

        const socket = io(
          `${config.socketUrl}${config.namespace}`,
          options,
        ) as ManagedSocket;
        socket.on('connect', () => {
          Object.entries(socket.rooms).forEach(([room, _]) => {
            this.joinRoomEmit(socket, room);
          });

          this.sockets.set(socketType, socket);
        });

        socket.on('connect_error', (error) => {
          this.reconnectOnError(socket, customerKey);

          this.monitoringService.logWebSocketErrorOnConnection(config.context);
          console.error(
            `Connection error on ${config.namespace}, trying connect with new token`,
            JSON.stringify(error),
          );
        });

        socket.on('disconnect', (reason) => {
          console.warn(`Disconnected from ${config.namespace}: ${reason}`);

          if (this.manuallyDisconnected.has(socketType)) {
            this.manuallyDisconnected.delete(socketType);
            return;
          }

          this.monitoringService.logWebsocketsError(
            new Error(
              `Unexpected disconnect from ${config.namespace}: ${reason}`,
            ),
          );
        });

        socket.on('reconnect_attempt', () => {
          // we so far are not hitting this event
          this.reconnectOnError(socket, customerKey);
        });

        return socket;
      }),
    );
  }

  private receiveMessages(destination: string, socketType: SocketType) {
    if (!this.receiveMessagesByDestination.has(destination)) {
      this.receiveMessagesByDestination.set(
        destination,
        new Subject<unknown>(),
      );
    }

    this.sockets.get(socketType)?.on(destination, (message: unknown) => {
      this.receiveMessagesByDestination.get(destination)?.next(message);
      this.receiveMessagesSubject$$.next(message);
    });
  }

  sendMessage(
    message: unknown,
    sendMessageDestination: string,
    socketType: SocketType,
  ) {
    const socket = this.sockets.get(socketType);
    try {
      socket?.emit(sendMessageDestination, message);
    } catch (error) {
      console.error('Could not emit message', JSON.stringify(error));
      this.monitoringService.logWebsocketsError(error);
    }
  }

  getMessagesByDestination$<T>(
    destination: string,
    socketType: SocketType,
  ): Observable<T> {
    const socket = this.sockets.get(socketType);
    if (!socket) {
      console.error('Socket was not found', socketType);
      return EMPTY;
    }

    return new Observable((observer) => {
      const handler = (message: T) => {
        observer.next(message);
      };
      socket.on(destination, handler);
      return () => {
        socket.off(destination, handler);
      };
    });
  }

  joinRoomAndReceiveMessagesByDestination(
    roomParam: string,
    destination: string,
    socketType: SocketType,
  ) {
    const socket = this.sockets.get(socketType);
    if (!socket) {
      return;
    }

    this.joinRoomEmit(socket, roomParam);
    this.receiveMessages(destination, socketType);
  }

  private joinRoomEmit(socket: ManagedSocket, roomParam: string): void {
    if (socket.rooms[roomParam]?.joined) {
      return;
    }

    socket.emit('join-room', roomParam, noop);
    // Mark as joined and ensure keyRoom is set correctly
    socket.rooms[roomParam] = { joined: true };
  }

  deactivateSocket(socketType: SocketType, manual = true) {
    const socket = this.sockets.get(socketType);
    if (manual) {
      this.manuallyDisconnected.add(socketType);
    }

    if (socket) {
      socket.disconnect();
    }

    this.sockets.delete(socketType);
  }

  checkIsConnected(socketType: SocketType): boolean {
    return this.sockets.get(socketType)?.connected;
  }

  leaveRoom(leaveRoomParams: string, socketType: SocketType) {
    const socket = this.sockets.get(socketType);
    if (!socket) {
      return;
    }
    if (socket.rooms[leaveRoomParams]) {
      delete socket.rooms[leaveRoomParams];
      socket.emit('leave-room', leaveRoomParams);
    }
  }

  private reconnectOnError(socket: ManagedSocket, customerKey: string) {
    const newToken =
      this.tokenManagementService.getToken(customerKey)?.tokenRaw?.accessToken;

    Object.values(socket.rooms)?.forEach((roomInfo) => {
      roomInfo.joined = false;
    });

    socket.io.opts.query.token = newToken;
    socket.connect();
  }
}
