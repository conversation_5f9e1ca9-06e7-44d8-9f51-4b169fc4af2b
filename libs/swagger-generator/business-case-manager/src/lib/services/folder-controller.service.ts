/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { ApiConfiguration } from '../api-configuration';
import { BaseService } from '../base-service';
import { StrictHttpResponse } from '../strict-http-response';

import {
  AddFieldToFolder$Params,
  addFieldToFolder,
} from '../fn/folder-controller/add-field-to-folder';
import {
  CompareFolderStructureForBusinessCase$Params,
  compareFolderStructureForBusinessCase,
} from '../fn/folder-controller/compare-folder-structure-for-business-case';
import {
  CreateFolder$Params,
  createFolder,
} from '../fn/folder-controller/create-folder';
import {
  DeleteFolder$Params,
  deleteFolder,
} from '../fn/folder-controller/delete-folder';
import {
  GetFolder$Params,
  getFolder,
} from '../fn/folder-controller/get-folder';
import {
  MoveFieldBetweenFolders$Params,
  moveFieldBetweenFolders,
} from '../fn/folder-controller/move-field-between-folders';
import {
  MoveFolder$Params,
  moveFolder,
} from '../fn/folder-controller/move-folder';
import {
  UpdateFolder$Params,
  updateFolder,
} from '../fn/folder-controller/update-folder';
import { BusinessCase } from '../models/business-case';
import { Folder } from '../models/folder';

@Injectable({ providedIn: 'root' })
export class FolderControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `updateFolder()` */
  static readonly UpdateFolderPath = '/business-case/{businessCaseId}/folder';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateFolder()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  updateFolder$Response(
    params: UpdateFolder$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<BusinessCase>> {
    return updateFolder(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateFolder$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  updateFolder(
    params: UpdateFolder$Params,
    context?: HttpContext,
  ): Observable<BusinessCase> {
    return this.updateFolder$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body),
    );
  }

  /** Path part for operation `createFolder()` */
  static readonly CreateFolderPath = '/business-case/{businessCaseId}/folder';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `createFolder()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createFolder$Response(
    params: CreateFolder$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<BusinessCase>> {
    return createFolder(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `createFolder$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createFolder(
    params: CreateFolder$Params,
    context?: HttpContext,
  ): Observable<BusinessCase> {
    return this.createFolder$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body),
    );
  }

  /** Path part for operation `deleteFolder()` */
  static readonly DeleteFolderPath = '/business-case/{businessCaseId}/folder';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteFolder()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteFolder$Response(
    params: DeleteFolder$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<BusinessCase>> {
    return deleteFolder(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteFolder$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteFolder(
    params: DeleteFolder$Params,
    context?: HttpContext,
  ): Observable<BusinessCase> {
    return this.deleteFolder$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body),
    );
  }

  /** Path part for operation `moveFolder()` */
  static readonly MoveFolderPath =
    '/business-case/{businessCaseId}/folder/move-folder';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `moveFolder()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  moveFolder$Response(
    params: MoveFolder$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<BusinessCase>> {
    return moveFolder(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `moveFolder$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  moveFolder(
    params: MoveFolder$Params,
    context?: HttpContext,
  ): Observable<BusinessCase> {
    return this.moveFolder$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body),
    );
  }

  /** Path part for operation `moveFieldBetweenFolders()` */
  static readonly MoveFieldBetweenFoldersPath =
    '/business-case/{businessCaseId}/folder/move-field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `moveFieldBetweenFolders()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  moveFieldBetweenFolders$Response(
    params: MoveFieldBetweenFolders$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<BusinessCase>> {
    return moveFieldBetweenFolders(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `moveFieldBetweenFolders$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  moveFieldBetweenFolders(
    params: MoveFieldBetweenFolders$Params,
    context?: HttpContext,
  ): Observable<BusinessCase> {
    return this.moveFieldBetweenFolders$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body),
    );
  }

  /** Path part for operation `compareFolderStructureForBusinessCase()` */
  static readonly CompareFolderStructureForBusinessCasePath =
    '/business-case/{businessCaseId}/folder/compare-structure';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `compareFolderStructureForBusinessCase()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  compareFolderStructureForBusinessCase$Response(
    params: CompareFolderStructureForBusinessCase$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<void>> {
    return compareFolderStructureForBusinessCase(
      this.http,
      this.rootUrl,
      params,
      context,
    );
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `compareFolderStructureForBusinessCase$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  compareFolderStructureForBusinessCase(
    params: CompareFolderStructureForBusinessCase$Params,
    context?: HttpContext,
  ): Observable<void> {
    return this.compareFolderStructureForBusinessCase$Response(
      params,
      context,
    ).pipe(map((r: StrictHttpResponse<void>): void => r.body));
  }

  /** Path part for operation `addFieldToFolder()` */
  static readonly AddFieldToFolderPath =
    '/business-case/{businessCaseId}/folder/add-field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addFieldToFolder()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addFieldToFolder$Response(
    params: AddFieldToFolder$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<BusinessCase>> {
    return addFieldToFolder(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addFieldToFolder$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addFieldToFolder(
    params: AddFieldToFolder$Params,
    context?: HttpContext,
  ): Observable<BusinessCase> {
    return this.addFieldToFolder$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body),
    );
  }

  /** Path part for operation `getFolder()` */
  static readonly GetFolderPath =
    '/business-case/{businessCaseId}/folder/{folderId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getFolder()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFolder$Response(
    params: GetFolder$Params,
    context?: HttpContext,
  ): Observable<StrictHttpResponse<Folder>> {
    return getFolder(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getFolder$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getFolder(
    params: GetFolder$Params,
    context?: HttpContext,
  ): Observable<Folder> {
    return this.getFolder$Response(params, context).pipe(
      map((r: StrictHttpResponse<Folder>): Folder => r.body),
    );
  }
}
