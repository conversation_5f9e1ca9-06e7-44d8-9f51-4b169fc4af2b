/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { ParticipantUserDto } from '../../models/participant-user-dto';

export interface GetUsersParticipantsInBusinessCase$Params {
  businessCaseId: string;
}

export function getUsersParticipantsInBusinessCase(http: HttpClient, rootUrl: string, params: GetUsersParticipantsInBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<ParticipantUserDto>>> {
  const rb = new RequestBuilder(rootUrl, getUsersParticipantsInBusinessCase.PATH, 'get');
  if (params) {
    rb.path('businessCaseId', params.businessCaseId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<Array<ParticipantUserDto>>;
    })
  );
}

getUsersParticipantsInBusinessCase.PATH = '/business-case/participant-users/{businessCaseId}';
