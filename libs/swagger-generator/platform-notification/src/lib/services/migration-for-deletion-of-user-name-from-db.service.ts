/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteInitiatorUserFullNameFromDbRecords } from '../fn/migration-for-deletion-of-user-name-from-db/delete-initiator-user-full-name-from-db-records';
import { DeleteInitiatorUserFullNameFromDbRecords$Params } from '../fn/migration-for-deletion-of-user-name-from-db/delete-initiator-user-full-name-from-db-records';

@Injectable({ providedIn: 'root' })
export class MigrationForDeletionOfUserNameFromDbService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `deleteInitiatorUserFullNameFromDbRecords()` */
  static readonly DeleteInitiatorUserFullNameFromDbRecordsPath = '/migration';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteInitiatorUserFullNameFromDbRecords()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteInitiatorUserFullNameFromDbRecords$Response(params: DeleteInitiatorUserFullNameFromDbRecords$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteInitiatorUserFullNameFromDbRecords(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteInitiatorUserFullNameFromDbRecords$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteInitiatorUserFullNameFromDbRecords(params: DeleteInitiatorUserFullNameFromDbRecords$Params, context?: HttpContext): Observable<void> {
    return this.deleteInitiatorUserFullNameFromDbRecords$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
