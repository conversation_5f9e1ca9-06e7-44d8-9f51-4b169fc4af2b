/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteNotificationsForUsers } from '../fn/migration-for-deletion-of-notifications/delete-notifications-for-users';
import { DeleteNotificationsForUsers$Params } from '../fn/migration-for-deletion-of-notifications/delete-notifications-for-users';

@Injectable({ providedIn: 'root' })
export class MigrationForDeletionOfNotificationsService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `deleteNotificationsForUsers()` */
  static readonly DeleteNotificationsForUsersPath = '/migration/delete-notifications';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteNotificationsForUsers()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteNotificationsForUsers$Response(params: DeleteNotificationsForUsers$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteNotificationsForUsers(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteNotificationsForUsers$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteNotificationsForUsers(params: DeleteNotificationsForUsers$Params, context?: HttpContext): Observable<void> {
    return this.deleteNotificationsForUsers$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
