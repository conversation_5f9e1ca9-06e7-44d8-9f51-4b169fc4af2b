/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelNotification } from '../../models/collection-model-entity-model-notification';

export interface ExecuteSearchNotificationGet3$Params {
  userId?: string;
  isRead?: boolean;
}

export function executeSearchNotificationGet3(http: HttpClient, rootUrl: string, params?: ExecuteSearchNotificationGet3$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelNotification>> {
  const rb = new RequestBuilder(rootUrl, executeSearchNotificationGet3.PATH, 'get');
  if (params) {
    rb.query('userId', params.userId, {});
    rb.query('isRead', params.isRead, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelNotification>;
    })
  );
}

executeSearchNotificationGet3.PATH = '/notifications/search/findByReceiverUserIdAndIsRead';
