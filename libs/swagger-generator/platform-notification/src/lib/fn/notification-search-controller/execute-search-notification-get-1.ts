/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EntityModelNotification } from '../../models/entity-model-notification';

export interface ExecuteSearchNotificationGet1$Params {
  id?: string;
  receiverUserId?: string;
}

export function executeSearchNotificationGet1(http: HttpClient, rootUrl: string, params?: ExecuteSearchNotificationGet1$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelNotification>> {
  const rb = new RequestBuilder(rootUrl, executeSearchNotificationGet1.PATH, 'get');
  if (params) {
    rb.query('id', params.id, {});
    rb.query('receiverUserId', params.receiverUserId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EntityModelNotification>;
    })
  );
}

executeSearchNotificationGet1.PATH = '/notifications/search/findByIdAndReceiverUserId';
