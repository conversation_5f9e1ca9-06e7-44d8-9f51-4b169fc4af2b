/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelNotification } from '../../models/collection-model-entity-model-notification';

export interface ExecuteSearchNotificationGet$Params {
  userId?: string;
}

export function executeSearchNotificationGet(http: HttpClient, rootUrl: string, params?: ExecuteSearchNotificationGet$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelNotification>> {
  const rb = new RequestBuilder(rootUrl, executeSearchNotificationGet.PATH, 'get');
  if (params) {
    rb.query('userId', params.userId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelNotification>;
    })
  );
}

executeSearchNotificationGet.PATH = '/notifications/search/findAllByInitiatorUserId';
