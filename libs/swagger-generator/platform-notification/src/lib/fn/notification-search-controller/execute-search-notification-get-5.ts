/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelNotification } from '../../models/collection-model-entity-model-notification';

export interface ExecuteSearchNotificationGet5$Params {
  userId?: string;
  isSeen?: boolean;
}

export function executeSearchNotificationGet5(http: HttpClient, rootUrl: string, params?: ExecuteSearchNotificationGet5$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelNotification>> {
  const rb = new RequestBuilder(rootUrl, executeSearchNotificationGet5.PATH, 'get');
  if (params) {
    rb.query('userId', params.userId, {});
    rb.query('isSeen', params.isSeen, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelNotification>;
    })
  );
}

executeSearchNotificationGet5.PATH = '/notifications/search/findByReceiverUserIdAndIsSeen';
