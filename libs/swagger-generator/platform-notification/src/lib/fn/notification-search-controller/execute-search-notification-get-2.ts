/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { PagedModelEntityModelNotification } from '../../models/paged-model-entity-model-notification';

export interface ExecuteSearchNotificationGet2$Params {
  receiverUserId?: string;
  hidden?: boolean;
  excluded?: Array<'CHAT_RELATED' | 'DATA_ROOM' | 'CADR' | 'BUSINESS_CASE' | 'UPLOAD_DOCUMENT' | 'USER_ASSIGNMENT'>;
  timestamp?: string;

/**
 * Zero-based page index (0..N)
 */
  page?: number;

/**
 * The size of the page to be returned
 */
  size?: number;

/**
 * Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
 */
  sort?: Array<string>;
}

export function executeSearchNotificationGet2(http: HttpClient, rootUrl: string, params?: ExecuteSearchNotificationGet2$Params, context?: HttpContext): Observable<StrictHttpResponse<PagedModelEntityModelNotification>> {
  const rb = new RequestBuilder(rootUrl, executeSearchNotificationGet2.PATH, 'get');
  if (params) {
    rb.query('receiverUserId', params.receiverUserId, {});
    rb.query('hidden', params.hidden, {});
    rb.query('excluded', params.excluded, {});
    rb.query('timestamp', params.timestamp, {});
    rb.query('page', params.page, {});
    rb.query('size', params.size, {});
    rb.query('sort', params.sort, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<PagedModelEntityModelNotification>;
    })
  );
}

executeSearchNotificationGet2.PATH = '/notifications/search/findByReceiverUserIdAndIsHiddenAndTypeNotInAndTimestampGreaterThanEqualOrderByTimestampDesc';
