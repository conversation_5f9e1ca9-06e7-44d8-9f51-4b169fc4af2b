/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { PageNotification } from '../../models/page-notification';

export interface GetAllUnreadNotificationsForUser$Params {
  userId: string;
  hidden?: boolean;
  page?: number;
  size?: number;
}

export function getAllUnreadNotificationsForUser(http: HttpClient, rootUrl: string, params: GetAllUnreadNotificationsForUser$Params, context?: HttpContext): Observable<StrictHttpResponse<PageNotification>> {
  const rb = new RequestBuilder(rootUrl, getAllUnreadNotificationsForUser.PATH, 'get');
  if (params) {
    rb.path('userId', params.userId, {});
    rb.query('hidden', params.hidden, {});
    rb.query('page', params.page, {});
    rb.query('size', params.size, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<PageNotification>;
    })
  );
}

getAllUnreadNotificationsForUser.PATH = '/notification/unread/{userId}';
