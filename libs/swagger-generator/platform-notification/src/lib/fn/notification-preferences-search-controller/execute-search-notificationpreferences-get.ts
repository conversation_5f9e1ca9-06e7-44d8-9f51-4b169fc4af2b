/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EntityModelNotificationPreferences } from '../../models/entity-model-notification-preferences';

export interface ExecuteSearchNotificationpreferencesGet$Params {
  userId?: string;
}

export function executeSearchNotificationpreferencesGet(http: HttpClient, rootUrl: string, params?: ExecuteSearchNotificationpreferencesGet$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelNotificationPreferences>> {
  const rb = new RequestBuilder(rootUrl, executeSearchNotificationpreferencesGet.PATH, 'get');
  if (params) {
    rb.query('userId', params.userId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EntityModelNotificationPreferences>;
    })
  );
}

executeSearchNotificationpreferencesGet.PATH = '/notificationPreferenceses/search/findByUserId';
