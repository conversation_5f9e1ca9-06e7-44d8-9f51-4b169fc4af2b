/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EntityModelNotification } from '../../models/entity-model-notification';

export interface GetItemResourceNotificationGet$Params {
  id: string;
}

export function getItemResourceNotificationGet(http: HttpClient, rootUrl: string, params: GetItemResourceNotificationGet$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelNotification>> {
  const rb = new RequestBuilder(rootUrl, getItemResourceNotificationGet.PATH, 'get');
  if (params) {
    rb.path('id', params.id, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EntityModelNotification>;
    })
  );
}

getItemResourceNotificationGet.PATH = '/notifications/{id}';
