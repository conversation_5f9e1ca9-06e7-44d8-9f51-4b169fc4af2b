/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EntityModelNotification } from '../../models/entity-model-notification';
import { NotificationRequestBody } from '../../models/notification-request-body';

export interface PutItemResourceNotificationPut$Params {
  id: string;
      body: NotificationRequestBody
}

export function putItemResourceNotificationPut(http: HttpClient, rootUrl: string, params: PutItemResourceNotificationPut$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelNotification>> {
  const rb = new RequestBuilder(rootUrl, putItemResourceNotificationPut.PATH, 'put');
  if (params) {
    rb.path('id', params.id, {});
    rb.body(params.body, 'application/json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EntityModelNotification>;
    })
  );
}

putItemResourceNotificationPut.PATH = '/notifications/{id}';
