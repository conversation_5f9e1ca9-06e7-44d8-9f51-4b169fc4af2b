/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { NotificationPreferences } from '../../models/notification-preferences';

export interface GetUserNotificationPreferences$Params {
  userId: string;
}

export function getUserNotificationPreferences(http: HttpClient, rootUrl: string, params: GetUserNotificationPreferences$Params, context?: HttpContext): Observable<StrictHttpResponse<NotificationPreferences>> {
  const rb = new RequestBuilder(rootUrl, getUserNotificationPreferences.PATH, 'get');
  if (params) {
    rb.path('userId', params.userId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<NotificationPreferences>;
    })
  );
}

getUserNotificationPreferences.PATH = '/preference/{userId}';
