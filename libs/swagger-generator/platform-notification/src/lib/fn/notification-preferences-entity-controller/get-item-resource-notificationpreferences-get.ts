/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EntityModelNotificationPreferences } from '../../models/entity-model-notification-preferences';

export interface GetItemResourceNotificationpreferencesGet$Params {
  id: string;
}

export function getItemResourceNotificationpreferencesGet(http: HttpClient, rootUrl: string, params: GetItemResourceNotificationpreferencesGet$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelNotificationPreferences>> {
  const rb = new RequestBuilder(rootUrl, getItemResourceNotificationpreferencesGet.PATH, 'get');
  if (params) {
    rb.path('id', params.id, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EntityModelNotificationPreferences>;
    })
  );
}

getItemResourceNotificationpreferencesGet.PATH = '/notificationPreferenceses/{id}';
