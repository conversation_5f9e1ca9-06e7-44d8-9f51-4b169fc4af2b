/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EntityModelNotificationPreferences } from '../../models/entity-model-notification-preferences';
import { NotificationPreferencesRequestBody } from '../../models/notification-preferences-request-body';

export interface PutItemResourceNotificationpreferencesPut$Params {
  id: string;
      body: NotificationPreferencesRequestBody
}

export function putItemResourceNotificationpreferencesPut(http: HttpClient, rootUrl: string, params: PutItemResourceNotificationpreferencesPut$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelNotificationPreferences>> {
  const rb = new RequestBuilder(rootUrl, putItemResourceNotificationpreferencesPut.PATH, 'put');
  if (params) {
    rb.path('id', params.id, {});
    rb.body(params.body, 'application/json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EntityModelNotificationPreferences>;
    })
  );
}

putItemResourceNotificationpreferencesPut.PATH = '/notificationPreferenceses/{id}';
