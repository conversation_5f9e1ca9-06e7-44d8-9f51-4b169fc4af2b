/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';


export interface DeleteInitiatorUserFullNameFromDbRecords$Params {
  saveToDb: boolean;
}

export function deleteInitiatorUserFullNameFromDbRecords(http: HttpClient, rootUrl: string, params: DeleteInitiatorUserFullNameFromDbRecords$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
  const rb = new RequestBuilder(rootUrl, deleteInitiatorUserFullNameFromDbRecords.PATH, 'post');
  if (params) {
    rb.query('saveToDb', params.saveToDb, {});
  }

  return http.request(
    rb.build({ responseType: 'text', accept: '*/*', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;
    })
  );
}

deleteInitiatorUserFullNameFromDbRecords.PATH = '/migration';
