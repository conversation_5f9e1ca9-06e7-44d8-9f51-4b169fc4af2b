/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { deleteAllByBusinessCases } from '../fn/business-case-removal-internal-controller/delete-all-by-business-cases';
import { DeleteAllByBusinessCases$Params } from '../fn/business-case-removal-internal-controller/delete-all-by-business-cases';

@Injectable({ providedIn: 'root' })
export class BusinessCaseRemovalInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `deleteAllByBusinessCases()` */
  static readonly DeleteAllByBusinessCasesPath = '/internal/business-case-removal/by-business-case-ids';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteAllByBusinessCases()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   *
   * @deprecated
   */
  deleteAllByBusinessCases$Response(params: DeleteAllByBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return deleteAllByBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteAllByBusinessCases$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   *
   * @deprecated
   */
  deleteAllByBusinessCases(params: DeleteAllByBusinessCases$Params, context?: HttpContext): Observable<Array<string>> {
    return this.deleteAllByBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
