/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { AssignmentsByStatusesCount } from '../models/assignments-by-statuses-count';
import { cancelManualUserAssignment } from '../fn/user-assignment-controller/cancel-manual-user-assignment';
import { CancelManualUserAssignment$Params } from '../fn/user-assignment-controller/cancel-manual-user-assignment';
import { completeManualUserAssignment } from '../fn/user-assignment-controller/complete-manual-user-assignment';
import { CompleteManualUserAssignment$Params } from '../fn/user-assignment-controller/complete-manual-user-assignment';
import { CountByPerspectivesResponse } from '../models/count-by-perspectives-response';
import { createManualUserAssignments } from '../fn/user-assignment-controller/create-manual-user-assignments';
import { CreateManualUserAssignments$Params } from '../fn/user-assignment-controller/create-manual-user-assignments';
import { getAssignmentCountByUserIdAndStatus } from '../fn/user-assignment-controller/get-assignment-count-by-user-id-and-status';
import { GetAssignmentCountByUserIdAndStatus$Params } from '../fn/user-assignment-controller/get-assignment-count-by-user-id-and-status';
import { getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary } from '../fn/user-assignment-controller/get-assignment-count-by-user-id-and-statuses-and-perspectives-for-summary';
import { GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Params } from '../fn/user-assignment-controller/get-assignment-count-by-user-id-and-statuses-and-perspectives-for-summary';
import { getAssignmentPosition } from '../fn/user-assignment-controller/get-assignment-position';
import { GetAssignmentPosition$Params } from '../fn/user-assignment-controller/get-assignment-position';
import { getBusinessCasesInfoByUserAssignmentLastUpdatedDate } from '../fn/user-assignment-controller/get-business-cases-info-by-user-assignment-last-updated-date';
import { GetBusinessCasesInfoByUserAssignmentLastUpdatedDate$Params } from '../fn/user-assignment-controller/get-business-cases-info-by-user-assignment-last-updated-date';
import { getUserAssignmentsAvailableTypesForStatus } from '../fn/user-assignment-controller/get-user-assignments-available-types-for-status';
import { GetUserAssignmentsAvailableTypesForStatus$Params } from '../fn/user-assignment-controller/get-user-assignments-available-types-for-status';
import { getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary } from '../fn/user-assignment-controller/get-user-assignments-by-user-id-and-business-case-ids-for-summary';
import { GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Params } from '../fn/user-assignment-controller/get-user-assignments-by-user-id-and-business-case-ids-for-summary';
import { getUserAssignmentsForUserIdAndBusinessCaseId } from '../fn/user-assignment-controller/get-user-assignments-for-user-id-and-business-case-id';
import { GetUserAssignmentsForUserIdAndBusinessCaseId$Params } from '../fn/user-assignment-controller/get-user-assignments-for-user-id-and-business-case-id';
import { updateManualUserAssignments } from '../fn/user-assignment-controller/update-manual-user-assignments';
import { UpdateManualUserAssignments$Params } from '../fn/user-assignment-controller/update-manual-user-assignments';
import { UserAssignment } from '../models/user-assignment';
import { UserAssignmentGroupPositionResult } from '../models/user-assignment-group-position-result';
import { UserAssignmentResponse } from '../models/user-assignment-response';

@Injectable({ providedIn: 'root' })
export class UserAssignmentControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `updateManualUserAssignments()` */
  static readonly UpdateManualUserAssignmentsPath = '/user-assignments/manual-todos';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateManualUserAssignments()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  updateManualUserAssignments$Response(params: UpdateManualUserAssignments$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return updateManualUserAssignments(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateManualUserAssignments$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  updateManualUserAssignments(params: UpdateManualUserAssignments$Params, context?: HttpContext): Observable<void> {
    return this.updateManualUserAssignments$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `createManualUserAssignments()` */
  static readonly CreateManualUserAssignmentsPath = '/user-assignments/manual-todos';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `createManualUserAssignments()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createManualUserAssignments$Response(params: CreateManualUserAssignments$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return createManualUserAssignments(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `createManualUserAssignments$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createManualUserAssignments(params: CreateManualUserAssignments$Params, context?: HttpContext): Observable<void> {
    return this.createManualUserAssignments$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `completeManualUserAssignment()` */
  static readonly CompleteManualUserAssignmentPath = '/user-assignments/manual-todos/complete';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `completeManualUserAssignment()` instead.
   *
   * This method doesn't expect any request body.
   */
  completeManualUserAssignment$Response(params: CompleteManualUserAssignment$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return completeManualUserAssignment(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `completeManualUserAssignment$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  completeManualUserAssignment(params: CompleteManualUserAssignment$Params, context?: HttpContext): Observable<void> {
    return this.completeManualUserAssignment$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `cancelManualUserAssignment()` */
  static readonly CancelManualUserAssignmentPath = '/user-assignments/manual-todos/cancel';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `cancelManualUserAssignment()` instead.
   *
   * This method doesn't expect any request body.
   */
  cancelManualUserAssignment$Response(params: CancelManualUserAssignment$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return cancelManualUserAssignment(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `cancelManualUserAssignment$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  cancelManualUserAssignment(params: CancelManualUserAssignment$Params, context?: HttpContext): Observable<void> {
    return this.cancelManualUserAssignment$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getUserAssignmentsForUserIdAndBusinessCaseId()` */
  static readonly GetUserAssignmentsForUserIdAndBusinessCaseIdPath = '/user-assignments/user';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserAssignmentsForUserIdAndBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsForUserIdAndBusinessCaseId$Response(params: GetUserAssignmentsForUserIdAndBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserAssignment>>> {
    return getUserAssignmentsForUserIdAndBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserAssignmentsForUserIdAndBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsForUserIdAndBusinessCaseId(params: GetUserAssignmentsForUserIdAndBusinessCaseId$Params, context?: HttpContext): Observable<Array<UserAssignment>> {
    return this.getUserAssignmentsForUserIdAndBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserAssignment>>): Array<UserAssignment> => r.body)
    );
  }

  /** Path part for operation `getAssignmentCountByUserIdAndStatus()` */
  static readonly GetAssignmentCountByUserIdAndStatusPath = '/user-assignments/user/count/assignments-by-statues';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAssignmentCountByUserIdAndStatus()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatus$Response(params: GetAssignmentCountByUserIdAndStatus$Params, context?: HttpContext): Observable<StrictHttpResponse<AssignmentsByStatusesCount>> {
    return getAssignmentCountByUserIdAndStatus(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAssignmentCountByUserIdAndStatus$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatus(params: GetAssignmentCountByUserIdAndStatus$Params, context?: HttpContext): Observable<AssignmentsByStatusesCount> {
    return this.getAssignmentCountByUserIdAndStatus$Response(params, context).pipe(
      map((r: StrictHttpResponse<AssignmentsByStatusesCount>): AssignmentsByStatusesCount => r.body)
    );
  }

  /** Path part for operation `getBusinessCasesInfoByUserAssignmentLastUpdatedDate()` */
  static readonly GetBusinessCasesInfoByUserAssignmentLastUpdatedDatePath = '/user-assignments/user/business-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCasesInfoByUserAssignmentLastUpdatedDate()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesInfoByUserAssignmentLastUpdatedDate$Response(params: GetBusinessCasesInfoByUserAssignmentLastUpdatedDate$Params, context?: HttpContext): Observable<StrictHttpResponse<UserAssignmentResponse>> {
    return getBusinessCasesInfoByUserAssignmentLastUpdatedDate(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCasesInfoByUserAssignmentLastUpdatedDate$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesInfoByUserAssignmentLastUpdatedDate(params: GetBusinessCasesInfoByUserAssignmentLastUpdatedDate$Params, context?: HttpContext): Observable<UserAssignmentResponse> {
    return this.getBusinessCasesInfoByUserAssignmentLastUpdatedDate$Response(params, context).pipe(
      map((r: StrictHttpResponse<UserAssignmentResponse>): UserAssignmentResponse => r.body)
    );
  }

  /** Path part for operation `getAssignmentPosition()` */
  static readonly GetAssignmentPositionPath = '/user-assignments/user/assignment-position';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAssignmentPosition()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentPosition$Response(params: GetAssignmentPosition$Params, context?: HttpContext): Observable<StrictHttpResponse<UserAssignmentGroupPositionResult>> {
    return getAssignmentPosition(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAssignmentPosition$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentPosition(params: GetAssignmentPosition$Params, context?: HttpContext): Observable<UserAssignmentGroupPositionResult> {
    return this.getAssignmentPosition$Response(params, context).pipe(
      map((r: StrictHttpResponse<UserAssignmentGroupPositionResult>): UserAssignmentGroupPositionResult => r.body)
    );
  }

  /** Path part for operation `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary()` */
  static readonly GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummaryPath = '/user-assignments/summary/user';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Response(params: GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserAssignment>>> {
    return getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary(params: GetUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Params, context?: HttpContext): Observable<Array<UserAssignment>> {
    return this.getUserAssignmentsByUserIdAndBusinessCaseIdsForSummary$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<UserAssignment>>): Array<UserAssignment> => r.body)
    );
  }

  /** Path part for operation `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary()` */
  static readonly GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummaryPath = '/user-assignments/summary/user/count/assignments-by-perspectives';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Response(params: GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Params, context?: HttpContext): Observable<StrictHttpResponse<CountByPerspectivesResponse>> {
    return getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary(params: GetAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Params, context?: HttpContext): Observable<CountByPerspectivesResponse> {
    return this.getAssignmentCountByUserIdAndStatusesAndPerspectivesForSummary$Response(params, context).pipe(
      map((r: StrictHttpResponse<CountByPerspectivesResponse>): CountByPerspectivesResponse => r.body)
    );
  }

  /** Path part for operation `getUserAssignmentsAvailableTypesForStatus()` */
  static readonly GetUserAssignmentsAvailableTypesForStatusPath = '/user-assignments/available-types';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserAssignmentsAvailableTypesForStatus()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsAvailableTypesForStatus$Response(params: GetUserAssignmentsAvailableTypesForStatus$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<'PROVIDE_DATA' | 'REVIEW_APPLICATION' | 'REVIEW_CONTRACT' | 'MANUAL_ASSIGNMENT'>>> {
    return getUserAssignmentsAvailableTypesForStatus(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserAssignmentsAvailableTypesForStatus$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserAssignmentsAvailableTypesForStatus(params: GetUserAssignmentsAvailableTypesForStatus$Params, context?: HttpContext): Observable<Array<'PROVIDE_DATA' | 'REVIEW_APPLICATION' | 'REVIEW_CONTRACT' | 'MANUAL_ASSIGNMENT'>> {
    return this.getUserAssignmentsAvailableTypesForStatus$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<'PROVIDE_DATA' | 'REVIEW_APPLICATION' | 'REVIEW_CONTRACT' | 'MANUAL_ASSIGNMENT'>>): Array<'PROVIDE_DATA' | 'REVIEW_APPLICATION' | 'REVIEW_CONTRACT' | 'MANUAL_ASSIGNMENT'> => r.body)
    );
  }

}
