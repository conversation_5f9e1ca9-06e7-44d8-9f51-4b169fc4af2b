/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata } from '../fn/user-assignment-metadata-migration-controller/replace-business-case-lead-customer-name-with-business-case-company-name-in-metadata';
import { ReplaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata$Params } from '../fn/user-assignment-metadata-migration-controller/replace-business-case-lead-customer-name-with-business-case-company-name-in-metadata';
import { updateUserAssignmentMetadata } from '../fn/user-assignment-metadata-migration-controller/update-user-assignment-metadata';
import { UpdateUserAssignmentMetadata$Params } from '../fn/user-assignment-metadata-migration-controller/update-user-assignment-metadata';

@Injectable({ providedIn: 'root' })
export class UserAssignmentMetadataMigrationControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `updateUserAssignmentMetadata()` */
  static readonly UpdateUserAssignmentMetadataPath = '/api/migration/update-user-assignment-metadata';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateUserAssignmentMetadata()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateUserAssignmentMetadata$Response(params: UpdateUserAssignmentMetadata$Params, context?: HttpContext): Observable<StrictHttpResponse<{
[key: string]: Array<string>;
}>> {
    return updateUserAssignmentMetadata(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateUserAssignmentMetadata$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateUserAssignmentMetadata(params: UpdateUserAssignmentMetadata$Params, context?: HttpContext): Observable<{
[key: string]: Array<string>;
}> {
    return this.updateUserAssignmentMetadata$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
[key: string]: Array<string>;
}>): {
[key: string]: Array<string>;
} => r.body)
    );
  }

  /** Path part for operation `replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata()` */
  static readonly ReplaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadataPath = '/api/migration/replace-customer-with-company';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata()` instead.
   *
   * This method doesn't expect any request body.
   */
  replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata$Response(params: ReplaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata(params: ReplaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata$Params, context?: HttpContext): Observable<void> {
    return this.replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
