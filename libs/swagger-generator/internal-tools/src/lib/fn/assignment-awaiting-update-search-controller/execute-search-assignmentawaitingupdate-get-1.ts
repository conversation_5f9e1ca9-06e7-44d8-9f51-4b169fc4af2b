/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelAssignmentAwaitingUpdate } from '../../models/collection-model-entity-model-assignment-awaiting-update';

export interface ExecuteSearchAssignmentawaitingupdateGet1$Params {
  localDateTime?: string;
}

export function executeSearchAssignmentawaitingupdateGet1(http: HttpClient, rootUrl: string, params?: ExecuteSearchAssignmentawaitingupdateGet1$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelAssignmentAwaitingUpdate>> {
  const rb = new RequestBuilder(rootUrl, executeSearchAssignmentawaitingupdateGet1.PATH, 'get');
  if (params) {
    rb.query('localDateTime', params.localDateTime, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelAssignmentAwaitingUpdate>;
    })
  );
}

executeSearchAssignmentawaitingupdateGet1.PATH = '/assignmentAwaitingUpdates/search/findAssignmentAwaitingUpdateByCreationDateBefore';
