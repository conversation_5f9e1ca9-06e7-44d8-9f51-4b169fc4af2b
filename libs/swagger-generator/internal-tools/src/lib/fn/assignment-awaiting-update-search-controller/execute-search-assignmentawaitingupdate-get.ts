/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelAssignmentAwaitingUpdate } from '../../models/collection-model-entity-model-assignment-awaiting-update';

export interface ExecuteSearchAssignmentawaitingupdateGet$Params {
  businessCaseIds?: Array<string>;
}

export function executeSearchAssignmentawaitingupdateGet(http: HttpClient, rootUrl: string, params?: ExecuteSearchAssignmentawaitingupdateGet$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelAssignmentAwaitingUpdate>> {
  const rb = new RequestBuilder(rootUrl, executeSearchAssignmentawaitingupdateGet.PATH, 'get');
  if (params) {
    rb.query('businessCaseIds', params.businessCaseIds, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelAssignmentAwaitingUpdate>;
    })
  );
}

executeSearchAssignmentawaitingupdateGet.PATH = '/assignmentAwaitingUpdates/search/findAllByBusinessCaseIdIn';
