/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EntityModelAssignmentAwaitingUpdate } from '../../models/entity-model-assignment-awaiting-update';

export interface ExecuteSearchAssignmentawaitingupdateGet2$Params {
  creationUserId?: string;
  assigneeUserId?: string;
  businessCaseId?: string;
}

export function executeSearchAssignmentawaitingupdateGet2(http: HttpClient, rootUrl: string, params?: ExecuteSearchAssignmentawaitingupdateGet2$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>> {
  const rb = new RequestBuilder(rootUrl, executeSearchAssignmentawaitingupdateGet2.PATH, 'get');
  if (params) {
    rb.query('creationUserId', params.creationUserId, {});
    rb.query('assigneeUserId', params.assigneeUserId, {});
    rb.query('businessCaseId', params.businessCaseId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EntityModelAssignmentAwaitingUpdate>;
    })
  );
}

executeSearchAssignmentawaitingupdateGet2.PATH = '/assignmentAwaitingUpdates/search/findAssignmentAwaitingUpdateByCreationUserIdAndAssigneeUserIdAndBusinessCaseId';
