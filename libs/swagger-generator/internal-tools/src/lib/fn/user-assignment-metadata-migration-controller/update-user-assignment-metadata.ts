/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';


export interface UpdateUserAssignmentMetadata$Params {
  securityCode: string;
  persist?: boolean;
}

export function updateUserAssignmentMetadata(http: HttpClient, rootUrl: string, params: UpdateUserAssignmentMetadata$Params, context?: HttpContext): Observable<StrictHttpResponse<{
[key: string]: Array<string>;
}>> {
  const rb = new RequestBuilder(rootUrl, updateUserAssignmentMetadata.PATH, 'post');
  if (params) {
    rb.query('securityCode', params.securityCode, {});
    rb.query('persist', params.persist, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<{
      [key: string]: Array<string>;
      }>;
    })
  );
}

updateUserAssignmentMetadata.PATH = '/api/migration/update-user-assignment-metadata';
