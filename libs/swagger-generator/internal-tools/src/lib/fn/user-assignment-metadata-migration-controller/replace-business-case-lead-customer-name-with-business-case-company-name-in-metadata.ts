/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';


export interface ReplaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata$Params {
  securityCode: string;
  persist?: boolean;
}

export function replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata(http: HttpClient, rootUrl: string, params: ReplaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
  const rb = new RequestBuilder(rootUrl, replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata.PATH, 'post');
  if (params) {
    rb.query('securityCode', params.securityCode, {});
    rb.query('persist', params.persist, {});
  }

  return http.request(
    rb.build({ responseType: 'text', accept: '*/*', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;
    })
  );
}

replaceBusinessCaseLeadCustomerNameWithBusinessCaseCompanyNameInMetadata.PATH = '/api/migration/replace-customer-with-company';
