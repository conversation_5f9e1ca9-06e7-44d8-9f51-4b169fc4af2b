/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { UserAssignmentGroupPositionResult } from '../../models/user-assignment-group-position-result';

export interface GetAssignmentPosition1$Params {
  perspective: 'MY_TASKS' | 'DELEGATED_TASKS';
  userId: string;
  userAssignmentId: string;
  limit?: number;
  businessOrderBy?: 'ASSIGNED_BY' | 'ASSIGNED_TO' | 'ASSIGNMENT_DUE_DATE' | 'LAST_UPDATED_DATE' | 'USER_ASSIGNMENT_TYPE';
  businessCaseDirection?: 'ASC' | 'DESC';
  userAssignmentOrderBy?: 'ASSIGNED_BY' | 'ASSIGNED_TO' | 'ASSIGNMENT_DUE_DATE' | 'LAST_UPDATED_DATE' | 'USER_ASSIGNMENT_TYPE';
  userAssignmentDirection?: 'ASC' | 'DESC';
}

export function getAssignmentPosition1(http: HttpClient, rootUrl: string, params: GetAssignmentPosition1$Params, context?: HttpContext): Observable<StrictHttpResponse<UserAssignmentGroupPositionResult>> {
  const rb = new RequestBuilder(rootUrl, getAssignmentPosition1.PATH, 'get');
  if (params) {
    rb.query('perspective', params.perspective, {});
    rb.query('userId', params.userId, {});
    rb.query('userAssignmentId', params.userAssignmentId, {});
    rb.query('limit', params.limit, {});
    rb.query('businessOrderBy', params.businessOrderBy, {});
    rb.query('businessCaseDirection', params.businessCaseDirection, {});
    rb.query('userAssignmentOrderBy', params.userAssignmentOrderBy, {});
    rb.query('userAssignmentDirection', params.userAssignmentDirection, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<UserAssignmentGroupPositionResult>;
    })
  );
}

getAssignmentPosition1.PATH = '/internal/user-assignments/user/assignment-position';
