/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { UserAssignmentResponse } from '../../models/user-assignment-response';

export interface GetBusinessCasesInfoByUserAssignmentLastUpdatedDate1$Params {
  userId: string;
  perspective: 'MY_TASKS' | 'DELEGATED_TASKS';
  assignmentTypes: Array<'PROVIDE_DATA' | 'REVIEW_APPLICATION' | 'REVIEW_CONTRACT' | 'MANUAL_ASSIGNMENT'>;
  assignmentStatuses: Array<'PENDING' | 'COMPLETED' | 'ARCHIVED' | 'CANCELLED'>;
  offset?: number;
  limit?: number;
  searchPhrase?: string;
  orderBy?: 'ASSIGNED_BY' | 'ASSIGNED_TO' | 'ASSIGNMENT_DUE_DATE' | 'LAST_UPDATED_DATE' | 'USER_ASSIGNMENT_TYPE';
  direction?: 'ASC' | 'DESC';
}

export function getBusinessCasesInfoByUserAssignmentLastUpdatedDate1(http: HttpClient, rootUrl: string, params: GetBusinessCasesInfoByUserAssignmentLastUpdatedDate1$Params, context?: HttpContext): Observable<StrictHttpResponse<UserAssignmentResponse>> {
  const rb = new RequestBuilder(rootUrl, getBusinessCasesInfoByUserAssignmentLastUpdatedDate1.PATH, 'get');
  if (params) {
    rb.path('userId', params.userId, {});
    rb.query('perspective', params.perspective, {});
    rb.query('assignmentTypes', params.assignmentTypes, {});
    rb.query('assignmentStatuses', params.assignmentStatuses, {});
    rb.query('offset', params.offset, {});
    rb.query('limit', params.limit, {});
    rb.query('searchPhrase', params.searchPhrase, {});
    rb.query('orderBy', params.orderBy, {});
    rb.query('direction', params.direction, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<UserAssignmentResponse>;
    })
  );
}

getBusinessCasesInfoByUserAssignmentLastUpdatedDate1.PATH = '/internal/user-assignments/user/{userId}/business-cases';
