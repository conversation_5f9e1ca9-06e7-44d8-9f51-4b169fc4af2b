/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelUserAssignment } from '../../models/collection-model-entity-model-user-assignment';

export interface ExecuteSearchUserassignmentGet12$Params {
  status?: 'PENDING' | 'COMPLETED' | 'ARCHIVED' | 'CANCELLED';
  localDateTime?: string;
}

export function executeSearchUserassignmentGet12(http: HttpClient, rootUrl: string, params?: ExecuteSearchUserassignmentGet12$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
  const rb = new RequestBuilder(rootUrl, executeSearchUserassignmentGet12.PATH, 'get');
  if (params) {
    rb.query('status', params.status, {});
    rb.query('localDateTime', params.localDateTime, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelUserAssignment>;
    })
  );
}

executeSearchUserassignmentGet12.PATH = '/userAssignments/search/findAllByStatusAndLastUpdatedDateIsBefore';
