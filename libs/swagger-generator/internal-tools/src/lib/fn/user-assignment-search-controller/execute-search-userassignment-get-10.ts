/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelUserAssignment } from '../../models/collection-model-entity-model-user-assignment';

export interface ExecuteSearchUserassignmentGet10$Params {
  status?: 'PENDING' | 'COMPLETED' | 'ARCHIVED' | 'CANCELLED';
  startDateTime?: string;
  endDateTime?: string;
}

export function executeSearchUserassignmentGet10(http: HttpClient, rootUrl: string, params?: ExecuteSearchUserassignmentGet10$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
  const rb = new RequestBuilder(rootUrl, executeSearchUserassignmentGet10.PATH, 'get');
  if (params) {
    rb.query('status', params.status, {});
    rb.query('startDateTime', params.startDateTime, {});
    rb.query('endDateTime', params.endDateTime, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelUserAssignment>;
    })
  );
}

executeSearchUserassignmentGet10.PATH = '/userAssignments/search/findAllByStatusAndDueDateBetween';
