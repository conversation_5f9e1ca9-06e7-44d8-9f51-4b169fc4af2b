/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelUserAssignment } from '../../models/collection-model-entity-model-user-assignment';

export interface ExecuteSearchUserassignmentGet11$Params {
  status?: 'PENDING' | 'COMPLETED' | 'ARCHIVED' | 'CANCELLED';
  dateTime?: string;
}

export function executeSearchUserassignmentGet11(http: HttpClient, rootUrl: string, params?: ExecuteSearchUserassignmentGet11$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
  const rb = new RequestBuilder(rootUrl, executeSearchUserassignmentGet11.PATH, 'get');
  if (params) {
    rb.query('status', params.status, {});
    rb.query('dateTime', params.dateTime, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelUserAssignment>;
    })
  );
}

executeSearchUserassignmentGet11.PATH = '/userAssignments/search/findAllByStatusAndDueDateIsBefore';
