/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelUserAssignment } from '../../models/collection-model-entity-model-user-assignment';

export interface ExecuteSearchUserassignmentGet14$Params {
  assigneeOrCreationUser?: string;
  userId?: string;
  statuses?: Array<'PENDING' | 'COMPLETED' | 'ARCHIVED' | 'CANCELLED'>;
  groupOffset?: number;
  groupLimit?: number;
  orderBy?: string;
  direction?: number;
}

export function executeSearchUserassignmentGet14(http: HttpClient, rootUrl: string, params?: ExecuteSearchUserassignmentGet14$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
  const rb = new RequestBuilder(rootUrl, executeSearchUserassignmentGet14.PATH, 'get');
  if (params) {
    rb.query('assigneeOrCreationUser', params.assigneeOrCreationUser, {});
    rb.query('userId', params.userId, {});
    rb.query('statuses', params.statuses, {});
    rb.query('groupOffset', params.groupOffset, {});
    rb.query('groupLimit', params.groupLimit, {});
    rb.query('orderBy', params.orderBy, {});
    rb.query('direction', params.direction, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelUserAssignment>;
    })
  );
}

executeSearchUserassignmentGet14.PATH = '/userAssignments/search/findUserAssignmentsByUserIdAndStatusAndSortByLastUpdatedDateAndGroupByBusinessCaseWithPagination';
