/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CollectionModelEntityModelUserAssignment } from '../../models/collection-model-entity-model-user-assignment';

export interface ExecuteSearchUserassignmentGet15$Params {
  assigneeOrCreationUserField?: string;
  userId?: string;
  statuses?: Array<'PENDING' | 'COMPLETED' | 'ARCHIVED' | 'CANCELLED'>;
  searchPhrase?: string;
  offset?: number;
  pageSize?: number;
  orderBy?: string;
  direction?: number;
}

export function executeSearchUserassignmentGet15(http: HttpClient, rootUrl: string, params?: ExecuteSearchUserassignmentGet15$Params, context?: HttpContext): Observable<StrictHttpResponse<CollectionModelEntityModelUserAssignment>> {
  const rb = new RequestBuilder(rootUrl, executeSearchUserassignmentGet15.PATH, 'get');
  if (params) {
    rb.query('assigneeOrCreationUserField', params.assigneeOrCreationUserField, {});
    rb.query('userId', params.userId, {});
    rb.query('statuses', params.statuses, {});
    rb.query('searchPhrase', params.searchPhrase, {});
    rb.query('offset', params.offset, {});
    rb.query('pageSize', params.pageSize, {});
    rb.query('orderBy', params.orderBy, {});
    rb.query('direction', params.direction, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<CollectionModelEntityModelUserAssignment>;
    })
  );
}

executeSearchUserassignmentGet15.PATH = '/userAssignments/search/findUserAssignmentsByUserIdAndStatusAndSortByLastUpdatedDateAndGroupByBusinessCaseWithPaginationWithSearch';
