/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { EntityModelUserAssignment } from '../../models/entity-model-user-assignment';

export interface ExecuteSearchUserassignmentGet13$Params {
  creationUserId?: string;
  assigneeUserId?: string;
  referenceRecordId?: string;
}

export function executeSearchUserassignmentGet13(http: HttpClient, rootUrl: string, params?: ExecuteSearchUserassignmentGet13$Params, context?: HttpContext): Observable<StrictHttpResponse<EntityModelUserAssignment>> {
  const rb = new RequestBuilder(rootUrl, executeSearchUserassignmentGet13.PATH, 'get');
  if (params) {
    rb.query('creationUserId', params.creationUserId, {});
    rb.query('assigneeUserId', params.assigneeUserId, {});
    rb.query('referenceRecordId', params.referenceRecordId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<EntityModelUserAssignment>;
    })
  );
}

executeSearchUserassignmentGet13.PATH = '/userAssignments/search/findUserAssignmentByCreationUserIdAndAssigneeUserIdAndReferenceRecordId';
