/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { UserAssignmentGroupPositionResult } from '../../models/user-assignment-group-position-result';

export interface GetAssignmentPosition$Params {
  perspective: 'MY_TASKS' | 'DELEGATED_TASKS';
  userAssignmentId: string;
  limit?: number;
  businessCaseOrderBy?: 'ASSIGNED_BY' | 'ASSIGNED_TO' | 'ASSIGNMENT_DUE_DATE' | 'LAST_UPDATED_DATE' | 'USER_ASSIGNMENT_TYPE';
  businessCaseDirection?: 'ASC' | 'DESC';
  userAssignmentOrderBy?: 'ASSIGNED_BY' | 'ASSIGNED_TO' | 'ASSIGNMENT_DUE_DATE' | 'LAST_UPDATED_DATE' | 'USER_ASSIGNMENT_TYPE';
  userAssignmentDirection?: 'ASC' | 'DESC';
}

export function getAssignmentPosition(http: HttpClient, rootUrl: string, params: GetAssignmentPosition$Params, context?: HttpContext): Observable<StrictHttpResponse<UserAssignmentGroupPositionResult>> {
  const rb = new RequestBuilder(rootUrl, getAssignmentPosition.PATH, 'get');
  if (params) {
    rb.query('perspective', params.perspective, {});
    rb.query('userAssignmentId', params.userAssignmentId, {});
    rb.query('limit', params.limit, {});
    rb.query('businessCaseOrderBy', params.businessCaseOrderBy, {});
    rb.query('businessCaseDirection', params.businessCaseDirection, {});
    rb.query('userAssignmentOrderBy', params.userAssignmentOrderBy, {});
    rb.query('userAssignmentDirection', params.userAssignmentDirection, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<UserAssignmentGroupPositionResult>;
    })
  );
}

getAssignmentPosition.PATH = '/user-assignments/user/assignment-position';
