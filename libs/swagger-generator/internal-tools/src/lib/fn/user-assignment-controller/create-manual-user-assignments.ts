/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { CreateManualTodosRequest } from '../../models/create-manual-todos-request';

export interface CreateManualUserAssignments$Params {
  businessCaseId: string;
      body: CreateManualTodosRequest
}

export function createManualUserAssignments(http: HttpClient, rootUrl: string, params: CreateManualUserAssignments$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
  const rb = new RequestBuilder(rootUrl, createManualUserAssignments.PATH, 'post');
  if (params) {
    rb.query('businessCaseId', params.businessCaseId, {});
    rb.body(params.body, 'application/json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: '*/*', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;
    })
  );
}

createManualUserAssignments.PATH = '/user-assignments/manual-todos';
