/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';


export interface CancelManualUserAssignment$Params {
  userAssignmentId: string;
}

export function cancelManualUserAssignment(http: HttpClient, rootUrl: string, params: CancelManualUserAssignment$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
  const rb = new RequestBuilder(rootUrl, cancelManualUserAssignment.PATH, 'put');
  if (params) {
    rb.query('userAssignmentId', params.userAssignmentId, {});
  }

  return http.request(
    rb.build({ responseType: 'text', accept: '*/*', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;
    })
  );
}

cancelManualUserAssignment.PATH = '/user-assignments/manual-todos/cancel';
