/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';


export interface GetUserAssignmentsAvailableTypesForStatus$Params {
  perspective: 'MY_TASKS' | 'DELEGATED_TASKS';
  statusList: Array<'PENDING' | 'COMPLETED' | 'ARCHIVED' | 'CANCELLED'>;
}

export function getUserAssignmentsAvailableTypesForStatus(http: HttpClient, rootUrl: string, params: GetUserAssignmentsAvailableTypesForStatus$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<'PROVIDE_DATA' | 'REVIEW_APPLICATION' | 'REVIEW_CONTRACT' | 'MANUAL_ASSIGNMENT'>>> {
  const rb = new RequestBuilder(rootUrl, getUserAssignmentsAvailableTypesForStatus.PATH, 'get');
  if (params) {
    rb.query('perspective', params.perspective, {});
    rb.query('statusList', params.statusList, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<Array<'PROVIDE_DATA' | 'REVIEW_APPLICATION' | 'REVIEW_CONTRACT' | 'MANUAL_ASSIGNMENT'>>;
    })
  );
}

getUserAssignmentsAvailableTypesForStatus.PATH = '/user-assignments/available-types';
