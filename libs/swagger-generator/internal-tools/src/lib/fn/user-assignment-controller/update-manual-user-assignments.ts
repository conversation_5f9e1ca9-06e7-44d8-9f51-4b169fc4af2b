/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { RequestBuilder } from '../../request-builder';
import { StrictHttpResponse } from '../../strict-http-response';

import { UpdateManualTodoRequest } from '../../models/update-manual-todo-request';

export interface UpdateManualUserAssignments$Params {
  userAssignmentId: string;
  businessCaseId: string;
  body: UpdateManualTodoRequest;
}

export function updateManualUserAssignments(
  http: HttpClient,
  rootUrl: string,
  params: UpdateManualUserAssignments$Params,
  context?: HttpContext,
): Observable<StrictHttpResponse<void>> {
  const rb = new RequestBuilder(
    rootUrl,
    updateManualUserAssignments.PATH,
    'put',
  );
  if (params) {
    rb.query('userAssignmentId', params.userAssignmentId, {});
    rb.query('businessCaseId', params.businessCaseId, {});
    rb.body(params.body, 'application/json');
  }

  return http
    .request(rb.build({ responseType: 'text', accept: '*/*', context }))
    .pipe(
      filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
      map((r: HttpResponse<any>) => {
        return (r as HttpResponse<any>).clone({
          body: undefined,
        }) as StrictHttpResponse<void>;
      }),
    );
}

updateManualUserAssignments.PATH = '/user-assignments/manual-todos';
