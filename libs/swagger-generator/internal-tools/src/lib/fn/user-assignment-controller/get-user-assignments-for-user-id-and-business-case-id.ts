/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { UserAssignment } from '../../models/user-assignment';

export interface GetUserAssignmentsForUserIdAndBusinessCaseId$Params {
  perspective: 'MY_TASKS' | 'DELEGATED_TASKS';
  assignmentStatuses: Array<'PENDING' | 'COMPLETED' | 'ARCHIVED' | 'CANCELLED'>;
  assignmentTypes: Array<'PROVIDE_DATA' | 'REVIEW_APPLICATION' | 'REVIEW_CONTRACT' | 'MANUAL_ASSIGNMENT'>;
  businessCaseId: string;
  offset?: number;
  limit?: number;
  searchPhrase?: string;
  orderBy?: 'ASSIGNED_BY' | 'ASSIGNED_TO' | 'ASSIGNMENT_DUE_DATE' | 'LAST_UPDATED_DATE' | 'USER_ASSIGNMENT_TYPE';
  direction?: 'ASC' | 'DESC';
}

export function getUserAssignmentsForUserIdAndBusinessCaseId(http: HttpClient, rootUrl: string, params: GetUserAssignmentsForUserIdAndBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<UserAssignment>>> {
  const rb = new RequestBuilder(rootUrl, getUserAssignmentsForUserIdAndBusinessCaseId.PATH, 'get');
  if (params) {
    rb.query('perspective', params.perspective, {});
    rb.query('assignmentStatuses', params.assignmentStatuses, {});
    rb.query('assignmentTypes', params.assignmentTypes, {});
    rb.query('businessCaseId', params.businessCaseId, {});
    rb.query('offset', params.offset, {});
    rb.query('limit', params.limit, {});
    rb.query('searchPhrase', params.searchPhrase, {});
    rb.query('orderBy', params.orderBy, {});
    rb.query('direction', params.direction, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<Array<UserAssignment>>;
    })
  );
}

getUserAssignmentsForUserIdAndBusinessCaseId.PATH = '/user-assignments/user';
