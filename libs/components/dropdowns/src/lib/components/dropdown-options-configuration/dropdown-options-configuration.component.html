<form
  [formGroup]="dropdownOptionsForm"
  cdkDropListGroup
  class="dropdown-options-container tw-p-[0.8rem_3.2rem_2.4rem]"
>
  <fin-scrollbar class="tw-max-h-[24rem]">
    <div
      #dropList
      formArrayName="options"
      cdkDropList
      (cdkDropListDropped)="drop($event)"
      cdkDropListLockAxis="y"
    >
      @for (option of options.controls; track option; let i = $index) {
        <div>
          <div cdkDrag [cdkDragBoundary]="dropList" class="tw-overflow-hidden">
            <ng-template cdkDragPreview [matchSize]="true">
              <div class="tw-overflow-hidden tw-pr-[3.2rem]">
                <fin-input
                  [formControl]="$any(option)"
                  [readonly]="true"
                  class="tw-grow tw-overflow-hidden"
                >
                  <ng-container finInputPrefix>
                    <fin-icon
                      name="drag_indicator"
                      class="tw-cursor-grab"
                      cdkDragHandle
                    ></fin-icon> </ng-container
                ></fin-input>
              </div>
            </ng-template>
            <div class="option-wrapper">
              <div
                class="tw-border-color-border-default-interactive tw-border-2 tw-h-[4rem] tw-rounded-lg tw-mr-[3.2rem] tw-mt-[1.8rem] tw-mb-[2.2rem]"
                *cdkDragPlaceholder
              >
                <div
                  class="tw-h-full tw-bg-color-background-secondary-strong tw-opacity-10"
                ></div>
              </div>
              <fin-input
                [formControl]="$any(option)"
                class="tw-grow tw-mr-[1.2rem]"
              >
                <ng-container finInputPrefix>
                  <fin-icon
                    name="drag_indicator"
                    class="tw-cursor-grab"
                    cdkDragHandle
                  ></fin-icon>
                </ng-container>

                <fin-field-messages>
                  <ng-template
                    finFieldMessage
                    type="error"
                    errorKey="required"
                    i18n="@@validationError.requiredField"
                  >
                    Pflichtfeld
                  </ng-template>
                </fin-field-messages>
              </fin-input>
              <button
                fin-button-action
                [ngClass]="{ 'tw-pointer-events-none tw-opacity-0': i < 2 }"
                [size]="finSize.M"
                (click)="removeOption(i)"
              >
                <fin-icon name="close"></fin-icon>
              </button>
            </div>
          </div>
        </div>
      }
    </div>
  </fin-scrollbar>
  <button
    fin-button
    class="tw-mt-[1.2rem]"
    [size]="finSize.M"
    [appearance]="finButtonAppearance.STEALTH"
    (click)="addOption()"
  >
    <fin-icon name="add"></fin-icon>
    <span i18n="@@dropDownOptions.config.addNewOption">Option hinzufügen</span>
  </button>
</form>
