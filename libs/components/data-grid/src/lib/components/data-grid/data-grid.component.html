<ng-container [ngTemplateOutlet]="fieldActions"></ng-container>

@if (dataGridViewMode === 'PersistentEdit') {
  <ui-data-grid-toolbar
    class="toolbar"
    [dataGridModel]="this"
    (openInFullscreen)="openInFullscreen.next($event)"
  >
    <ng-container
      [ngTemplateOutlet]="fieldActionsKebap"
      field-actions
    ></ng-container>
  </ui-data-grid-toolbar>
}

@if (dataGridViewMode === 'PersistentEdit') {
  <ui-data-grid-formula-bar
    [focusedCellCoordinates]="focusedCellCoordinates"
    [showValue]="(selectionFormatting$ | async).dataType !== 'checkbox'"
    [inputValue]="focusedCellValue"
    (inputValueChanged)="onFormulaBarValueChanged($event)"
  ></ui-data-grid-formula-bar>
}

@if (workingConfig) {
  <ag-grid-angular
    class="data-grid"
    [ngStyle]="{
      height:
        dataGridViewMode === 'ReadOnly'
          ? config.revisionTableHeight
          : config.tableHeight,
    }"
    [rowHeight]="workingConfig.rowHeight"
    [headerHeight]="workingConfig.headerHeight"
    [ngClass]="[workingConfig.theme]"
    [getContextMenuItems]="getContextMenuItems.bind(this)"
    (gridReady)="onGridReady($event)"
    (cellValueChanged)="onCellValueChanged($event)"
    (rowDragEnd)="onRowDragEnd($event)"
    (columnMoved)="onColumnMoved($event)"
    (columnPinned)="onColumnPinned($event)"
    (dragStopped)="onDragStopped()"
    (rangeSelectionChanged)="onRangeSelectionChanged($event)"
    (cellFocused)="onCellFocus($event)"
    [columnDefs]="colDefs"
    [rowData]="workingConfig.rowData"
    [defaultColDef]="workingConfig.defaultColDef"
    [localeText]="workingConfig.localeText"
    [undoRedoCellEditing]="workingConfig.undoRedoCellEditing"
    [undoRedoCellEditingLimit]="workingConfig.undoRedoCellEditingLimit"
    [suppressColumnVirtualisation]="true"
    [suppressRowVirtualisation]="false"
    [popupParent]="popupParent"
    [rowDragManaged]="workingConfig.rowDragManaged"
    [enterNavigatesVertically]="workingConfig.enterNavigatesVertically"
    [enterNavigatesVerticallyAfterEdit]="
      workingConfig.enterNavigatesVerticallyAfterEdit
    "
    [suppressRowHoverHighlight]="dataGridViewMode === 'ReadOnly'"
    [suppressCellFocus]="dataGridViewMode === 'ReadOnly'"
    [suppressContextMenu]="dataGridViewMode !== 'PersistentEdit'"
    [suppressCutToClipboard]="false"
    [suppressDragLeaveHidesColumns]="true"
    [stopEditingWhenCellsLoseFocus]="true"
    [enableFillHandle]="workingConfig.enableFillHandle"
    [enableRangeSelection]="workingConfig.enableRangeSelection"
    [processCellFromClipboard]="onProcessCellFromClipboard"
    [processCellForClipboard]="onProcessCellForClipboard"
    (cellKeyDown)="onCellKeyDown($event)"
  >
  </ag-grid-angular>
}

@if (dataGridViewMode === 'PersistentEdit') {
  <ui-data-grid-status-bar
    class="toolbar"
    [summaryMetricFunctions]="summaryMetricFunctions$ | async"
  ></ui-data-grid-status-bar>
}

<ng-template #fieldActionsKebap>
  <ng-content select="[field-actions]"></ng-content>
</ng-template>

<ng-template #fieldActions>
  <ui-label-and-actions
    class="!tw-text-body-2-moderate tw-text-color-text-primary tw-mb-[0.4rem]"
    [label]="label"
    [highlightLabel]="highlightLabel"
  >
    <ng-content select="[information-actions]"></ng-content>
  </ui-label-and-actions>
</ng-template>
