import { selectDocumentInboxViewData } from '@fincloud/state/document-inbox';
import { TableRow } from '@fincloud/types/models';

import { CdkDragDrop } from '@angular/cdk/drag-drop';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { DocumentEntity } from '@fincloud/swagger-generator/document';
import {
  PreviewService,
  Size,
} from '@fincloud/swagger-generator/document-preview';
import { BusinessCaseGroup } from '@fincloud/swagger-generator/exchange';
import { Locale } from '@fincloud/types/enums';
import { Store } from '@ngrx/store';
import { cloneDeep, isEqual } from 'lodash-es';
import {
  BehaviorSubject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  merge,
  of,
  switchMap,
  tap,
} from 'rxjs';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Alert } from '@fincloud/components/alerts';
import { FileUploaderComponent } from '@fincloud/components/files';
import { FluidCheckboxTableComponent } from '@fincloud/components/lists';
import { ConfirmationModalComponent } from '@fincloud/components/modals';
import { InformationUtils } from '@fincloud/core/business-case';
import { InboxDocumentService } from '@fincloud/core/document';
import {
  FileManagerService,
  FileService,
  TusDocumentUploadController,
} from '@fincloud/core/files';
import { Hotkey, HotkeyService } from '@fincloud/core/hotkeys';
import { IdentityService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { isUuid } from '@fincloud/core/uuid';
import { DocumentFieldManageHelperService } from '@fincloud/neoshare/document';
import { selectCustomerKey } from '@fincloud/state/customer';
import { StateLibDocumentPageActions } from '@fincloud/state/document';
import { StateLibDocumentInboxPageActions } from '@fincloud/state/document-inbox';
import { CustomerManagementControllerService } from '@fincloud/swagger-generator/authorization-server';
import { CadrGroup } from '@fincloud/swagger-generator/company';
import { BusinessCasePermission, TusUploadType } from '@fincloud/types/enums';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import {
  FIN_MODAL_DEFAULT_OPTIONS,
  FIN_MODAL_REF_PROVIDER,
  FinModalService,
} from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { FILE_EXTENSIONS_ARRAY } from '@fincloud/utils';
import { TourService } from 'ngx-ui-tour-ngx-bootstrap';
import { SelectableDocumentEntity } from '../../models/selectable-document-entity';
import { getUserTableColumnsConfig } from '../../utils/get-user-table-columns-config';
import { UPLOAD_SUCCESS } from '../../utils/upload-success';
import { ZIP_ATTACHMENT_USERNAME } from '../../utils/zip-attachment-username';

@Component({
  selector: 'app-document-inbox',
  templateUrl: './document-inbox.component.html',
  styleUrls: ['./document-inbox.component.scss'],
  providers: [
    { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
    FIN_MODAL_REF_PROVIDER,
    FinModalService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentInboxComponent implements OnInit, AfterViewInit {
  @ViewChild(FileUploaderComponent)
  fileUploader: FileUploaderComponent;

  @Input() dataRoomGroups: (BusinessCaseGroup | CadrGroup)[];
  @Input() dataRoomOwnerId: string;
  @Input() showGroupsInside: boolean;
  @Input() activeDocumentId: string;

  @Output()
  documentsUploaded = new EventEmitter<Alert>();
  @Output()
  documentsDroppedIntoGroupOutput = new EventEmitter<{
    draggedDocuments: SelectableDocumentEntity[];
    group: BusinessCaseGroup | CadrGroup;
  }>();
  @Output() clearActiveDocument = new EventEmitter<void>();
  @ViewChild(FluidCheckboxTableComponent)
  fluidTable: FluidCheckboxTableComponent;

  documents: SelectableDocumentEntity[] = [];
  documentsToShow: SelectableDocumentEntity[] = [];
  draggedDocument: SelectableDocumentEntity;
  draggedDocumentsMetaData: SelectableDocumentEntity[];
  columnConfig = getUserTableColumnsConfig(this.locale);
  selectedRows: SelectableDocumentEntity[] = [];
  userId: string;
  finSize = FinSize;
  finButtonShape = FinButtonShape;

  viewMode: 'rows' | 'grid' = 'rows';
  editMode: boolean;

  allSelected: boolean;
  searchFilterFocusedSubject$ = new BehaviorSubject<boolean>(true);
  searchFilterFocused$ = this.searchFilterFocusedSubject$.asObservable().pipe(
    debounceTime(500),
    filter(() => !this.activeDocumentId), // do not focus search input (active highlight background)
  );

  groupHovered: { [key: number]: boolean } = {};
  groupDroppedTo = -1;
  tableHight: number;

  foldersDragDropEnabled = true;
  uploadOptionsVisible = false;

  getDocumentInboxViewData$ = this.store
    .select(selectDocumentInboxViewData)
    .pipe(
      distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
      tap((data) => {
        this.fluidTable?.clearSelection();

        const accessibleParticipantsKeys = data.participants
          .filter(
            (p) =>
              p.lead ||
              data.permissions[p.customerKey]?.permissions.includes(
                BusinessCasePermission.BCP_00131,
              ),
          )
          .map((p) => p.customerKey);
        this.documents = cloneDeep(data.documents as DocumentEntity[]) ?? [];

        this.documents.forEach((d) => {
          if (InformationUtils.isEmptyValue(d.id)) {
            return;
          }
          [d.documentName, d.fileExtension] =
            this.getDocumentNameWithoutExtension(d.contentReference?.fileName);
          d.groupsContainedIn?.filter(Boolean)?.forEach((duplicate) => {
            [duplicate.fileName] = this.getDocumentNameWithoutExtension(
              duplicate.fileName,
            );
          });
          d.imgBlob = this.previewService.previewControllerLoadDocumentPreview({
            documentId: d.id,
            size: Size.S,
          });

          if (d.hasError && !d.isWarningIgnored) {
            setTimeoutUnpatched(() => {
              this.fluidTable?.extendRow(d);
            }, 1000);
          }
          if (data.usersById[d.userId]) {
            d.customerKey = data.usersById[d.userId].customerKey;
            d.userEmail = data.usersById[d.userId].username;
            const firstName = data.usersById[d.userId].firstName;
            const lastName = data.usersById[d.userId].lastName;
            d.userName = firstName + ' ' + lastName;
          } else {
            d.userName = d.userId === ZIP_ATTACHMENT_USERNAME ? '' : d.userId;
          }
        });
        // filter by participant visibility
        this.documentsToShow = this.documents.filter((d) => {
          const isAccessibleDocument = accessibleParticipantsKeys.includes(
            d.customerKey,
          );
          const isParticipantOn = accessibleParticipantsKeys.includes(
            data.userCustomerKey,
          );
          const isDocumentOwner = d.customerKey === data.userCustomerKey;

          if (data.userPartnerLead) {
            return true;
          } else if (isDocumentOwner) {
            return true;
          } else if (isParticipantOn && !d.customerKey) {
            return true;
          } else if (isAccessibleDocument) {
            return true;
          } else {
            return false;
          }
        });
      }),
    );

  constructor(
    private previewService: PreviewService,
    private store: Store,
    private finToastService: FinToastService,
    private documentInboxControllerService: InboxDocumentService,
    private hotkeysService: HotkeyService,
    public tourService: TourService,
    private finModalService: FinModalService,
    private fileService: FileService,
    private customerManagementControllerService: CustomerManagementControllerService,
    private tusDocumentUploadController: TusDocumentUploadController,
    private fileManagerService: FileManagerService,
    private identityService: IdentityService,
    private destroyRef: DestroyRef,
    private documentUploadHelperService: DocumentFieldManageHelperService,
    private cd: ChangeDetectorRef,
    @Inject(LOCALE_ID) private locale: Locale,
  ) {}

  ngOnInit() {
    this.hotkeysService
      .getHotkeyEvents(Hotkey.F)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.searchFilterFocusedSubject$.next(true);
      });

    if (this.columnConfig?.length) {
      this.columnConfig.forEach((column) => {
        column.sortable = false;
      });
      this.columnConfig[0].sortable = true;
    }
  }

  ngAfterViewInit(): void {
    this.fileUploader.options.maxFilesCount = undefined;
    this.fileUploader.fileSelected
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((files) => {
        this.handleFile(files);
      });
  }

  toggleUploadOptionsVisibility(visible: boolean) {
    this.uploadOptionsVisible = visible;
  }

  selectFilesForUpload() {
    this.fileUploader.options = {
      ...this.fileUploader.options,
      folderUploadEnabled: null,
    };
    this.toggleUploadOptionsVisibility(false);
    this.fileUploader.chooseFile();
  }

  selectFolderForUpload() {
    this.fileUploader.options = {
      ...this.fileUploader.options,
      folderUploadEnabled: true,
    };
    this.toggleUploadOptionsVisibility(false);
    this.fileUploader.chooseFile();
  }

  trackByDocumentId(_: number, document: SelectableDocumentEntity) {
    return document.contentReference?.fileName;
  }

  selectViewMode(mode: 'rows' | 'grid') {
    this.viewMode = mode;
  }

  isViewModeSelected(mode: 'rows' | 'grid') {
    return this.viewMode === mode;
  }

  filterDocuments(searchTerm: string) {
    this.documentsToShow = this.documents.filter(
      (d) =>
        d.documentName
          .toLocaleLowerCase()
          .includes(searchTerm.toLocaleLowerCase()) ||
        d.userName
          .toLocaleLowerCase()
          .includes(searchTerm.toLocaleLowerCase()) ||
        d.createdOn.includes(searchTerm),
    );
  }

  onDragStarted(document: SelectableDocumentEntity) {
    this.draggedDocument = document;
    this.draggedDocument.isDragged = true;
    if (!document.checked) {
      this.draggedDocumentsMetaData = [document];
      this.documentsToShow.forEach((d) => (d.checked = false));
      return;
    }
    const selectedDocuments = this.documentsToShow.filter((d) => d.checked);
    selectedDocuments.forEach((d) => (d.isDragged = true));
    this.draggedDocumentsMetaData = selectedDocuments;
  }

  onSourceListEntered() {
    this.groupHovered = {};
    this.documentsToShow = this.documentsToShow.filter((f) => !f.isPlaceholder);
  }

  handleFile(files: File[] | File) {
    if (!Array.isArray(files)) {
      files = [files];
    }
    this.fluidTable.clearSelection();
    let showedUploadNotification = false;

    const documentsToUpload = files.map((f) => {
      const [documentName] = this.getDocumentNameWithoutExtension(f.name);

      const document: SelectableDocumentEntity = {
        documentName,
        contentReference: {
          fileName: f.name,
        },
        isUploading: true,
      };

      this.documentsToShow = [document, ...this.documentsToShow];
      this.fluidTable.table.recalculateColumns();

      return this.store.select(selectCustomerKey).pipe(
        switchMap((customerKey) => {
          const isZipFile =
            f.name.substring(f.name.lastIndexOf('.')) === '.zip';

          const endpoint = isZipFile
            ? this.tusDocumentUploadController.getUploadInboxZipDocumentPath(
                this.dataRoomOwnerId,
              )
            : this.tusDocumentUploadController.getUploadInboxDocumentPath(
                this.dataRoomOwnerId,
              );

          const typeOfTusUpload = isZipFile
            ? TusUploadType.INBOX_ZIP
            : TusUploadType.INBOX;

          const docType = isZipFile ? 'INBOX_ZIP' : 'INBOX';

          return this.fileManagerService.upload({
            businessCaseId: this.dataRoomOwnerId,
            endpoint,
            typeOfTusUpload,
            docType,
            file: f,
            customerKey,
            uploadId: this.identityService.generateKey(),
          });
        }),
        catchError((error) => {
          const message =
            this.documentUploadHelperService.getHandleFileErrorMsg(
              f,
              error.message,
            );
          this.finToastService.show(Toast.error(message));
          this.fileUploader?.removeAll();
          this.documentsToShow = this.documentsToShow.filter(
            (doc) => !isEqual(doc, document),
          );
          this.cd.markForCheck();
          return of(null);
        }),
      );
    });

    merge(...documentsToUpload)
      .pipe(
        filter((res) => !!res?.document),
        tap((res) => {
          this.store.dispatch(
            StateLibDocumentInboxPageActions.uploadDocuments({
              payload: [res.document],
            }),
          );
          if (!showedUploadNotification) {
            this.finToastService.show(Toast.success(UPLOAD_SUCCESS));
            showedUploadNotification = true;
            this.fileUploader?.removeAll();
          }
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  onRemoveSelectedDocuments(row?: SelectableDocumentEntity) {
    this.finModalService
      .open(ConfirmationModalComponent, {
        data: {
          title: $localize`:@@documentInbox.onRemove.message:Ausgewählte Dateien löschen?`,
          confirmButtonAppearance: FinButtonAppearance.PRIMARY,
          confirmLabel: $localize`:@@userManagement.user.userEditor.delete:Löschen`,
          cancelLabel: $localize`:@@button.label.cancel:Abbrechen`,
          svgIcon: 'svgUserDelete',
          size: this.finSize.L,
          attention: true,
        },
        size: this.finSize.S,
      })
      .afterClosed()
      .pipe(filter(Boolean))
      .subscribe(() => {
        if (row) {
          this.store.dispatch(
            StateLibDocumentInboxPageActions.deleteDocuments({
              payload: [row],
            }),
          );
        } else {
          this.store.dispatch(
            StateLibDocumentInboxPageActions.deleteDocuments({
              payload: this.selectedRows,
            }),
          );
        }
        this.fluidTable.clearSelection();
      });
  }

  onIgnoreWarning(row: TableRow) {
    const document = row as SelectableDocumentEntity;
    document.isWarningIgnored = true;

    this.documentInboxControllerService
      .ignoreDuplicateWarning({
        documentId: document.id,
        businessCaseId: this.dataRoomOwnerId,
      })
      .subscribe({
        next: () => {
          this.store.dispatch(
            StateLibDocumentInboxPageActions.updateDocument({
              payload: document,
            }),
          );
        },
        error: () => {
          this.finToastService.show(Toast.error());
        },
      });
  }

  onOpenPreview(document: TableRow) {
    const documentPreview = document as SelectableDocumentEntity;
    const uploadType = documentPreview.ownerReference.chatId
      ? 'chat'
      : (documentPreview.ownerReference.documentType as string);
    const [firstName, lastName] = documentPreview.userName.split(' ');
    const email = documentPreview.userEmail;
    const user = { firstName, lastName, email };

    if (isUuid(documentPreview.userName)) {
      this.store.dispatch(
        StateLibDocumentPageActions.openPdfViewer({
          documentId: documentPreview.id,
          user,
          uploadType,
        }),
      );
    } else {
      this.customerManagementControllerService
        .getCustomerByKey({
          customerKey: documentPreview?.customerKey,
        })
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe({
          next: (res) => {
            const customerName = res.name;
            this.store.dispatch(
              StateLibDocumentPageActions.openPdfViewer({
                documentId: documentPreview.id,
                user,
                uploadType,
                customerName,
              }),
            );
          },
          error: () => {
            this.store.dispatch(
              StateLibDocumentPageActions.openPdfViewer({
                documentId: documentPreview.id,
                user,
                uploadType,
              }),
            );
          },
        });
    }
  }

  documentsDroppedIntoGroup(
    event: CdkDragDrop<unknown>,
    group: BusinessCaseGroup | CadrGroup,
    groupIndex: number,
  ) {
    this.groupHovered = {};
    this.groupDroppedTo = groupIndex;
    const draggedDocuments = event.item.data as SelectableDocumentEntity[];

    this.documentsDroppedIntoGroupOutput.emit({ draggedDocuments, group });
  }

  dropListHover(hovered: boolean, groupIndex: number) {
    this.groupHovered[groupIndex] = hovered;
  }

  private getDocumentNameWithoutExtension(fileName: string) {
    const documentNameParts = fileName.split('.');
    const fileExtension =
      documentNameParts.length > 1 ? documentNameParts.pop() : '';

    if (!FILE_EXTENSIONS_ARRAY.includes(fileExtension)) {
      return [fileName];
    }

    return [documentNameParts.join('.'), fileExtension];
  }

  onRowsSelected(rows?: TableRow[]) {
    this.selectedRows = rows as SelectableDocumentEntity[];
  }

  downloadDocument(row: SelectableDocumentEntity) {
    this.fileService.downloadDocument(row.id);
  }

  saveName(newName: string, row: SelectableDocumentEntity) {
    this.onEditRow(row, false);

    if (newName === row.documentName) {
      return;
    }

    this.store.dispatch(
      StateLibDocumentInboxPageActions.renameDocument({
        payload: { newName: newName, docId: row.id },
      }),
    );
  }

  onEditRow(row: SelectableDocumentEntity, condition: boolean) {
    const newRow = row;
    newRow.editable = condition;
    this.store.dispatch(
      StateLibDocumentInboxPageActions.updateDocument({ payload: newRow }),
    );
  }

  getRowClass = (row: TableRow) => {
    return {
      'row-warning':
        row.hasError && !row.isWarningIgnored && !row.isDraggingRow,
      'tw-text-color-text-primary': true,
    };
  };
}
