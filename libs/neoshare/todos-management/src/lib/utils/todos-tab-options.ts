import { CountByPerspectivesResponse } from '@fincloud/swagger-generator/internal-tools';
import { TodosApiPerspective, TodosType } from '@fincloud/types/enums';

export const TODOS_TABS_OPTIONS = [
  {
    label: $localize`:@@todosManagement.myTasks:<PERSON><PERSON>,
    name: TodosApiPerspective.MY_TASKS,
    urlName: TodosType.MY_TASKS,
    countKey: 'myTasksCount' as keyof CountByPerspectivesResponse,
  },
  {
    label: $localize`:@@todosManagement.delegated:Delegiert`,
    name: TodosApiPerspective.DELEGATED,
    urlName: TodosType.DELEGATED,
    countKey: 'delegatedTasksCount' as keyof CountByPerspectivesResponse,
  },
];
