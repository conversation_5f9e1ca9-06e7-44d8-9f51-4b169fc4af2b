import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinContainerModule } from '@fincloud/ui/container';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinLoaderModule } from '@fincloud/ui/loader';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { TodosManagementBadgeComponent } from './components/todos-management-badge/todos-management-badge.component';
import { TodosManagementSummaryDescriptionComponent } from './components/todos-management-summary-description/todos-management-summary-description.component';
import { TodosManagementSummaryComponent } from './components/todos-management-summary/todos-management-summary.component';

@NgModule({
  imports: [
    CommonModule,
    NsUiIconsModule,
    FinBadgesModule,
    FinButtonModule,
    RouterModule,
    FinSeparatorsModule,
    FinTabsModule,
    FinTruncateTextModule,
    FinContainerModule,
    OverlayModule,
    FinIconModule,
    FinActionsMenuModule,
    FinLoaderModule,
    FinScrollbarModule,
    NsCorePipesModule,
  ],
  declarations: [
    TodosManagementBadgeComponent,
    TodosManagementSummaryComponent,
    TodosManagementSummaryDescriptionComponent,
  ],
  exports: [TodosManagementBadgeComponent, TodosManagementSummaryComponent],
})
export class TodosManagementModule {}
