import { ChangeDetectionStrategy, Component } from '@angular/core';
import { Router } from '@angular/router';
import { selectCustomerKey } from '@fincloud/state/customer';
import {
  StateLibTodosManagementPageActions,
  todosManagementBadgeFeature,
} from '@fincloud/state/todos-management';
import { UserAssignment } from '@fincloud/swagger-generator/internal-tools';
import {
  TodosApiPerspective,
  TodosApiStatus,
  TodosType,
} from '@fincloud/types/enums';
import { FinBadgeType } from '@fincloud/ui/badges';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinSeparator } from '@fincloud/ui/separators';
import { FinTabType } from '@fincloud/ui/tabs';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { shareReplay } from 'rxjs';
import { todoStatusMap } from '../../utils/todo-status-map';
import { TODOS_TABS_OPTIONS } from '../../utils/todos-tab-options';

@Component({
  selector: 'app-todos-management-summary',
  templateUrl: './todos-management-summary.component.html',
  styleUrl: './todos-management-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TodosManagementSummaryComponent {
  size = FinSize;
  shape = FinButtonShape;
  appearance = FinButtonAppearance;
  separatorTypes = FinSeparator;
  tabType = FinTabType;
  badgeType = FinBadgeType;
  todoType = TodosApiPerspective;
  tabs = TODOS_TABS_OPTIONS;

  selectedSummaryTab$ = this.store
    .select(todosManagementBadgeFeature.selectSelectedSummaryTab)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  todosBadgeCount$ = this.store.select(
    todosManagementBadgeFeature.selectCounts,
  );

  customerKey$ = this.store.select(selectCustomerKey);

  todos$ = this.store
    .select(todosManagementBadgeFeature.selectTodos)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  showSummaryEmptyView$ = this.store.select(
    todosManagementBadgeFeature.selectSummaryShowEmptyView,
  );

  showSummaryLoadingView$ = this.store.select(
    todosManagementBadgeFeature.selectSummaryShowLoadingView,
  );

  isTodosLoading$ = this.store.select(
    todosManagementBadgeFeature.selectIsSummaryLoading,
  );

  private perspective = TodosApiPerspective.MY_TASKS;
  private perspectiveUrlName = TodosType.MY_TASKS;

  constructor(
    private store: Store,
    private router: Router,
  ) {}

  tabChange(tabIndex: number): void {
    this.perspective = TODOS_TABS_OPTIONS[tabIndex].name;
    this.perspectiveUrlName = TODOS_TABS_OPTIONS[tabIndex].urlName;

    this.store.dispatch(
      StateLibTodosManagementPageActions.changeSummaryTab({
        perspective: this.perspective,
      }),
    );
  }

  loadMoreTodos(): void {
    this.store.dispatch(StateLibTodosManagementPageActions.loadTodosSummary());
  }

  navigateToTodos(customerKey: string): void {
    this.router.navigate(['/', customerKey, 'todos-management']);
    this.store.dispatch(StateLibTodosManagementPageActions.closeTodosSummary());
  }

  redirectByTodoType(todo: UserAssignment): void {
    this.store.dispatch(
      StateLibTodosManagementPageActions.redirectToTodo({
        status: todoStatusMap(todo.status as TodosApiStatus),
        todoId: todo.id,
        businessCaseId: todo.businessCaseId,
        perspective: this.perspectiveUrlName,
        highlight: false,
      }),
    );
  }
}
