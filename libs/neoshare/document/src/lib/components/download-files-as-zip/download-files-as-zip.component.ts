import { Component, DestroyRef, Input, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { InformationUtils } from '@fincloud/core/business-case';
import { DocumentBusinessCaseControllerService as DocumentBusinessCaseControllerServiceOverride } from '@fincloud/core/document';
import { FileService } from '@fincloud/core/files';
import { SocketService } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import { selectBusinessCase } from '@fincloud/state/business-case';
import {
  selectCompany,
  selectCompanyDataRoomInformation,
} from '@fincloud/state/company-analysis';
import { Company, Information } from '@fincloud/swagger-generator/company';
import { DocumentCompanyControllerService } from '@fincloud/swagger-generator/document';
import {
  BusinessCaseInformation,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import { BusinessCasePermission } from '@fincloud/types/enums';
import { DownloadResponseNotification } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { Store } from '@ngrx/store';
import { NgxPermissionsService } from 'ngx-permissions';
import { catchError, combineLatest, map, of, switchMap, take, tap } from 'rxjs';

@Component({
  selector: 'app-download-files-as-zip',
  templateUrl: './download-files-as-zip.component.html',
  styleUrls: ['./download-files-as-zip.component.scss'],
})
export class DownloadFilesAsZipComponent implements OnInit {
  @Input() page: 'business-case' | 'company';
  @Input() customerKey: string;
  @Input() isCADR: boolean;

  businessCase: ExchangeBusinessCase;
  showDownload: boolean;
  companyId: string;
  company: Company;

  constructor(
    private destroyRef: DestroyRef,
    private documentService: DocumentBusinessCaseControllerServiceOverride,
    private documentCompanyService: DocumentCompanyControllerService,
    private fileService: FileService,
    private store: Store,
    private finToastService: FinToastService,
    private ngxPermissionsService: NgxPermissionsService,
    private socketService: SocketService,
  ) {}

  ngOnInit() {
    if (this.page === 'business-case') {
      this.store
        .select(selectBusinessCase)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((businessCase) => {
          this.businessCase = businessCase;
          if (businessCase) {
            if (!this.isCADR) {
              const hasDocuments = this.getHasDocuments(
                Object.values(this.businessCase.information || {}),
              );
              const hasPublicDocuments = this.getHasDocuments(
                Object.values(this.businessCase.information || {}),
                true, // should we check for lead ? he can see private wtf
              );
              this.showDownload =
                this.checkBusinessCasePermissionForDocumentExport()
                  ? hasDocuments
                  : hasPublicDocuments;
            } else {
              this.companyId = this.businessCase.company.id;
              this.showDownload = this.getHasDocuments(
                Object.values(this.businessCase.company.information || {}),
              );
            }
          }
        });
    }

    if (this.page === 'company') {
      combineLatest([
        this.store.select(selectCompany),
        this.store.select(selectCompanyDataRoomInformation),
      ])
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(([company, information]) => {
          this.company = company;
          this.companyId = company?.id;
          if (company) {
            this.showDownload = this.getHasDocuments(
              Object.values(information || {}),
            );
          }
        });
    }
  }

  download() {
    this.finToastService.show(
      Toast.info(
        $localize`:@@downloadFilesAsZip.toast.info:Dokumentenexport gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.`,
      ),
    );

    const docReq = this.company
      ? this.documentCompanyService.getAllCompanyDocumentsLink({
          companyId: this.companyId,
        })
      : this.documentService.getAllBusinessCaseDocumentsDownloadLink({
          businessCaseId: this.businessCase.id,
        });

    docReq
      .pipe(
        switchMap(() =>
          this.socketService.receiveMessages$.pipe(
            map((msg) => msg as DownloadResponseNotification),
            tap(({ source, link, hasFailed }) => {
              if (hasFailed) {
                this.finToastService.show(Toast.error());
                return;
              }

              const zipName =
                source === 'CADR'
                  ? $localize`:@@downloadFilesAsZip.fileName.company:Archiv-company-${this.company.companyInfo.legalName}.zip`
                  : $localize`:@@downloadFilesAsZip.fileName.case:Archiv-Fall-${this.businessCase.autoGeneratedBusinessCaseName}.zip`;

              this.fileService.downloadUrl(link, zipName);
            }),
            take(1),
          ),
        ),
        catchError((e) => {
          this.finToastService.show(Toast.error());
          return of(e);
        }),
      )
      .subscribe();
  }

  private getHasDocuments(
    information: (BusinessCaseInformation | Information)[],
    checkForPublicDocs = false,
  ) {
    return information
      .filter((info) => info.field.fieldType === 'DOCUMENT')
      .some((info) => {
        const isEmptyValue = InformationUtils.isEmptyValue(info?.value);
        const informationGroup =
          info &&
          this.businessCase?.businessCaseTemplate?.template.groupsOrdered.find(
            (g) => g.fields.includes(info.field?.key),
          );
        const hasParticipantAccessToGroup =
          informationGroup?.groupVisibility?.visibility === 'PUBLIC' ||
          informationGroup?.groupVisibility?.currentParticipantsWithVisibility?.includes(
            this.customerKey,
          );

        return (
          !isEmptyValue &&
          (checkForPublicDocs ? hasParticipantAccessToGroup : true)
        );
      });
  }

  checkBusinessCasePermissionForDocumentExport() {
    const permissionsToCheck = [BusinessCasePermission.BCP_00014];
    return permissionsToCheck.some(
      (permission) => !!this.ngxPermissionsService.getPermission(permission),
    );
  }
}
