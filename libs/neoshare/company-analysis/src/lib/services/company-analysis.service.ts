import { Injectable } from '@angular/core';
import { selectUserId } from '@fincloud/state/user';
import {
  BusinessCaseControllerService,
  CaseContextInformationControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import { ExchangeService } from '@fincloud/swagger-generator/exchange';
import { BusinessCasePermission } from '@fincloud/types/enums';
import { AppState, BusinessCaseCompanyAnalysis } from '@fincloud/types/models';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { flatten, keyBy } from 'lodash-es';
import { Observable, catchError, forkJoin, map, of, switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CompanyAnalysisService {
  constructor(
    private store: Store<AppState>,
    private businessCaseControllerService: BusinessCaseControllerService,
    private exchangeControllerService: ExchangeService,
    private businessCaseContextService: CaseContextInformationControllerService,
  ) {}

  loadCompanyBusinessCases(
    companyId: string,
    cadrLink = false,
  ): Observable<BusinessCaseCompanyAnalysis[]> {
    return this.businessCaseControllerService
      .getOtherBusinessCasesForCompany({
        companyId,
      })
      .pipe(
        switchMap((caseIds) => {
          if (caseIds.length === 0) {
            return of([]);
          }

          return forkJoin(
            caseIds.map((businessCaseId) => {
              return this.businessCaseContextService.getContextInformation({
                businessCaseId,
              });
            }),
          ).pipe(
            switchMap((businessCaseContexts) => {
              const accessToCasesContexts = businessCaseContexts.filter(
                (c) => !!c,
              );

              if (accessToCasesContexts.length === 0) {
                return of([]);
              }

              const contextDictionary = keyBy(
                accessToCasesContexts,
                'businessCaseId',
              );

              return forkJoin(
                accessToCasesContexts.map((accessBusinessCaseContext) =>
                  this.exchangeControllerService
                    .exchangeControllerFindById({
                      id: accessBusinessCaseContext.businessCaseId,
                      requiresPlatformManagerOrCurrentUserCheck: true,
                    })
                    .pipe(
                      catchError(() => {
                        return of(null);
                      }),
                    ),
                ),
              ).pipe(
                map((businessCases) => businessCases.filter(Boolean)),
                concatLatestFrom(() => this.store.select(selectUserId)),
                map(([businessCases, loggedInUserId]) => {
                  return businessCases.map((bCase) => {
                    return cadrLink
                      ? ({
                          canManageCadrLink:
                            contextDictionary[bCase.id]?.permissions?.includes(
                              BusinessCasePermission.BCP_00059,
                            ) &&
                            flatten(
                              bCase.participants.map((p) =>
                                p.users.map((u) => u.userId),
                              ),
                            ).some((userId) => userId === loggedInUserId),
                          ...bCase,
                        } as BusinessCaseCompanyAnalysis)
                      : bCase;
                  });
                }),
              );
            }),
          );
        }),
      );
  }
}
