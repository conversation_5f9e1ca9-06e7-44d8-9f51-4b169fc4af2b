{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx", "@fincloud/ns", "check-file"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.base.json"]}, "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "lib:neoshare", "onlyDependOnLibsWithTags": ["lib:components", "lib:state", "lib:core", "lib:types", "lib:utils", "lib:swagger-generator"]}, {"sourceTag": "lib:components", "onlyDependOnLibsWithTags": ["lib:state", "lib:core", "lib:types", "lib:utils", "lib:swagger-generator"]}, {"sourceTag": "lib:state", "onlyDependOnLibsWithTags": ["lib:core", "lib:types", "lib:utils", "lib:swagger-generator"]}, {"sourceTag": "lib:core-state", "onlyDependOnLibsWithTags": ["lib:core", "lib:types", "lib:swagger-generator"]}, {"sourceTag": "lib:core", "onlyDependOnLibsWithTags": ["lib:types", "lib:core-state", "lib:utils", "lib:swagger-generator"]}, {"sourceTag": "lib:types", "onlyDependOnLibsWithTags": ["lib:swagger-generator"]}, {"sourceTag": "lib:utils", "onlyDependOnLibsWithTags": ["lib:swagger-generator", "lib:types"]}, {"sourceTag": "app:fincloud", "onlyDependOnLibsWithTags": ["lib:state", "lib:core", "lib:types", "lib:swagger-generator", "lib:components", "lib:utils", "lib:neoshare"]}]}], "multiline-ternary": ["warn", "never"], "no-nested-ternary": "warn", "@typescript-eslint/naming-convention": ["error", {"selector": "enum", "format": ["PascalCase"]}, {"selector": "enumMember", "format": ["UPPER_CASE"]}], "no-restricted-syntax": ["error", {"selector": "Decorator[expression.callee.name='<PERSON><PERSON><PERSON><PERSON>']", "message": "The @UntilDestroy decorator is not allowed, use RxJS pipe takeUntilDestroy instead."}], "@fincloud/ns/no-missing-subject-suffix": "error", "@fincloud/ns/no-class-based-guards": "error", "check-file/folder-match-with-fex": ["error", {"**/*.directive.ts": "**/directives/", "**/*.pipe.ts": "**/pipes/", "**/*.service.ts": "**/services/", "**/*.guard.ts": "**/guards/", "**/*.interceptor.ts": "**/interceptors/", "**/*.actions.ts": "**/actions/", "**/*.reducer.ts": "**/reducers/", "**/*.effects.ts": "**/effects/", "**/*.selectors.ts": "**/selectors/", "**/*.validator.ts": "**/validators/", "**/*.resolver.ts": "**/resolvers/"}]}}, {"files": ["*.ts"], "extends": ["plugin:@nx/typescript", "plugin:@ngrx/recommended"], "rules": {"no-restricted-syntax": ["warn", {"selector": "MethodDefinition[kind='get']:not([value.returnType])", "message": "Getter must declare an explicit return type"}]}}, {"files": ["*.actions.ts"], "rules": {"@fincloud/ns/no-ngrx-actions-missing-format": "error", "@typescript-eslint/naming-convention": ["error", {"selector": "variable", "modifiers": ["const", "exported"], "types": ["function"], "format": ["camelCase"]}]}}, {"files": ["**/utils/*.ts", "**/utils/**/*.ts"], "rules": {"@typescript-eslint/naming-convention": ["error", {"selector": "variable", "modifiers": ["const", "exported"], "format": ["UPPER_CASE"]}, {"selector": "variable", "modifiers": ["const", "exported"], "types": ["function"], "format": ["camelCase"]}]}}, {"files": ["*.ts"], "excludedFiles": ["**/models/*.ts", "**/models/**/*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-export-interface": "error", "@fincloud/ns/no-declare-interface": "error", "@fincloud/ns/no-export-type": "error", "@fincloud/ns/no-declare-type": "error"}}, {"files": ["*.ts"], "excludedFiles": ["**/enums/*.ts", "**/enums/**/*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-export-enum": "error", "@fincloud/ns/no-declare-enum": "error"}}, {"files": ["*.ts"], "excludedFiles": ["**/*.actions.ts", "**/*.module.ts", "**/*.reducer.ts", "**/*.reducers.ts", "**/*.selectors.ts", "**/actions/*.ts", "**/app.config.ts", "**/app.routes.ts", "**/environments/*.ts", "**/guards/*.ts", "**/index.ts", "**/locales/*.ts", "**/module-federation.config.ts", "**/reducers/*.ts", "**/selectors/*.ts", "**/utils/*.ts", "**/utils/**/*.ts", "**/validators/*.ts"], "extends": ["plugin:@nx/typescript"], "rules": {"@fincloud/ns/no-export-constant": "error", "@fincloud/ns/no-declare-constant": "error"}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {}}, {"files": ["*.spec.ts", "*.spec.tsx", "*.spec.js", "*.spec.jsx"], "env": {"jest": true}, "rules": {}}]}